Feature: Dapr Helpers

  <PERSON><PERSON><PERSON>: Publish a message to topic
    When I publish message "Hello World" to topic "mytopic"
    Then the message should be published to topic "mytopic"

  Scenario: Save and get state
    When I save state key "myKey" with value "myValue"
    Then I get state key "myKey" and it should be "myValue"
    
  Scenario: Set state with complex data
    When I set state with complex data
      | Key      | Value     |
      | key1     | value1    |
      | key2     | value2    |
      | key3     | value3    |
    
  Scenario: Service invocation
    When I invoke service "calculator" method "add" with arguments
      """
      {"a": 5, "b": 3}
      """
    Then the service response should contain "8"
    
  Scenario: Distributed lock operations
    When I acquire distributed lock "resource1" with timeout "5000"
    Then the lock should be acquired
    When I release distributed lock "resource1"
    Then the lock should be released
    
  Sc<PERSON>rio: Wait for object to appear
    When I save state key "waitKey" with value "waitValue"
    And I wait for object "waitKey" to arrive with timeout "2000"
    Then the object should have arrived
    
  Scenario: ODRL policy and permissions
    Given I set ODRL policy with name "policy1" and content
      """
      {
        "permission": "use",
        "target": "service1",
        "assignee": "service2"
      }
      """
    When I apply permission "read" to service "service1"
    Then service "service1" should have permission "read"