package com.example.dapr;

import io.cucumber.java.Before;
import io.cucumber.java.en.*;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class DaprSteps {

    private static final DaprHelper daprHelper = new DaprHelper();
    private static final Map<String, Object> scenarioContext = new HashMap<>();
    private final ObjectMapper objectMapper = new ObjectMapper();

    // Pub/Sub Steps
    @When("I publish message {string} to topic {string}")
    public void i_publish_message_to_topic(String message, String topic) throws Exception {
        daprHelper.publishMessage(topic, message);
        scenarioContext.put("LastPublishedMessage", message);
        scenarioContext.put("LastPublishedTopic", topic);
    }

    @When("I publish event to topic {string} with data")
    public void i_publish_event_to_topic_with_data(String topic, String dataTable) throws Exception {
        // Assuming the data table is provided as a JSON string.
        Object data = objectMapper.readValue(dataTable, Object.class);
        daprHelper.publishEvent(topic, data);
        scenarioContext.put("LastPublishedData", data);
        scenarioContext.put("LastPublishedTopic", topic);
    }

    @Then("the message should be published to topic {string}")
    public void the_message_should_be_published_to_topic(String topic) {
        String lastTopic = (String) scenarioContext.get("LastPublishedTopic");
        if (lastTopic == null || !lastTopic.equals(topic)) {
            throw new RuntimeException("Expected topic '" + topic + "' but got '" + lastTopic + "'");
        }
    }

    // State Management Steps
    @When("I save state key {string} with value {string}")
    public void i_save_state_key_with_value(String key, String value) throws Exception {
        daprHelper.saveState(key, value);
        scenarioContext.put("LastStateKey", key);
        scenarioContext.put("LastStateValue", value);
    }

    @When("I set state with complex data")
    public void i_set_state_with_complex_data(String dataTable) throws Exception {
        // Assuming dataTable is a JSON string representing a map.
        Map<String, String> states = objectMapper.readValue(dataTable, Map.class);
        daprHelper.saveBulkState(states);
        scenarioContext.put("LastBulkStates", states);
    }

    @Then("I get state key {string} and it should be {string}")
    public void i_get_state_key_and_it_should_be(String key, String expectedValue) throws Exception {
        String value = daprHelper.getState(key);
        scenarioContext.put("RetrievedStateKey", key);
        scenarioContext.put("RetrievedStateValue", value);
        if (!expectedValue.equals(value)) {
            throw new RuntimeException("Expected '" + expectedValue + "' but got '" + value + "'");
        }
    }

    // Service Invocation Steps
    @When("I invoke service {string} method {string} with arguments")
    public void i_invoke_service_method_with_arguments(String serviceName, String methodName, String arguments) throws Exception {
        Object args = objectMapper.readValue(arguments, Object.class);
        String response = daprHelper.invokeService(serviceName, methodName, args);
        scenarioContext.put("LastInvokedService", serviceName);
        scenarioContext.put("LastInvokedMethod", methodName);
        scenarioContext.put("LastInvokedArguments", args);
        scenarioContext.put("LastServiceResponse", response);
    }

    @Then("the service response should contain {string}")
    public void the_service_response_should_contain(String expectedValue) {
        String response = (String) scenarioContext.get("LastServiceResponse");
        if (response == null || !response.contains(expectedValue)) {
            throw new RuntimeException("Expected response to contain '" + expectedValue + "' but got '" + response + "'");
        }
    }

    // Distributed Lock Steps
    @When("I acquire distributed lock {string} with timeout {string}")
    public void i_acquire_distributed_lock_with_timeout(String lockName, String timeout) throws Exception {
        int timeoutMs = Integer.parseInt(timeout);
        String lockOwner = UUID.randomUUID().toString();
        boolean success = daprHelper.acquireLock(lockName, lockOwner, timeoutMs);
        scenarioContext.put("LockName", lockName);
        scenarioContext.put("LockOwner", lockOwner);
        scenarioContext.put("LockAcquired", success);
        if (!success) {
            throw new RuntimeException("Lock was not acquired");
        }
    }

    @When("I release distributed lock {string}")
    public void i_release_distributed_lock(String lockName) throws Exception {
        String lockOwner = (String) scenarioContext.get("LockOwner");
        if (lockOwner == null) {
            throw new RuntimeException("No lock owner stored in context");
        }
        boolean success = daprHelper.releaseLock(lockName, lockOwner);
        scenarioContext.put("LockReleased", success);
        if (!success) {
            throw new RuntimeException("Lock was not released");
        }
    }

    @Then("the lock should be acquired")
    public void the_lock_should_be_acquired() {
        Boolean acquired = (Boolean) scenarioContext.get("LockAcquired");
        if (acquired == null || !acquired) {
            throw new RuntimeException("Lock was not acquired");
        }
    }

    @Then("the lock should be released")
    public void the_lock_should_be_released() {
        Boolean released = (Boolean) scenarioContext.get("LockReleased");
        if (released == null || !released) {
            throw new RuntimeException("Lock was not released");
        }
    }

    // Wait for Object Steps
    @When("I wait for object {string} to arrive with timeout {string}")
    public void i_wait_for_object_to_arrive_with_timeout(String objectId, String timeout) throws Exception {
        int timeoutMs = Integer.parseInt(timeout);
        String result = daprHelper.waitForObject(objectId, timeoutMs);
        scenarioContext.put("ObjectArrived", !result.isEmpty());
        scenarioContext.put("ObjectValue", result);
        if (result.isEmpty()) {
            throw new RuntimeException("Object did not arrive");
        }
    }

    @Then("the object should have arrived")
    public void the_object_should_have_arrived() {
        Boolean arrived = (Boolean) scenarioContext.get("ObjectArrived");
        if (arrived == null || !arrived) {
            throw new RuntimeException("Object did not arrive");
        }
    }

    // ODRL Policy Steps
    @When("I set ODRL policy with name {string} and content")
    public void i_set_odrl_policy_with_name_and_content(String policyName, String policyContent) throws Exception {
        Object policy = objectMapper.readValue(policyContent, Object.class);
        daprHelper.setODRLPolicy(policyName, policy);
        scenarioContext.put("LastPolicyName", policyName);
        scenarioContext.put("LastPolicyContent", policy);
    }

    @When("I apply permission {string} to service {string}")
    public void i_apply_permission_to_service(String permission, String serviceName) throws Exception {
        daprHelper.applyPermission(serviceName, permission);
        scenarioContext.put("LastPermission", permission);
        scenarioContext.put("LastPermissionService", serviceName);
    }

    @Then("service {string} should have permission {string}")
    public void service_should_have_permission(String serviceName, String permission) throws Exception {
        boolean hasPermission = daprHelper.checkPermission(serviceName, permission);
        if (!hasPermission) {
            throw new RuntimeException("Service '" + serviceName + "' does not have permission '" + permission + "'");
        }
    }

    @Before
    public void clearScenarioContext() {
        scenarioContext.clear();
    }
}
