package com.example.dapr;

import io.dapr.client.DaprClient;
import io.dapr.client.DaprClientBuilder;
import io.dapr.client.domain.HttpExtension;
import io.dapr.client.domain.State;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class DaprHelper {

    private final DaprClient client;
    private final String pubsubName;
    private final String stateStore;
    private final String lockStore;
    private final ObjectMapper objectMapper;

    public DaprHelper() {
        this.client = new DaprClientBuilder().build();
        this.pubsubName = System.getenv("DAPR_PUBSUB_NAME") != null ? System.getenv("DAPR_PUBSUB_NAME") : "pubsub";
        this.stateStore = System.getenv("DAPR_STATE_STORE") != null ? System.getenv("DAPR_STATE_STORE") : "statestore";
        this.lockStore = System.getenv("DAPR_LOCK_STORE") != null ? System.getenv("DAPR_LOCK_STORE") : "lockstore";
        this.objectMapper = new ObjectMapper();
    }

    // Pub/Sub Operations
    public void publishMessage(String topic, String message) throws Exception {
        client.publishEvent(pubsubName, topic, message.getBytes(StandardCharsets.UTF_8)).block();
    }

    public void publishEvent(String topic, Object data) throws Exception {
        String json = objectMapper.writeValueAsString(data);
        client.publishEvent(pubsubName, topic, json.getBytes(StandardCharsets.UTF_8)).block();
    }

    // State Management
    public void saveState(String key, String value) throws Exception {
        client.saveState(stateStore, key, value.getBytes(StandardCharsets.UTF_8)).block();
    }

    public void saveBulkState(Map<String, String> states) throws Exception {
        List<State<byte[]>> stateList = new ArrayList<>();
        for (Map.Entry<String, String> entry : states.entrySet()) {
            State<byte[]> state = new State<>(entry.getKey(), entry.getValue().getBytes(StandardCharsets.UTF_8));
            stateList.add(state);
        }
        client.saveBulkState(stateStore, stateList).block();
    }

    public String getState(String key) throws Exception {
        byte[] result = client.getState(stateStore, key, byte[].class).block().getValue();
        return result != null ? new String(result, StandardCharsets.UTF_8) : "";
    }

    // Service Invocation
    public String invokeService(String serviceName, String methodName, Object args) throws Exception {
        String json = objectMapper.writeValueAsString(args);
        byte[] response = client.invokeMethod(serviceName, methodName, json.getBytes(StandardCharsets.UTF_8), HttpExtension.POST)
                                  .block().getBody();
        return response != null ? new String(response, StandardCharsets.UTF_8) : "";
    }

    // Distributed Lock (Simplified Implementation)
    public boolean acquireLock(String resourceId, String lockOwner, int timeoutMs) throws Exception {
        String lockKey = "lock-" + resourceId;
        try {
            String state = getState(lockKey);
            if (state != null && !state.isEmpty()) {
                Map<String, Object> lockData = objectMapper.readValue(state, Map.class);
                Number expiryTime = (Number) lockData.get("expiryTime");
                long currentTime = System.nanoTime();
                if (currentTime < expiryTime.longValue()) {
                    return false;
                }
            }
        } catch (Exception e) {
            // In case of an error, assume the lock is available.
        }
        long expiryTime = System.nanoTime() + TimeUnit.MILLISECONDS.toNanos(timeoutMs);
        Map<String, Object> lockData = Map.of("owner", lockOwner, "expiryTime", expiryTime);
        saveState(lockKey, objectMapper.writeValueAsString(lockData));
        return true;
    }

    public boolean releaseLock(String resourceId, String lockOwner) throws Exception {
        String lockKey = "lock-" + resourceId;
        try {
            String state = getState(lockKey);
            if (state != null && !state.isEmpty()) {
                Map<String, Object> lockData = objectMapper.readValue(state, Map.class);
                if (lockData.get("owner").equals(lockOwner)) {
                    client.deleteState(stateStore, lockKey).block();
                    return true;
                }
            }
        } catch (Exception e) {
            // Handle or log exception if needed.
        }
        return false;
    }

    // Wait for Object
    public String waitForObject(String key, int timeoutMs) throws Exception {
        long startTime = System.currentTimeMillis();
        while ((System.currentTimeMillis() - startTime) < timeoutMs) {
            String state = getState(key);
            if (state != null && !state.isEmpty()) {
                return state;
            }
            Thread.sleep(500);
        }
        return "";
    }

    // ODRL Policies
    public void setODRLPolicy(String policyName, Object policy) throws Exception {
        String policyKey = "odrl-policy-" + policyName;
        String json = objectMapper.writeValueAsString(policy);
        saveState(policyKey, json);
    }

    public void applyPermission(String serviceName, String permission) throws Exception {
        String permissionKey = "permission-" + serviceName + "-" + permission;
        saveState(permissionKey, "true");
    }

    public boolean checkPermission(String serviceName, String permission) throws Exception {
        String permissionKey = "permission-" + serviceName + "-" + permission;
        String state = getState(permissionKey);
        return "true".equals(state);
    }
}
