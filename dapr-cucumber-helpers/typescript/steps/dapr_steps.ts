import { Given, When, Then, Before } from '@cucumber/cucumber';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../helpers/dapr_helper';
import { v4 as uuidv4 } from 'uuid';

// Initialize Dapr helper and scenario context.
const daprHelper = new DaprHelper();
const scenarioContext: { [key: string]: any } = {};

// Pub/Sub Steps
When(/^I publish message "([^"]*)" to topic "([^"]*)"$/, async function (message: string, topic: string) {
  try {
    await daprHelper.publishMessage(topic, message);
    scenarioContext["LastPublishedMessage"] = message;
    scenarioContext["LastPublishedTopic"] = topic;
  } catch (error) {
    throw new Error(`Failed to publish message: ${error}`);
  }
});

When(/^I publish event to topic "([^"]*)" with data$/, async function (topic: string, docString: string) {
  let data: Record<string, unknown>;
  try {
    data = JSON.parse(docString);
  } catch (error) {
    throw new Error(`Failed to parse event data: ${error}`);
  }
  try {
    await daprHelper.publishEvent(topic, data);
    scenarioContext["LastPublishedData"] = data;
    scenarioContext["LastPublishedTopic"] = topic;
  } catch (error) {
    throw new Error(`Failed to publish event: ${error}`);
  }
});

Then(/^the message should be published to topic "([^"]*)"$/, async function (topic: string) {
  const lastTopic = scenarioContext["LastPublishedTopic"];
  if (!lastTopic) {
    throw new Error("No topic was published");
  }
  if (lastTopic !== topic) {
    throw new Error(`Expected topic '${topic}' but got '${lastTopic}'`);
  }
});

// State Management Steps
When(/^I save state key "([^"]*)" with value "([^"]*)"$/, async function (key: string, value: string) {
  try {
    await daprHelper.saveState(key, value);
    scenarioContext["LastStateKey"] = key;
    scenarioContext["LastStateValue"] = value;
  } catch (error) {
    throw new Error(`Failed to save state: ${error}`);
  }
});

When(/^I set state with complex data$/, async function (docString: string) {
  let states: { [key: string]: string };
  try {
    states = JSON.parse(docString);
  } catch (error) {
    throw new Error(`Failed to parse complex state data: ${error}`);
  }
  try {
    await daprHelper.saveBulkState(states);
    scenarioContext["LastBulkStates"] = states;
  } catch (error) {
    throw new Error(`Failed to save bulk state: ${error}`);
  }
});

Then(/^I get state key "([^"]*)" and it should be "([^"]*)"$/, async function (key: string, expectedValue: string) {
  try {
    const value = await daprHelper.getState(key);
    scenarioContext["RetrievedStateKey"] = key;
    scenarioContext["RetrievedStateValue"] = value;
    if (value !== expectedValue) {
      throw new Error(`Expected '${expectedValue}' but got '${value}'`);
    }
  } catch (error) {
    throw new Error(`Failed to get state: ${error}`);
  }
});

// Service Invocation Steps
When(/^I invoke service "([^"]*)" method "([^"]*)" with arguments$/, async function (serviceName: string, methodName: string, docString: string) {
  let args: Record<string, unknown>;
  try {
    args = JSON.parse(docString);
  } catch (error) {
    throw new Error(`Failed to parse service invocation arguments: ${error}`);
  }
  try {
    const response = await daprHelper.invokeService(serviceName, methodName, args);
    scenarioContext["LastInvokedService"] = serviceName;
    scenarioContext["LastInvokedMethod"] = methodName;
    scenarioContext["LastInvokedArguments"] = args;
    scenarioContext["LastServiceResponse"] = response;
  } catch (error) {
    throw new Error(`Failed to invoke service: ${error}`);
  }
});

Then(/^the service response should contain "([^"]*)"$/, async function (expectedValue: string) {
  const response = scenarioContext["LastServiceResponse"] || "";
  if (!response.includes(expectedValue)) {
    throw new Error(`Expected response to contain '${expectedValue}' but got '${response}'`);
  }
});

// Distributed Lock Steps
When(/^I acquire distributed lock "([^"]*)" with timeout "([^"]*)"$/, async function (lockName: string, timeout: string) {
  const timeoutMs = parseInt(timeout, 10);
  if (isNaN(timeoutMs)) {
    throw new Error(`Invalid timeout value: ${timeout}`);
  }
  const lockOwner = uuidv4();
  const success = await daprHelper.acquireLock(lockName, lockOwner, timeoutMs);
  scenarioContext["LockName"] = lockName;
  scenarioContext["LockOwner"] = lockOwner;
  scenarioContext["LockAcquired"] = success;
  if (!success) {
    throw new Error("Lock was not acquired");
  }
});

When(/^I release distributed lock "([^"]*)"$/, async function (lockName: string) {
  const lockOwner = scenarioContext["LockOwner"];
  if (!lockOwner) {
    throw new Error("No lock owner stored in context");
  }
  const success = await daprHelper.releaseLock(lockName, lockOwner);
  scenarioContext["LockReleased"] = success;
  if (!success) {
    throw new Error("Lock was not released");
  }
});

Then(/^the lock should be acquired$/, async function () {
  const acquired = scenarioContext["LockAcquired"];
  if (!acquired) {
    throw new Error("Lock was not acquired");
  }
});

Then(/^the lock should be released$/, async function () {
  const released = scenarioContext["LockReleased"];
  if (!released) {
    throw new Error("Lock was not released");
  }
});

// Wait for Object Steps
When(/^I wait for object "([^"]*)" to arrive with timeout "([^"]*)"$/, async function (objectId: string, timeout: string) {
  const timeoutMs = parseInt(timeout, 10);
  if (isNaN(timeoutMs)) {
    throw new Error(`Invalid timeout value: ${timeout}`);
  }
  const result = await daprHelper.waitForObject(objectId, timeoutMs);
  scenarioContext["ObjectArrived"] = Boolean(result);
  scenarioContext["ObjectValue"] = result;
  if (!result) {
    throw new Error("Object did not arrive");
  }
});

Then(/^the object should have arrived$/, async function () {
  const arrived = scenarioContext["ObjectArrived"];
  if (!arrived) {
    throw new Error("Object did not arrive");
  }
});

// ODRL Policy Steps
When(/^I set ODRL policy with name "([^"]*)" and content$/, async function (policyName: string, docString: string) {
  let policy: Record<string, unknown>;
  try {
    policy = JSON.parse(docString);
  } catch (error) {
    throw new Error(`Failed to parse ODRL policy content: ${error}`);
  }
  try {
    await daprHelper.setODRLPolicy(policyName, policy);
    scenarioContext["LastPolicyName"] = policyName;
    scenarioContext["LastPolicyContent"] = policy;
  } catch (error) {
    throw new Error(`Failed to set ODRL policy: ${error}`);
  }
});

When(/^I apply permission "([^"]*)" to service "([^"]*)"$/, async function (permission: string, serviceName: string) {
  try {
    await daprHelper.applyPermission(serviceName, permission);
    scenarioContext["LastPermission"] = permission;
    scenarioContext["LastPermissionService"] = serviceName;
  } catch (error) {
    throw new Error(`Failed to apply permission: ${error}`);
  }
});

Then(/^service "([^"]*)" should have permission "([^"]*)"$/, async function (serviceName: string, permission: string) {
  try {
    const hasPermission = await daprHelper.checkPermission(serviceName, permission);
    if (!hasPermission) {
      throw new Error(`Service '${serviceName}' does not have permission '${permission}'`);
    }
  } catch (error) {
    throw new Error(`Failed to check permission: ${error}`);
  }
});

// Before each scenario, clear the context.
Before(function () {
  for (const key in scenarioContext) {
    delete scenarioContext[key];
  }
});
