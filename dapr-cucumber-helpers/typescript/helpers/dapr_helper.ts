import { DaprClient, CommunicationProtocolEnum } from '@dapr/dapr';
import { HttpMethod } from '@dapr/dapr/enum/HttpMethod.enum';

export class DaprHelper {
  private client: DaprClient;
  private pubsubName: string;
  private stateStore: string;
  private lockStore: string;

  constructor() {
    // Initialize the Dapr client with default settings (adjust host/port as needed)
    this.client = new DaprClient({ communicationProtocol: CommunicationProtocolEnum.HTTP });
    this.pubsubName = process.env.DAPR_PUBSUB_NAME || 'pubsub';
    this.stateStore = process.env.DAPR_STATE_STORE || 'statestore';
    this.lockStore = process.env.DAPR_LOCK_STORE || 'lockstore';
  }

  // Pub/Sub Operations
  public async publishMessage(topic: string, message: string): Promise<void> {
    await this.client.pubsub.publish(this.pubsubName, topic, Buffer.from(message, 'utf-8'));
  }

  public async publishEvent(topic: string, data: Record<string, unknown>): Promise<void> {
    const dataBytes = Buffer.from(JSON.stringify(data), 'utf-8');
    await this.client.pubsub.publish(this.pubsubName, topic, dataBytes);
  }

  // State Management
  public async saveState(key: string, value: string): Promise<void> {
    await this.client.state.save(this.stateStore, [{ key, value: Buffer.from(value, 'utf-8') }]);
  }

  public async saveBulkState(states: { [key: string]: string }): Promise<void> {
    for (const key in states) {
      await this.saveState(key, states[key]);
    }
  }

  public async getState(key: string): Promise<string> {
    // Cast response to any to access "value" property.
    const result: any = await this.client.state.get(this.stateStore, key);
    return result && result.value ? result.value.toString() : '';
  }

  // Service Invocation
  public async invokeService(serviceName: string, methodName: string, args: Record<string, unknown>): Promise<string> {
    const dataBytes = Buffer.from(JSON.stringify(args), 'utf-8');
    // Use HttpMethod.POST from the imported enum.
    const response: any = await this.client.invoker.invoke(serviceName, methodName, HttpMethod.POST, dataBytes);
    return response && response.data ? response.data.toString() : '';
  }

  // Distributed Lock (Simplified Implementation)
  public async acquireLock(resourceId: string, lockOwner: string, timeoutMs: number): Promise<boolean> {
    const lockKey = `lock-${resourceId}`;
    try {
      const state = await this.getState(lockKey);
      if (state) {
        const lockData = JSON.parse(state);
        const expiryTime: number = lockData.expiryTime;
        const currentTime = Date.now() * 1e6; // convert current time to nanoseconds
        if (currentTime < expiryTime) {
          return false;
        }
      }
    } catch (error) {
      // If parsing fails or state cannot be read, assume lock is available.
    }

    const expiryTime = (Date.now() * 1e6) + timeoutMs * 1_000_000;
    const lockData = { owner: lockOwner, expiryTime };
    try {
      await this.saveState(lockKey, JSON.stringify(lockData));
      return true;
    } catch (error) {
      return false;
    }
  }

  public async releaseLock(resourceId: string, lockOwner: string): Promise<boolean> {
    const lockKey = `lock-${resourceId}`;
    try {
      const state = await this.getState(lockKey);
      if (state) {
        const lockData = JSON.parse(state);
        if (lockData.owner === lockOwner) {
          await this.client.state.delete(this.stateStore, lockKey);
          return true;
        }
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  // Wait for Object
  public async waitForObject(key: string, timeoutMs: number): Promise<string> {
    const timeoutSec = timeoutMs / 1000;
    const startTime = Date.now();
    while ((Date.now() - startTime) / 1000 < timeoutSec) {
      const state = await this.getState(key);
      if (state) {
        return state;
      }
      // Wait for 500ms before checking again.
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    return '';
  }

  // ODRL Policies
  public async setODRLPolicy(policyName: string, policy: Record<string, unknown>): Promise<void> {
    const policyKey = `odrl-policy-${policyName}`;
    await this.saveState(policyKey, JSON.stringify(policy));
  }

  public async applyPermission(serviceName: string, permission: string): Promise<void> {
    const permissionKey = `permission-${serviceName}-${permission}`;
    await this.saveState(permissionKey, "true");
  }

  public async checkPermission(serviceName: string, permission: string): Promise<boolean> {
    const permissionKey = `permission-${serviceName}-${permission}`;
    const state = await this.getState(permissionKey);
    return state === "true";
  }
}
