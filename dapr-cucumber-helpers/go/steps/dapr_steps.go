package steps

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/cucumber/godog"

	"dapr-cucumber-helpers-go/helpers"
)

var daprHelper *helpers.DaprHelper
var scenarioContext map[string]interface{}

func init() {
	scenarioContext = make(map[string]interface{})
}

// PubSub Operations
func iPublishMessageToTopic(message, topic string) error {
	err := daprHelper.PublishMessage(topic, message)
	if err == nil {
		scenarioContext["LastPublishedMessage"] = message
		scenarioContext["LastPublishedTopic"] = topic
	}
	return err
}

func iPublishEventToTopicWithData(topic string, dataTable string) error {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(dataTable), &data); err != nil {
		return err
	}

	err := daprHelper.PublishEvent(topic, data)
	if err == nil {
		scenarioContext["LastPublishedData"] = data
		scenarioContext["LastPublishedTopic"] = topic
	}
	return err
}

func theMessageShouldBePublishedToTopic(topic string) error {
	lastTopic, ok := scenarioContext["LastPublishedTopic"].(string)
	if !ok {
		return fmt.Errorf("no topic was published")
	}
	if lastTopic != topic {
		return fmt.Errorf("expected topic '%s' but got '%s'", topic, lastTopic)
	}
	return nil
}

// State Management
func iSaveStateKeyWithValue(key, value string) error {
	err := daprHelper.SaveState(key, value)
	if err == nil {
		scenarioContext["LastStateKey"] = key
		scenarioContext["LastStateValue"] = value
	}
	return err
}

func iSetStateWithComplexData(dataTable *godog.Table) error {
	states := make(map[string]string)
	for _, row := range dataTable.Rows {
		if len(row.Cells) >= 2 {
			key := row.Cells[0].Value
			value := row.Cells[1].Value
			states[key] = value
		}
	}

	err := daprHelper.SaveBulkState(states)
	if err == nil {
		scenarioContext["LastBulkStates"] = states
	}
	return err
}

func iGetStateKeyAndItShouldBe(key, expectedValue string) error {
	val, err := daprHelper.GetState(key)
	if err != nil {
		return err
	}

	scenarioContext["RetrievedStateKey"] = key
	scenarioContext["RetrievedStateValue"] = val

	if val != expectedValue {
		return fmt.Errorf("expected '%s' but got '%s'", expectedValue, val)
	}
	return nil
}

// Service Invocation
func iInvokeServiceMethodWithArguments(serviceName, methodName string, dataTable string) error {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(dataTable), &data); err != nil {
		return err
	}

	response, err := daprHelper.InvokeService(serviceName, methodName, data)
	if err == nil {
		scenarioContext["LastInvokedService"] = serviceName
		scenarioContext["LastInvokedMethod"] = methodName
		scenarioContext["LastInvokedArguments"] = data
		scenarioContext["LastServiceResponse"] = response
	}
	return err
}

func theServiceResponseShouldContain(expectedValue string) error {
	response, ok := scenarioContext["LastServiceResponse"].(string)
	if !ok {
		return fmt.Errorf("no service response was stored")
	}

	if !contains(response, expectedValue) {
		return fmt.Errorf("expected response to contain '%s' but got '%s'", expectedValue, response)
	}
	return nil
}

// Distributed Lock
func iAcquireDistributedLockWithTimeout(lockName, timeout string) error {
	timeoutMs, err := strconv.Atoi(timeout)
	if err != nil {
		return fmt.Errorf("invalid timeout value: %s", timeout)
	}

	owner := generateUUID()
	success, err := daprHelper.AcquireLock(lockName, owner, time.Duration(timeoutMs)*time.Millisecond)
	if err != nil {
		return err
	}

	scenarioContext["LockName"] = lockName
	scenarioContext["LockOwner"] = owner
	scenarioContext["LockAcquired"] = success

	return nil
}

func iReleaseDistributedLock(lockName string) error {
	owner, ok := scenarioContext["LockOwner"].(string)
	if !ok {
		return fmt.Errorf("no lock owner stored in context")
	}

	success, err := daprHelper.ReleaseLock(lockName, owner)
	if err != nil {
		return err
	}

	scenarioContext["LockReleased"] = success
	return nil
}

func theLockShouldBeAcquired() error {
	acquired, ok := scenarioContext["LockAcquired"].(bool)
	if !ok {
		return fmt.Errorf("lock acquired status not stored in context")
	}

	if !acquired {
		return fmt.Errorf("lock was not acquired")
	}
	return nil
}

func theLockShouldBeReleased() error {
	released, ok := scenarioContext["LockReleased"].(bool)
	if !ok {
		return fmt.Errorf("lock released status not stored in context")
	}

	if !released {
		return fmt.Errorf("lock was not released")
	}
	return nil
}

// Wait for Object
func iWaitForObjectToArriveWithTimeout(objectId, timeout string) error {
	timeoutMs, err := strconv.Atoi(timeout)
	if err != nil {
		return fmt.Errorf("invalid timeout value: %s", timeout)
	}

	result, err := daprHelper.WaitForObject(objectId, time.Duration(timeoutMs)*time.Millisecond)
	if err != nil {
		return err
	}

	scenarioContext["ObjectArrived"] = result != ""
	scenarioContext["ObjectValue"] = result

	return nil
}

func theObjectShouldHaveArrived() error {
	arrived, ok := scenarioContext["ObjectArrived"].(bool)
	if !ok {
		return fmt.Errorf("object arrived status not stored in context")
	}

	if !arrived {
		return fmt.Errorf("object did not arrive")
	}
	return nil
}

// ODRL Policies
func iSetODRLPolicyWithNameAndContent(policyName, policyContent string) error {
	var policy map[string]interface{}
	if err := json.Unmarshal([]byte(policyContent), &policy); err != nil {
		return err
	}

	err := daprHelper.SetODRLPolicy(policyName, policy)
	if err == nil {
		scenarioContext["LastPolicyName"] = policyName
		scenarioContext["LastPolicyContent"] = policy
	}
	return err
}

func iApplyPermissionToService(permission, serviceName string) error {
	err := daprHelper.ApplyPermission(serviceName, permission)
	if err == nil {
		scenarioContext["LastPermission"] = permission
		scenarioContext["LastPermissionService"] = serviceName
	}
	return err
}

func serviceShouldHavePermission(serviceName, permission string) error {
	hasPermission, err := daprHelper.CheckPermission(serviceName, permission)
	if err != nil {
		return err
	}

	if !hasPermission {
		return fmt.Errorf("service '%s' does not have permission '%s'", serviceName, permission)
	}
	return nil
}

// Helper functions
func contains(s, substr string) bool {
	return s != "" && s != "{}" && s != "null" && s != "[]" && json.Valid([]byte(s))
}

func generateUUID() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

// Initialize Scenario Context and Register Steps
func InitializeScenario(ctx *godog.ScenarioContext) {
	var err error
	daprHelper, err = helpers.NewDaprHelper()
	if err != nil {
		panic("Failed to initialize Dapr client: " + err.Error())
	}

	// PubSub Steps
	ctx.Step(`^I publish message "([^"]*)" to topic "([^"]*)"$`, iPublishMessageToTopic)
	ctx.Step(`^I publish event to topic "([^"]*)" with data$`, iPublishEventToTopicWithData)
	ctx.Step(`^the message should be published to topic "([^"]*)"$`, theMessageShouldBePublishedToTopic)

	// State Management Steps
	ctx.Step(`^I save state key "([^"]*)" with value "([^"]*)"$`, iSaveStateKeyWithValue)
	ctx.Step(`^I set state with complex data$`, iSetStateWithComplexData)
	ctx.Step(`^I get state key "([^"]*)" and it should be "([^"]*)"$`, iGetStateKeyAndItShouldBe)

	// Service Invocation Steps
	ctx.Step(`^I invoke service "([^"]*)" method "([^"]*)" with arguments$`, iInvokeServiceMethodWithArguments)
	ctx.Step(`^the service response should contain "([^"]*)"$`, theServiceResponseShouldContain)

	// Distributed Lock Steps
	ctx.Step(`^I acquire distributed lock "([^"]*)" with timeout "([^"]*)"$`, iAcquireDistributedLockWithTimeout)
	ctx.Step(`^I release distributed lock "([^"]*)"$`, iReleaseDistributedLock)
	ctx.Step(`^the lock should be acquired$`, theLockShouldBeAcquired)
	ctx.Step(`^the lock should be released$`, theLockShouldBeReleased)

	// Wait for Object Steps
	ctx.Step(`^I wait for object "([^"]*)" to arrive with timeout "([^"]*)"$`, iWaitForObjectToArriveWithTimeout)
	ctx.Step(`^the object should have arrived$`, theObjectShouldHaveArrived)

	// ODRL Policy Steps
	ctx.Step(`^I set ODRL policy with name "([^"]*)" and content$`, iSetODRLPolicyWithNameAndContent)
	ctx.Step(`^I apply permission "([^"]*)" to service "([^"]*)"$`, iApplyPermissionToService)
	ctx.Step(`^service "([^"]*)" should have permission "([^"]*)"$`, serviceShouldHavePermission)

	// Clear scenario context before each scenario
	ctx.BeforeScenario(func(sc *godog.Scenario) {
		scenarioContext = make(map[string]interface{})
	})
}
