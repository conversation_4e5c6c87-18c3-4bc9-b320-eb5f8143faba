package helpers

import (
	"context"
	"encoding/json"
	"time"

	dapr "github.com/dapr/go-sdk/client"
)

type Dapr<PERSON>elper struct {
	Client     dapr.Client
	PubSubName string
	StateStore string
	LockStore  string
}

func NewDaprHelper() (*DaprHelper, error) {
	client, err := dapr.NewClient()
	if err != nil {
		return nil, err
	}

	// Get configuration from environment or use defaults
	pubsubName := getEnvOrDefault("DAPR_PUBSUB_NAME", "pubsub")
	stateStore := getEnvOrDefault("DAPR_STATE_STORE", "statestore")
	lockStore := getEnvOrDefault("DAPR_LOCK_STORE", "lockstore")

	return &DaprHelper{
		Client:     client,
		PubSubName: pubsubName,
		StateStore: stateStore,
		LockStore:  lockStore,
	}, nil
}

// PubSub Operations
func (h *DaprHelper) PublishMessage(topic, message string) error {
	return h.Client.PublishEvent(context.Background(), h.PubSubName, topic, []byte(message))
}

func (h *DaprHelper) PublishEvent(topic string, data interface{}) error {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}
	return h.Client.PublishEvent(context.Background(), h.PubSubName, topic, dataBytes)
}

// State Management
func (h *DaprHelper) SaveState(key, value string) error {
	return h.Client.SaveState(context.Background(), h.StateStore, key, []byte(value), nil)
}

func (h *DaprHelper) SaveBulkState(states map[string]string) error {
	var items []*dapr.SetStateItem

	for key, value := range states {
		item := &dapr.SetStateItem{
			Key:   key,
			Value: []byte(value),
		}
		items = append(items, item)
	}

	return h.Client.SaveBulkState(context.Background(), h.StateStore, items...)
}

func (h *DaprHelper) GetState(key string) (string, error) {
	item, err := h.Client.GetState(context.Background(), h.StateStore, key, nil)
	if err != nil {
		return "", err
	}
	return string(item.Value), nil
}

// Service Invocation
func (h *DaprHelper) InvokeService(serviceName, methodName string, args interface{}) (string, error) {
	dataBytes, err := json.Marshal(args)
	if err != nil {
		return "", err
	}

	content := &dapr.DataContent{
		ContentType: "application/json",
		Data:        dataBytes,
	}

	resp, err := h.Client.InvokeMethodWithContent(context.Background(), serviceName, methodName, "post", content)
	if err != nil {
		return "", err
	}

	return string(resp), nil
}

// Distributed Lock - using a simplified implementation since Dapr Go SDK might not have direct lock API
func (h *DaprHelper) AcquireLock(resourceId, lockOwner string, expiryTime time.Duration) (bool, error) {
	// Create a lock key
	lockKey := "lock-" + resourceId

	// Check if lock exists
	item, err := h.Client.GetState(context.Background(), h.StateStore, lockKey, nil)
	if err != nil {
		return false, err
	}

	// If lock exists and hasn't expired, fail
	if item.Value != nil && string(item.Value) != "" {
		var lockData map[string]interface{}
		if err := json.Unmarshal(item.Value, &lockData); err != nil {
			return false, err
		}

		if expiryTime, ok := lockData["expiryTime"].(float64); ok {
			if time.Now().UnixNano() < int64(expiryTime) {
				return false, nil
			}
		}
	}

	// Set lock
	lockData := map[string]interface{}{
		"owner":      lockOwner,
		"expiryTime": time.Now().Add(expiryTime).UnixNano(),
	}

	lockDataBytes, err := json.Marshal(lockData)
	if err != nil {
		return false, err
	}

	err = h.Client.SaveState(context.Background(), h.StateStore, lockKey, lockDataBytes, nil)
	if err != nil {
		return false, err
	}

	return true, nil
}

func (h *DaprHelper) ReleaseLock(resourceId, lockOwner string) (bool, error) {
	// Create a lock key
	lockKey := "lock-" + resourceId

	// Check if lock exists and belongs to owner
	item, err := h.Client.GetState(context.Background(), h.StateStore, lockKey, nil)
	if err != nil {
		return false, err
	}

	if item.Value == nil || string(item.Value) == "" {
		return false, nil // Lock doesn't exist
	}

	var lockData map[string]interface{}
	if err := json.Unmarshal(item.Value, &lockData); err != nil {
		return false, err
	}

	if owner, ok := lockData["owner"].(string); ok && owner == lockOwner {
		// Delete the lock
		err = h.Client.DeleteState(context.Background(), h.StateStore, lockKey, nil)
		if err != nil {
			return false, err
		}
		return true, nil
	}

	return false, nil // Lock belongs to someone else
}

// Wait for Object
func (h *DaprHelper) WaitForObject(key string, timeout time.Duration) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	start := time.Now()
	for time.Since(start) < timeout {
		select {
		case <-ctx.Done():
			return "", nil
		default:
			item, err := h.Client.GetState(ctx, h.StateStore, key, nil)
			if err != nil {
				return "", err
			}

			if item.Value != nil && string(item.Value) != "" {
				return string(item.Value), nil
			}

			time.Sleep(500 * time.Millisecond)
		}
	}

	return "", nil
}

// ODRL Policies
func (h *DaprHelper) SetODRLPolicy(policyName string, policy interface{}) error {
	policyKey := "odrl-policy-" + policyName
	policyBytes, err := json.Marshal(policy)
	if err != nil {
		return err
	}

	return h.Client.SaveState(context.Background(), h.StateStore, policyKey, policyBytes, nil)
}

func (h *DaprHelper) ApplyPermission(serviceName, permission string) error {
	permissionKey := "permission-" + serviceName + "-" + permission

	// Store a simple "true" value to indicate permission is granted
	return h.Client.SaveState(context.Background(), h.StateStore, permissionKey, []byte("true"), nil)
}

func (h *DaprHelper) CheckPermission(serviceName, permission string) (bool, error) {
	permissionKey := "permission-" + serviceName + "-" + permission

	item, err := h.Client.GetState(context.Background(), h.StateStore, permissionKey, nil)
	if err != nil {
		return false, err
	}

	// Permission exists if the value is "true"
	return item.Value != nil && string(item.Value) == "true", nil
}

// Utility function to get env or default value
func getEnvOrDefault(key, defaultValue string) string {
	value := getEnv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// Mock for getting environment variables - replace with os.Getenv in real implementation
func getEnv(key string) string {
	// This is a placeholder - in a real implementation, use:
	// return os.Getenv(key)
	return ""
}
