import os
import json
import time
import uuid
from dapr.clients import DaprClient

class DaprHelper:
    """
    A helper class for interacting with Dapr services using the Python SDK.
    This class implements pub/sub operations, state management, service invocation,
    a simplified distributed locking mechanism, waiting for object availability,
    and handling ODRL policies.
    """
    def __init__(self):
        self.client = DaprClient()
        self.pubsub_name = os.getenv("DAPR_PUBSUB_NAME", "pubsub")
        self.state_store = os.getenv("DAPR_STATE_STORE", "statestore")
        self.lock_store = os.getenv("DAPR_LOCK_STORE", "lockstore")

    # Pub/Sub Operations
    def publish_message(self, topic: str, message: str) -> None:
        """
        Publishes a string message to a given topic.
        """
        self.client.publish_event(
            pubsub_name=self.pubsub_name,
            topic_name=topic,
            data=message.encode('utf-8')
        )

    def publish_event(self, topic: str, data: dict) -> None:
        """
        Publishes an event with JSON data to a given topic.
        """
        data_bytes = json.dumps(data).encode('utf-8')
        self.client.publish_event(
            pubsub_name=self.pubsub_name,
            topic_name=topic,
            data=data_bytes
        )

    # State Management
    def save_state(self, key: str, value: str) -> None:
        """
        Saves a state value for a given key.
        """
        self.client.save_state(
            store_name=self.state_store,
            key=key,
            value=value.encode('utf-8')
        )

    def save_bulk_state(self, states: dict) -> None:
        """
        Saves multiple state key-value pairs.
        The Python SDK may not support bulk state natively, so we iterate over the items.
        """
        for key, value in states.items():
            self.save_state(key, value)

    def get_state(self, key: str) -> str:
        """
        Retrieves the state for a given key.
        """
        resp = self.client.get_state(
            store_name=self.state_store,
            key=key
        )
        return resp.data.decode('utf-8') if resp.data else ""

    # Service Invocation
    def invoke_service(self, service_name: str, method_name: str, args: dict) -> str:
        """
        Invokes a method on a given service with provided arguments.
        """
        data_bytes = json.dumps(args).encode('utf-8')
        response = self.client.invoke_method(
            app_id=service_name,
            method_name=method_name,
            data=data_bytes,
            http_verb="POST"
        )
        return response.data.decode('utf-8')

    # Distributed Lock (Simplified Implementation)
    def acquire_lock(self, resource_id: str, lock_owner: str, timeout_ms: int) -> bool:
        """
        Acquires a distributed lock for a given resource.
        Uses state management to simulate a lock mechanism.
        """
        lock_key = "lock-" + resource_id
        try:
            state = self.get_state(lock_key)
            if state:
                lock_data = json.loads(state)
                expiry_time = lock_data.get("expiryTime", 0)
                current_time = int(time.time() * 1e9)
                if current_time < expiry_time:
                    return False
        except Exception:
            # In case of an error reading lock, assume it is available
            pass

        expiry_time = int(time.time() * 1e9) + timeout_ms * 1_000_000
        lock_data = {
            "owner": lock_owner,
            "expiryTime": expiry_time
        }
        try:
            self.save_state(lock_key, json.dumps(lock_data))
            return True
        except Exception:
            return False

    def release_lock(self, resource_id: str, lock_owner: str) -> bool:
        """
        Releases a distributed lock if the current owner matches.
        """
        lock_key = "lock-" + resource_id
        try:
            state = self.get_state(lock_key)
            if state:
                lock_data = json.loads(state)
                if lock_data.get("owner") == lock_owner:
                    self.client.delete_state(
                        store_name=self.state_store,
                        key=lock_key
                    )
                    return True
            return False
        except Exception:
            return False

    # Wait for Object
    def wait_for_object(self, key: str, timeout_ms: int) -> str:
        """
        Polls for an object (state) to be available until a timeout is reached.
        """
        timeout_sec = timeout_ms / 1000.0
        start_time = time.time()
        while (time.time() - start_time) < timeout_sec:
            state = self.get_state(key)
            if state:
                return state
            time.sleep(0.5)
        return ""

    # ODRL Policies
    def set_odrl_policy(self, policy_name: str, policy: dict) -> None:
        """
        Sets an ODRL policy by saving it to the state store.
        """
        policy_key = "odrl-policy-" + policy_name
        self.save_state(policy_key, json.dumps(policy))

    def apply_permission(self, service_name: str, permission: str) -> None:
        """
        Applies a permission to a service by saving a true flag.
        """
        permission_key = f"permission-{service_name}-{permission}"
        self.save_state(permission_key, "true")

    def check_permission(self, service_name: str, permission: str) -> bool:
        """
        Checks if a service has a given permission.
        """
        permission_key = f"permission-{service_name}-{permission}"
        state = self.get_state(permission_key)
        return state == "true"
