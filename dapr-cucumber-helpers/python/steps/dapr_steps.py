import json
import time
import uuid
from behave import given, when, then
from dapr_helper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

# Initialize the helper and a shared scenario context
dapr_helper = DaprHelper()
scenario_context = {}

@when('I publish message "{message}" to topic "{topic}"')
def step_publish_message(context, message, topic):
    try:
        dapr_helper.publish_message(topic, message)
        scenario_context["LastPublishedMessage"] = message
        scenario_context["LastPublishedTopic"] = topic
    except Exception as e:
        raise Exception(f"Failed to publish message: {e}")

@when('I publish event to topic "{topic}" with data')
def step_publish_event(context, topic):
    # Expects JSON data in the step's text block
    data_table = context.text
    try:
        data = json.loads(data_table)
    except Exception as e:
        raise Exception(f"Failed to parse data: {e}")
    try:
        dapr_helper.publish_event(topic, data)
        scenario_context["LastPublishedData"] = data
        scenario_context["LastPublishedTopic"] = topic
    except Exception as e:
        raise Exception(f"Failed to publish event: {e}")

@then('the message should be published to topic "{topic}"')
def step_verify_message_topic(context, topic):
    last_topic = scenario_context.get("LastPublishedTopic")
    if not last_topic:
        raise Exception("No topic was published")
    if last_topic != topic:
        raise Exception(f"Expected topic '{topic}' but got '{last_topic}'")

@when('I save state key "{key}" with value "{value}"')
def step_save_state(context, key, value):
    try:
        dapr_helper.save_state(key, value)
        scenario_context["LastStateKey"] = key
        scenario_context["LastStateValue"] = value
    except Exception as e:
        raise Exception(f"Failed to save state: {e}")

@when('I set state with complex data')
def step_set_state_complex(context):
    # Expects a JSON representation of key-value pairs in the step's text block
    if context.text:
        try:
            states = json.loads(context.text)
        except Exception as e:
            raise Exception(f"Failed to parse state data: {e}")
    else:
        states = {}
    try:
        dapr_helper.save_bulk_state(states)
        scenario_context["LastBulkStates"] = states
    except Exception as e:
        raise Exception(f"Failed to save bulk state: {e}")

@then('I get state key "{key}" and it should be "{expected_value}"')
def step_get_state(context, key, expected_value):
    try:
        value = dapr_helper.get_state(key)
        scenario_context["RetrievedStateKey"] = key
        scenario_context["RetrievedStateValue"] = value
        if value != expected_value:
            raise Exception(f"Expected '{expected_value}' but got '{value}'")
    except Exception as e:
        raise Exception(f"Failed to get state: {e}")

@when('I invoke service "{service_name}" method "{method_name}" with arguments')
def step_invoke_service(context, service_name, method_name):
    # Expects JSON arguments in the step's text block
    if context.text:
        try:
            args = json.loads(context.text)
        except Exception as e:
            raise Exception(f"Failed to parse arguments: {e}")
    else:
        args = {}
    try:
        response = dapr_helper.invoke_service(service_name, method_name, args)
        scenario_context["LastInvokedService"] = service_name
        scenario_context["LastInvokedMethod"] = method_name
        scenario_context["LastInvokedArguments"] = args
        scenario_context["LastServiceResponse"] = response
    except Exception as e:
        raise Exception(f"Failed to invoke service: {e}")

@then('the service response should contain "{expected_value}"')
def step_verify_service_response(context, expected_value):
    response = scenario_context.get("LastServiceResponse", "")
    if expected_value not in response:
        raise Exception(f"Expected response to contain '{expected_value}' but got '{response}'")

@when('I acquire distributed lock "{lock_name}" with timeout "{timeout}"')
def step_acquire_lock(context, lock_name, timeout):
    try:
        timeout_ms = int(timeout)
    except Exception:
        raise Exception(f"Invalid timeout value: {timeout}")
    lock_owner = str(uuid.uuid4())
    success = dapr_helper.acquire_lock(lock_name, lock_owner, timeout_ms)
    scenario_context["LockName"] = lock_name
    scenario_context["LockOwner"] = lock_owner
    scenario_context["LockAcquired"] = success
    if not success:
        raise Exception("Lock was not acquired")

@when('I release distributed lock "{lock_name}"')
def step_release_lock(context, lock_name):
    lock_owner = scenario_context.get("LockOwner")
    if not lock_owner:
        raise Exception("No lock owner stored in context")
    success = dapr_helper.release_lock(lock_name, lock_owner)
    scenario_context["LockReleased"] = success
    if not success:
        raise Exception("Lock was not released")

@then('the lock should be acquired')
def step_verify_lock_acquired(context):
    acquired = scenario_context.get("LockAcquired", False)
    if not acquired:
        raise Exception("Lock was not acquired")

@then('the lock should be released')
def step_verify_lock_released(context):
    released = scenario_context.get("LockReleased", False)
    if not released:
        raise Exception("Lock was not released")

@when('I wait for object "{object_id}" to arrive with timeout "{timeout}"')
def step_wait_for_object(context, object_id, timeout):
    try:
        timeout_ms = int(timeout)
    except Exception:
        raise Exception(f"Invalid timeout value: {timeout}")
    result = dapr_helper.wait_for_object(object_id, timeout_ms)
    scenario_context["ObjectArrived"] = bool(result)
    scenario_context["ObjectValue"] = result
    if not result:
        raise Exception("Object did not arrive")

@then('the object should have arrived')
def step_verify_object_arrived(context):
    arrived = scenario_context.get("ObjectArrived", False)
    if not arrived:
        raise Exception("Object did not arrive")

@when('I set ODRL policy with name "{policy_name}" and content')
def step_set_odrl_policy(context, policy_name):
    # Expects JSON policy content in the step's text block
    if context.text:
        try:
            policy = json.loads(context.text)
        except Exception as e:
            raise Exception(f"Failed to parse policy content: {e}")
    else:
        policy = {}
    try:
        dapr_helper.set_odrl_policy(policy_name, policy)
        scenario_context["LastPolicyName"] = policy_name
        scenario_context["LastPolicyContent"] = policy
    except Exception as e:
        raise Exception(f"Failed to set ODRL policy: {e}")

@when('I apply permission "{permission}" to service "{service_name}"')
def step_apply_permission(context, permission, service_name):
    try:
        dapr_helper.apply_permission(service_name, permission)
        scenario_context["LastPermission"] = permission
        scenario_context["LastPermissionService"] = service_name
    except Exception as e:
        raise Exception(f"Failed to apply permission: {e}")

@then('service "{service_name}" should have permission "{permission}"')
def step_check_permission(context, service_name, permission):
    try:
        has_permission = dapr_helper.check_permission(service_name, permission)
        if not has_permission:
            raise Exception(f"Service '{service_name}' does not have permission '{permission}'")
    except Exception as e:
        raise Exception(f"Failed to check permission: {e}")
