@api
Feature: Generic API Testing

  Background:
    Given the API base URL is set to "http://localhost:8080"

  # Status Codes
  Scenario Outline: Validate response status codes for various endpoints and methods
    When I send a <method> request to "<endpoint>"
    Then the response status should be <status>

    Examples:
      | method | endpoint         | status |
      | GET    | /resource        | 200    |
      | POST   | /resource        | 201    |
      | PUT    | /resource/1      | 200    |
      | DELETE | /resource/1      | 204    |
      | GET    | /nonexistent     | 404    |

Scenario Outline: Validate POST and PUT with body
  When I send a <method> request to "<endpoint>" with body:
    """
    <body>
    """
  Then the response status should be <status>

  Examples:
    | method | endpoint     | body                                            | status |
    | POST   | /resource    | {"name":"Resource","active":true}               | 201    |
    | POST   | /resource    | {}                                              | 422    |
    | POST   | /resource    | invalid json                                    | 400    |
    | PUT    | /resource/1  | {"name":"Updated Resource","active":false}      | 200    |
    | PUT    | /resource/1  | {}                                              | 422    |


  # Response Time
  Scenario: Response time is acceptable
    When I send a GET request to "/resource"
    Then the response time should be less than 1000 milliseconds

  # Validation Method Support
  Scenario: POST /resource with valid data
  When I send a POST request to "/resource" with body:
  """
  {
    "name": "Test Resource",
    "active": true
  }
  """
  Then the response status should be 201
  And the response should contain "id, name, active"
  And the field "name" should be of type string
  And the field "active" should be of type boolean

Scenario: POST /resource with invalid data
  When I send a POST request to "/resource" with body:
  """
  {
    "name": 123,
    "active": "yes"
  }
  """
  Then the response status should be 400
  And the response should contain "error"

Scenario: POST /resource with missing required fields
  When I send a POST request to "/resource" with body:
  """
  {
    "active": true
  }
  """
Scenario: PATCH /resource
  When I send a PATCH request to "/resource" with body:
  """
  {
    "key": "value"
  }
  """

  # Authentication and Authorization
  Scenario: Request without authorization
    When I send a GET request to "/protected-resource" without authorization
    Then the response status should be 401

  Scenario: Request with valid token
    When I send a GET request to "/protected-resource" with valid token
    Then the response status should be 200

  Scenario: Request with expired token
    When I send a GET request to "/protected-resource" with expired token
    Then the response status should be 401

  # Request and Response Schema
  Scenario: Response contains expected fields and types
    When I send a GET request to "/resource/1"
    Then the response status should be 200
    And the response should contain "id,name,active"
    And the field "id" should be of type integer
    And the field "name" should be of type string
    And the field "active" should be of type boolean

  # Idempotency
  Scenario: PUT request is idempotent
    When I send a PUT request to "/resource/1" with the same data multiple times

  Scenario: DELETE request is idempotent
    When I send a DELETE request to "/resource/1" multiple times

  # Security Headers
  Scenario: Response includes security headers
    When I send a GET request to "/resource"
    Then the response header "Content-Security-Policy" should be present
    And the response header "X-Content-Type-Options" should be present
    And the response header "Strict-Transport-Security" should be present

  # CORS Headers
  Scenario: Response includes CORS headers
    When I send a OPTIONS request to "/resource"
    Then the response header "Access-Control-Allow-Origin" should be "*"
    And the response header "Access-Control-Allow-Methods" should be "GET, POST, PUT, DELETE, OPTIONS"



