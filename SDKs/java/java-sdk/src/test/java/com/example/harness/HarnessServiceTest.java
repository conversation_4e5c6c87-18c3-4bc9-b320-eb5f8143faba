package com.example.harness;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Example test demonstrating how to use the harness.
 */
public class HarnessServiceTest {
    private CustomFakePostgres postgres;
    private TestFakeRedis redis;
    private HarnessService service;
    
    /**
     * Extended FakeRedis class that provides a JedisClientAdapter.
     */
    static class TestFakeRedis extends HarnessUtilities.FakeRedis {
        @Override
        protected HarnessUtilities.RedisClient createRedisClient(String host, int port) {
            return new JedisClientAdapter(host, port);
        }
    }
    
    /**
     * Custom PostgreSQL fake with more robust connection handling.
     */
    static class CustomFakePostgres {
        private com.example.testcontainers.ContainerBrick containerBrick;
        private Connection connection;
        
        /**
         * Initializes the PostgreSQL container with a custom port.
         * 
         * @throws Exception if initialization fails
         */
        public void initialize() throws Exception {
            // Configure the container using BrickConfig with a unique port
            com.example.testcontainers.BrickConfig config = new com.example.testcontainers.BrickConfig.Builder("postgres", "fake-postgres-test")
                .environmentVariables(Map.of(
                    "POSTGRES_PASSWORD", "postgres",
                    "POSTGRES_USER", "postgres",
                    "POSTGRES_DB", "testdb"
                ))
                .exposedPort("5432/tcp")
                .hostPort("15432") // Use a different port than the default 5432
                .build();
                
            // Create and start the container brick
            containerBrick = com.example.testcontainers.ContainerBrickFactory.newContainerBrick(config);
            containerBrick.start();
            
            // Build connection string
            String host = "localhost";
            java.util.Optional<Integer> mappedPort = containerBrick.getMappedPort("5432/tcp");
            if (!mappedPort.isPresent()) {
                throw new RuntimeException("Failed to get mapped port for PostgreSQL container");
            }
            
            String connectionString = String.format(
                "**************************************************************", 
                host, 
                mappedPort.get()
            );
            
            // Retry connecting
            SQLException lastException = null;
            for (int i = 0; i < 15; i++) {  // Increase retry attempts
                try {
                    connection = java.sql.DriverManager.getConnection(connectionString);
                    System.out.println("Successfully connected to PostgreSQL at " + connectionString);
                    break;
                } catch (SQLException e) {
                    lastException = e;
                    System.out.println("Retrying PostgreSQL connection attempt " + (i+1) + "/15...");
                    try {
                        Thread.sleep(1000);  // Wait longer between retries
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            
            if (connection == null && lastException != null) {
                throw new RuntimeException("Could not connect to PostgreSQL", lastException);
            }
        }
        
        /**
         * Gets the database connection.
         * 
         * @return the JDBC connection
         */
        public Connection getConnection() {
            return connection;
        }
        
        /**
         * Cleans up resources.
         * 
         * @throws Exception if cleanup fails
         */
        public void cleanup() throws Exception {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    System.err.println("Error closing database connection: " + e.getMessage());
                }
            }
            
            if (containerBrick != null) {
                containerBrick.stop();
            }
        }
    }
    
    @BeforeEach
    void setUp() throws Exception {
        try {
            // Initialize PostgreSQL with specific port to avoid conflicts
            postgres = new CustomFakePostgres();
            postgres.initialize();
            
            // Create test table
            createTestTable(postgres.getConnection());
            
            // Initialize Redis
            redis = new TestFakeRedis();
            redis.initialize();
            
            // Create service with adapter for the Redis client
            service = new HarnessService(
                postgres.getConnection(),
                new HarnessService.RedisClient() {
                    @Override
                    public String get(String key) {
                        return redis.getWithCounter(key);
                    }
                    
                    @Override
                    public void set(String key, String value, long timeoutMs) {
                        redis.getClient().setex(key, (int)(timeoutMs / 1000), value);
                    }
                },
                (from, to, subject, body) -> true, // Mock email sender that always succeeds
                LoggerFactory.getLogger(getClass())
            );
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }
    
    @AfterEach
    void tearDown() throws Exception {
        if (redis != null) {
            try {
                redis.cleanup();
            } catch (Exception e) {
                System.err.println("Error cleaning up Redis: " + e.getMessage());
            }
        }
        
        if (postgres != null) {
            try {
                postgres.cleanup();
            } catch (Exception e) {
                System.err.println("Error cleaning up PostgreSQL: " + e.getMessage());
            }
        }
    }
    
    @Test
    void testGetUser_CacheMiss_ThenCacheHit() throws Exception {
        // Insert test user
        insertTestUser(postgres.getConnection(), "user123", "Test User", "<EMAIL>");
        
        // Create context
        HarnessService.Context context = new HarnessService.Context("test-correlation-id");
        
        // Reset hit counter for Redis
        String cacheKey = "user:user123";
        
        // First call - should be a cache miss and read from database
        HarnessService.User user = service.getUser(context, "user123");
        
        // Verify user data
        assertNotNull(user);
        assertEquals("user123", user.getId());
        assertEquals("Test User", user.getName());
        assertEquals("<EMAIL>", user.getEmail());
        
        // Get the current hit count
        int hitCount = redis.getHitCount(cacheKey);
        
        // Second call - should be a cache hit
        HarnessService.User cachedUser = service.getUser(context, "user123");
        
        // Verify user data is the same
        assertEquals(user.getId(), cachedUser.getId());
        assertEquals(user.getName(), cachedUser.getName());
        assertEquals(user.getEmail(), cachedUser.getEmail());
        
        // Verify cache was hit by checking the hit count increased
        assertEquals(hitCount + 1, redis.getHitCount(cacheKey), 
            "Cache hit count should increase by 1 after second call");
    }
    
    @Test
    void testSendWelcomeEmail() throws Exception {
        HarnessService.User user = new HarnessService.User("user456", "Jane Smith", "<EMAIL>");
        HarnessService.Context context = new HarnessService.Context("email-test-correlation-id");
        
        boolean result = service.sendWelcomeEmail(context, user);
        
        assertTrue(result, "Email sending should succeed with our mock implementation");
    }
    
    /**
     * Helper method to create the test table.
     */
    private void createTestTable(Connection connection) throws SQLException {
        try (PreparedStatement statement = connection.prepareStatement(
                "CREATE TABLE IF NOT EXISTS users (" +
                "id VARCHAR(50) PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "email VARCHAR(100) NOT NULL)")) {
            statement.executeUpdate();
        }
    }
    
    /**
     * Helper method to insert a test user.
     */
    private void insertTestUser(Connection connection, String id, String name, String email) throws SQLException {
        try (PreparedStatement statement = connection.prepareStatement(
                "INSERT INTO users (id, name, email) VALUES (?, ?, ?)")) {
            statement.setString(1, id);
            statement.setString(2, name);
            statement.setString(3, email);
            statement.executeUpdate();
        }
    }
} 