package com.example.stepdefinitions;

import io.cucumber.java.en.Given;
import io.cucumber.java.en.When;
import io.cucumber.java.en.Then;
import io.cucumber.docstring.DocString;

import java.io.IOException;
import java.net.URI;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Arrays;
import java.util.stream.Collectors;

import org.json.JSONException;
import org.json.JSONObject;
import org.json.JSONArray;

public class APISteps {
    private static final String VALID_TOKEN = System.getenv("VALID_TOKEN");
    private static final String EXPIRED_TOKEN = System.getenv("EXPIRED_TOKEN");
    
    private String baseUrl;
    private HttpResponse<String> lastResponse;
    private String lastResponseBody;
    private HttpClient httpClient;
    private long responseTime;
    private String authToken;
    
    public APISteps() {
        httpClient = HttpClient.newBuilder()
                .version(HttpClient.Version.HTTP_1_1)
                .connectTimeout(Duration.ofSeconds(5))
                .build();
    }
    
    private void addAuthHeader(HttpRequest.Builder requestBuilder, String token) {
        if (token != null && !token.isEmpty()) {
            requestBuilder.header("Authorization", "Bearer " + token);
        }
    }
    
    private String buildUrlWithParams(String url, Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return url;
        }
        
        StringBuilder sb = new StringBuilder(url);
        sb.append(url.contains("?") ? "&" : "?");
        
        String queryString = params.entrySet().stream()
                .map(e -> URLEncoder.encode(e.getKey(), StandardCharsets.UTF_8) + "=" + 
                        URLEncoder.encode(e.getValue(), StandardCharsets.UTF_8))
                .collect(Collectors.joining("&"));
        
        sb.append(queryString);
        return sb.toString();
    }
    
    private HttpResponse<String> sendRequest(String method, String endpoint, String body, String token) throws IOException, InterruptedException {
        String url = baseUrl + endpoint;
        
        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("Content-Type", "application/json");
        
        addAuthHeader(requestBuilder, token);
        
        if (token != null) {
            System.out.println("DEBUG: Request URL: " + url);
            System.out.println("DEBUG: Authorization header: Bearer " + token);
        }
        
        switch (method) {
            case "GET":
                requestBuilder.GET();
                break;
            case "DELETE":
                requestBuilder.DELETE();
                break;
            case "POST":
                requestBuilder.POST(HttpRequest.BodyPublishers.ofString(body != null ? body : ""));
                break;
            case "PUT":
                requestBuilder.PUT(HttpRequest.BodyPublishers.ofString(body != null ? body : ""));
                break;
            case "PATCH":
                requestBuilder.method("PATCH", HttpRequest.BodyPublishers.ofString(body != null ? body : ""));
                break;
            case "OPTIONS":
                requestBuilder.method("OPTIONS", HttpRequest.BodyPublishers.noBody());
                break;
            default:
                throw new IllegalArgumentException("Unsupported HTTP method: " + method);
        }
        
        long startTime = System.currentTimeMillis();
        HttpResponse<String> response = httpClient.send(requestBuilder.build(), 
                HttpResponse.BodyHandlers.ofString());
        responseTime = System.currentTimeMillis() - startTime;
        
        System.out.println("Response Status: " + response.statusCode());
        System.out.println("Response Body: " + response.body());
        
        lastResponse = response;
        lastResponseBody = response.body();
        
        return response;
    }
    
    @Given("the API base URL is set to {string}")
    public void theAPIBaseURLIsSetTo(String url) {
        baseUrl = url;
    }
    
    @Given("the auth token is set to {string}")
    public void theAuthTokenIsSetTo(String token) {
        authToken = token;
    }
    
    @Given("the expired token is set to {string}")
    public void theExpiredTokenIsSetTo(String token) {
        authToken = token;
    }
    
    @When("I send a {word} request to {string}")
    public void iSendRequestTo(String method, String endpoint) throws IOException, InterruptedException {
        String body = null;
        if (method.equals("POST") || method.equals("PUT")) {
            JSONObject jsonBody = new JSONObject();
            jsonBody.put("name", "test resource");
            jsonBody.put("active", true);
            body = jsonBody.toString();
        }
        sendRequest(method, endpoint, body, null);
    }
    
    @When("I send a POST request to {string} with body:")
    public void iSendAPOSTRequestToWithBody(String endpoint, DocString docString) throws IOException, InterruptedException {
        if (docString == null || docString.getContent().isEmpty()) {
            throw new IllegalArgumentException("Body content is required");
        }
        sendRequest("POST", endpoint, docString.getContent(), null);
    }
    
    @When("I send a PATCH request to {string} with body:")
    public void iSendAPATCHRequestToWithBody(String endpoint, DocString docString) throws IOException, InterruptedException {
        String body = docString != null ? docString.getContent() : null;
        sendRequest("PATCH", endpoint, body, null);
    }
    
    @When("I send a POST request to {string} with empty body")
    public void iSendAPOSTRequestToWithEmptyBody(String endpoint) throws IOException, InterruptedException {
        sendRequest("POST", endpoint, "{}", null);
    }
    
    @When("I send a GET request to {string} without authorization")
    public void iSendGETRequestToWithoutAuthorization(String endpoint) throws IOException, InterruptedException {
        sendRequest("GET", endpoint, null, null);
    }
    
    @When("I send a {word} request to {string} with valid token")
    public void iSendRequestToWithValidToken(String method, String endpoint) throws IOException, InterruptedException {
        String token = authToken;
        if (token == null || token.isEmpty()) {
            token = VALID_TOKEN;
        }
        if (token == null || token.isEmpty()) {
            token = "valid-test-token-12345";
            System.out.println("Warning: Using default test token. Set VALID_TOKEN environment variable for production tests.");
        }
        sendRequest(method, endpoint, null, token);
    }
    
    @When("I send a {word} request to {string} with expired token")
    public void iSendRequestToWithExpiredToken(String method, String endpoint) throws IOException, InterruptedException {
        String token = authToken;
        if (token == null || token.isEmpty()) {
            token = EXPIRED_TOKEN;
        }
        if (token == null || token.isEmpty()) {
            token = "expired-test-token-67890";
            System.out.println("Warning: Using default test token. Set EXPIRED_TOKEN environment variable for production tests.");
        }
        System.out.println("DEBUG: Sending " + method + " request to " + endpoint + " with expired token: " + token);
        sendRequest(method, endpoint, null, token);
    }
    
    @Then("the response status should be {int}")
    public void theResponseStatusShouldBe(int status) {
        if (lastResponse == null) {
            throw new IllegalStateException("No response received");
        }
        if (lastResponse.statusCode() != status) {
            throw new AssertionError("Expected status " + status + " but got " + lastResponse.statusCode());
        }
    }
    
    @Then("the response time should be less than {int} milliseconds")
    public void theResponseTimeShouldBeLessThan(int ms) {
        if (lastResponse == null) {
            throw new IllegalStateException("No response received");
        }
        if (responseTime >= ms) {
            throw new AssertionError("Response time " + responseTime + " ms exceeded limit of " + ms + " ms");
        }
    }
    
    @Then("the response should contain {string}")
    public void theResponseShouldContain(String fields) {
        if (lastResponseBody == null || lastResponseBody.isEmpty()) {
            throw new IllegalStateException("No response body received");
        }
        
        try {
            JSONObject jsonObject = new JSONObject(lastResponseBody);
            
            for (String field : fields.split(",")) {
                field = field.trim();
                if (!jsonObject.has(field)) {
                    throw new AssertionError("Response does not contain expected field: " + field);
                }
            }
        } catch (JSONException e) {
            throw new AssertionError("Response body is not valid JSON: " + e.getMessage());
        }
    }
    
    @Then("the field {string} should be of type {word}")
    public void theFieldShouldBeOfType(String field, String type) {
        if (lastResponseBody == null || lastResponseBody.isEmpty()) {
            throw new IllegalStateException("No response body received");
        }
        
        try {
            JSONObject jsonObject = new JSONObject(lastResponseBody);
            
            if (!jsonObject.has(field)) {
                throw new AssertionError("Response does not contain field: " + field);
            }
            
            Object value = jsonObject.get(field);
            
            switch (type) {
                case "integer":
                    if (!(value instanceof Integer) && !(value instanceof Long)) {
                        if (value instanceof Double) {
                            double doubleValue = (Double) value;
                            if (doubleValue == Math.floor(doubleValue)) {
                                // It's a whole number, so acceptable as integer
                                break;
                            }
                        }
                        throw new AssertionError("Field " + field + " is not an integer");
                    }
                    break;
                case "string":
                    if (!(value instanceof String)) {
                        throw new AssertionError("Field " + field + " is not a string");
                    }
                    break;
                case "boolean":
                    if (!(value instanceof Boolean)) {
                        throw new AssertionError("Field " + field + " is not a boolean");
                    }
                    break;
                case "array":
                    if (!(value instanceof JSONArray)) {
                        throw new AssertionError("Field " + field + " is not an array");
                    }
                    break;
                case "object":
                    if (!(value instanceof JSONObject)) {
                        throw new AssertionError("Field " + field + " is not an object");
                    }
                    break;
                case "number":
                    if (!(value instanceof Number)) {
                        throw new AssertionError("Field " + field + " is not a number");
                    }
                    break;
                default:
                    throw new IllegalArgumentException("Unsupported type check: " + type);
            }
        } catch (JSONException e) {
            throw new AssertionError("Response body is not valid JSON: " + e.getMessage());
        }
    }
    
    @When("I send a PUT request to {string} with the same data multiple times")
    public void iSendAPUTRequestToWithTheSameDataMultipleTimes(String endpoint) throws IOException, InterruptedException {
        JSONObject jsonBody = new JSONObject();
        jsonBody.put("name", "test resource");
        jsonBody.put("active", true);
        String body = jsonBody.toString();
        
        for (int i = 0; i < 3; i++) {
            sendRequest("PUT", endpoint, body, null);
            if (lastResponse.statusCode() != 200) {
                throw new AssertionError("Expected status 200, got " + lastResponse.statusCode() + " on iteration " + (i + 1));
            }
        }
    }
    
    @When("I send a DELETE request to {string} multiple times")
    public void iSendADELETERequestToMultipleTimes(String endpoint) throws IOException, InterruptedException {
        for (int i = 0; i < 3; i++) {
            sendRequest("DELETE", endpoint, null, null);
            int status = lastResponse.statusCode();
            if (status != 204 && status != 404) {
                throw new AssertionError("Expected status 204 or 404, got " + status + " on iteration " + (i + 1));
            }
        }
    }
    
    @Then("the response header {string} should be present")
    public void theResponseHeaderShouldBePresent(String header) {
        if (lastResponse == null) {
            throw new IllegalStateException("No response received");
        }
        
        if (!lastResponse.headers().map().containsKey(header)) {
            throw new AssertionError("Response header '" + header + "' not present");
        }
    }

    
    @When("I send a PUT request to {string} with body:")
    public void iSendAPUTRequestToWithBody(String endpoint, DocString docString) throws IOException, InterruptedException {
        String body = docString != null ? docString.getContent() : null;
        sendRequest("PUT", endpoint, body, null);
    }
    
    @Then("the response header {string} should be {string}")
    public void theResponseHeaderShouldBe(String header, String expectedValue) {
        if (lastResponse == null) {
            throw new IllegalStateException("No response received");
        }
        
        if (!lastResponse.headers().map().containsKey(header)) {
            throw new AssertionError("Response header '" + header + "' not present");
        }
        
        String actualValue = lastResponse.headers().firstValue(header).orElse("");
        if (!actualValue.equals(expectedValue)) {
            throw new AssertionError("Expected header '" + header + "' to be '" + expectedValue + "' but got '" + actualValue + "'");
        }
    }
}
