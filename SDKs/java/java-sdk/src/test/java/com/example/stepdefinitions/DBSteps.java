package com.example.stepdefinitions;

import io.cucumber.java.Before;
import io.cucumber.java.After;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.When;
import io.cucumber.java.en.Then;
import io.cucumber.datatable.DataTable;

import java.sql.*;
import java.util.List;
import java.util.Map;
import java.util.Properties;

public class DBSteps {
    // Global database connection
    private Connection connection;
    private PostgresTest pgTest;
    private String host;
    private String port;
    private String user;
    private String password;
    private String dbname;
    private boolean connectionResult;
    private ResultSet queryResult;

    public DBSteps() {
        pgTest = new PostgresTest();
        // Set default values from environment variables or use fallbacks
        host = getEnvOrDefault("DB_HOST", "localhost");
        port = getEnvOrDefault("DB_PORT", "5433");
        user = getEnvOrDefault("DB_USER", "postgres");
        password = getEnvOrDefault("DB_PASSWORD", "postgres");
        dbname = getEnvOrDefault("DB_NAME", "demo_db");
    }

    public String getEnvOrDefault(String key, String defaultValue) {
        String value = System.getenv(key);
        return (value != null) ? value : defaultValue;
    }

    // PostgreSQL test class
    public class PostgresTest {
        private Connection conn;

        public void connectToDB() throws SQLException {
            try {
                String url = String.format("jdbc:postgresql://%s:%s/%s", host, port, dbname);
                Properties props = new Properties();
                props.setProperty("user", user);
                props.setProperty("password", password);
                props.setProperty("ssl", "false");

                conn = DriverManager.getConnection(url, props);
                connection = conn; // Update the global connection reference
                connectionResult = true;
            } catch (SQLException e) {
                System.err.println("Failed to connect to database: " + e.getMessage());
                connectionResult = false;
                throw e;
            }
        }

        public void closeDB() throws SQLException {
            if (conn != null && !conn.isClosed()) {
                conn.close();
                conn = null;
                connection = null;
            }
        }

        public void connectionSuccessful() throws SQLException {
            if (conn == null || conn.isClosed()) {
                throw new SQLException("No database connection");
            }
            
            try (Statement stmt = conn.createStatement()) {
                stmt.execute("SELECT 1");
            }
        }

        public void rollbackTransaction() throws SQLException {
            if (conn == null || conn.isClosed()) {
                throw new SQLException("No transaction to rollback");
            }
            conn.rollback();
        }

        public void tablesShouldExist(DataTable table) throws SQLException {
            if (conn == null || conn.isClosed()) {
                throw new SQLException("No database connection");
            }

            List<Map<String, String>> rows = table.asMaps();
            for (Map<String, String> row : rows) {
                String tableName = row.get("table_name");
                if (tableName != null) {
                    tableShouldExist(tableName);
                }
            }
        }

        public void tableShouldExist(String tableName) throws SQLException {
            if (conn == null || conn.isClosed()) {
                throw new SQLException("No database connection");
            }

            String sql = "SELECT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'public' AND table_name = ?)";
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, tableName);
                try (ResultSet rs = pstmt.executeQuery()) {
                    if (rs.next() && !rs.getBoolean(1)) {
                        throw new SQLException("Table " + tableName + " does not exist");
                    }
                }
            }
        }

        public void columnsShouldExistInTable(String tableName, DataTable columnsTable) throws SQLException {
            if (conn == null || conn.isClosed()) {
                throw new SQLException("No database connection");
            }

            List<Map<String, String>> rows = columnsTable.asMaps();
            for (Map<String, String> row : rows) {
                String columnName = row.get("column_name");
                String dataType = row.get("data_type");
                String isNullable = row.get("is_nullable");

                String sql = "SELECT column_name, data_type, is_nullable FROM information_schema.columns WHERE table_schema = 'public' AND table_name = ? AND column_name = ?";
                try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                    pstmt.setString(1, tableName);
                    pstmt.setString(2, columnName);
                    try (ResultSet rs = pstmt.executeQuery()) {
                        if (!rs.next()) {
                            throw new SQLException("Column " + columnName + " not found in table " + tableName);
                        }

                        String actualDataType = rs.getString("data_type");
                        String actualIsNullable = rs.getString("is_nullable");

                        // Handle PostgreSQL's different data type names
                        if (dataType != null && !actualDataType.toLowerCase().contains(dataType.toLowerCase())) {
                            throw new SQLException("Expected data type " + dataType + " for column " + columnName + " but got " + actualDataType);
                        }

                        if (isNullable != null) {
                            boolean expectedNullable = isNullable.equals("YES");
                            boolean actualNullable = actualIsNullable.equals("YES");
                            if (expectedNullable != actualNullable) {
                                throw new SQLException("Expected nullable=" + isNullable + " for column " + columnName + " but got " + actualIsNullable);
                            }
                        }
                    }
                }
            }
        }

        public void indexesShouldExistOnTable(String tableName, DataTable indexesTable) throws SQLException {
            if (conn == null || conn.isClosed()) {
                throw new SQLException("No database connection");
            }

            List<Map<String, String>> rows = indexesTable.asMaps();
            for (Map<String, String> row : rows) {
                String indexName = row.get("index_name");
                if (indexName != null) {
                    String sql = "SELECT indexname FROM pg_indexes WHERE schemaname = 'public' AND tablename = ? AND indexname = ?";
                    try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                        pstmt.setString(1, tableName);
                        pstmt.setString(2, indexName);
                        try (ResultSet rs = pstmt.executeQuery()) {
                            if (!rs.next()) {
                                throw new SQLException("Index " + indexName + " does not exist on table " + tableName);
                            }
                        }
                    }
                }
            }
        }

        public void foreignKeyShouldExistOnTable(String fkName, String tableName) throws SQLException {
            if (conn == null || conn.isClosed()) {
                throw new SQLException("No database connection");
            }

            String sql = "SELECT constraint_name FROM information_schema.table_constraints WHERE table_schema = 'public' AND table_name = ? AND constraint_name = ? AND constraint_type = 'FOREIGN KEY'";
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, tableName);
                pstmt.setString(2, fkName);
                try (ResultSet rs = pstmt.executeQuery()) {
                    if (!rs.next()) {
                        throw new SQLException("Foreign key " + fkName + " does not exist on table " + tableName);
                    }
                }
            }
        }

        public void foreignKeyShouldReferenceTableOnColumn(String refTable, String refColumn, String fkName) throws SQLException {
            if (conn == null || conn.isClosed()) {
                throw new SQLException("No database connection");
            }

            String sql = "SELECT tc.constraint_name, kcu.column_name, ccu.table_name AS foreign_table_name, ccu.column_name AS foreign_column_name " +
                    "FROM information_schema.table_constraints AS tc " +
                    "JOIN information_schema.key_column_usage AS kcu ON tc.constraint_name = kcu.constraint_name AND tc.table_schema = kcu.table_schema " +
                    "JOIN information_schema.constraint_column_usage AS ccu ON ccu.constraint_name = tc.constraint_name AND ccu.table_schema = tc.table_schema " +
                    "WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.constraint_name = ?";
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, fkName);
                try (ResultSet rs = pstmt.executeQuery()) {
                    if (!rs.next()) {
                        throw new SQLException("Could not find constraint information for " + fkName);
                    }

                    String foreignTable = rs.getString("foreign_table_name");
                    String foreignColumn = rs.getString("foreign_column_name");

                    if (!foreignTable.equals(refTable)) {
                        throw new SQLException("Expected foreign key to reference table " + refTable + " but got " + foreignTable);
                    }

                    if (!foreignColumn.equals(refColumn)) {
                        throw new SQLException("Expected foreign key to reference column " + refColumn + " but got " + foreignColumn);
                    }
                }
            }
        }

        public void tableHasUniqueConstraintOnColumn(String tableName, String columnName) throws SQLException {
            if (conn == null || conn.isClosed()) {
                throw new SQLException("No database connection");
            }

            String sql = "SELECT tc.constraint_name " +
                    "FROM information_schema.table_constraints tc " +
                    "JOIN information_schema.constraint_column_usage ccu " +
                    "ON tc.constraint_name = ccu.constraint_name " +
                    "WHERE tc.table_schema = 'public' " +
                    "AND tc.table_name = ? " +
                    "AND ccu.column_name = ? " +
                    "AND tc.constraint_type IN ('UNIQUE', 'PRIMARY KEY')";
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, tableName);
                pstmt.setString(2, columnName);
                try (ResultSet rs = pstmt.executeQuery()) {
                    if (!rs.next()) {
                        throw new SQLException("Table " + tableName + " does not have a unique constraint on column " + columnName);
                    }
                }
            }
        }

        public void columnIsNotNull(String tableColumn) throws SQLException {
            if (conn == null || conn.isClosed()) {
                throw new SQLException("No database connection");
            }

            // Split table.column format
            String[] parts = tableColumn.split("\\.");
            String tableName = parts[0];
            String columnName = parts[1];

            String sql = "SELECT is_nullable FROM information_schema.columns WHERE table_schema = 'public' AND table_name = ? AND column_name = ?";
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, tableName);
                pstmt.setString(2, columnName);
                try (ResultSet rs = pstmt.executeQuery()) {
                    if (!rs.next()) {
                        throw new SQLException("Column " + tableColumn + " not found");
                    }

                    String isNullable = rs.getString("is_nullable");
                    if (isNullable.equals("YES")) {
                        throw new SQLException("Column " + tableColumn + " is nullable but expected NOT NULL");
                    }
                }
            }
        }

        public void columnHasDefaultValue(String tableColumn, String expectedDefault) throws SQLException {
            if (conn == null || conn.isClosed()) {
                throw new SQLException("No database connection");
            }

            // Split table.column format
            String[] parts = tableColumn.split("\\.");
            String tableName = parts[0];
            String columnName = parts[1];

            String sql = "SELECT column_default FROM information_schema.columns WHERE table_schema = 'public' AND table_name = ? AND column_name = ?";
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, tableName);
                pstmt.setString(2, columnName);
                try (ResultSet rs = pstmt.executeQuery()) {
                    if (!rs.next()) {
                        throw new SQLException("Column " + tableColumn + " not found");
                    }

                    String columnDefault = rs.getString("column_default");
                    if (columnDefault == null) {
                        throw new SQLException("No default value for " + tableColumn);
                    }

                    if (!columnDefault.toLowerCase().contains(expectedDefault.toLowerCase())) {
                        throw new SQLException("Expected default value containing " + expectedDefault + 
                                " for " + tableColumn + " but got " + columnDefault);
                    }
                }
            }
        }

        public void columnIsOfType(String tableColumn, String expectedType) throws SQLException {
            if (conn == null || conn.isClosed()) {
                throw new SQLException("No database connection");
            }

            // Split table.column format
            String[] parts = tableColumn.split("\\.");
            String tableName = parts[0];
            String columnName = parts[1];

            String sql = "SELECT data_type, udt_name FROM information_schema.columns WHERE table_schema = 'public' AND table_name = ? AND column_name = ?";
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, tableName);
                pstmt.setString(2, columnName);
                try (ResultSet rs = pstmt.executeQuery()) {
                    if (!rs.next()) {
                        throw new SQLException("Column " + tableColumn + " not found");
                    }

                    String dataType = rs.getString("data_type");
                    String udtName = rs.getString("udt_name");

                    // Special case for serial
                    if (expectedType.equalsIgnoreCase("serial")) {
                        // Check if it's an integer column with a sequence
                        if (dataType.equals("integer")) {
                            String sequenceQuery = "SELECT pg_get_serial_sequence(?, ?) IS NOT NULL as has_sequence";
                            try (PreparedStatement seqStmt = conn.prepareStatement(sequenceQuery)) {
                                seqStmt.setString(1, tableName);
                                seqStmt.setString(2, columnName);
                                try (ResultSet seqRs = seqStmt.executeQuery()) {
                                    if (!seqRs.next() || !seqRs.getBoolean("has_sequence")) {
                                        throw new SQLException("Column " + tableColumn + " does not have a sequence (not a serial type)");
                                    }
                                }
                            }
                            return;
                        }
                    }

                    if (!dataType.equalsIgnoreCase(expectedType) && !udtName.equalsIgnoreCase(expectedType)) {
                        throw new SQLException("Expected data type " + expectedType + " for " + tableColumn + " but got " + dataType);
                    }
                }
            }
        }

        public void foreignKeyHasOnDeleteCascade(String fkName) throws SQLException {
            if (conn == null || conn.isClosed()) {
                throw new SQLException("No database connection");
            }

            String sql = "SELECT delete_rule FROM information_schema.referential_constraints WHERE constraint_name = ?";
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                pstmt.setString(1, fkName);
                try (ResultSet rs = pstmt.executeQuery()) {
                    if (!rs.next()) {
                        throw new SQLException("Constraint " + fkName + " not found");
                    }

                    String deleteRule = rs.getString("delete_rule");
                    if (!deleteRule.equals("CASCADE")) {
                        throw new SQLException("Constraint " + fkName + " doesn't have CASCADE delete rule, found: " + deleteRule);
                    }
                }
            }
        }
    }

    // Helper methods
    private boolean recordExists(String table, String column, String value) throws SQLException {
        if (connection == null) {
            throw new SQLException("No database connection");
        }

        String sql = String.format("SELECT COUNT(*) FROM %s WHERE %s = ?", table, column);
        try (PreparedStatement pstmt = connection.prepareStatement(sql)) {
            pstmt.setString(1, value);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt(1) > 0;
                }
                return false;
            }
        }
    }

    private void insertIfNotExists(String table, String column, String value) throws SQLException {
        if (connection == null) {
            throw new SQLException("No database connection");
        }

        if (!recordExists(table, column, value)) {
            String insertSql = String.format("INSERT INTO %s(%s) VALUES(?)", table, column);
            try (PreparedStatement insertStmt = connection.prepareStatement(insertSql)) {
                insertStmt.setString(1, value);
                insertStmt.executeUpdate();
            }
        }
    }

    // Step definitions
    @Given("a PostgreSQL database is running")
    public void postgresqlDatabaseIsRunning() {
        // This step is a precondition - no implementation needed
    }

    @Given("I set DB host to {string}")
    public void setDbHost(String hostValue) {
        host = hostValue;
    }

    @Given("I set DB port to {string}")
    public void setDbPort(String portValue) {
        port = portValue;
    }

    @Given("I set DB user to {string}")
    public void setDbUser(String userValue) {
        user = userValue;
    }

    @Given("I set DB password to {string}")
    public void setDbPassword(String passwordValue) {
        password = passwordValue;
    }

    @Given("I set DB name to {string}")
    public void setDbName(String dbnameValue) {
        dbname = dbnameValue;
    }

    @Given("I have a valid connection to the database")
    public void validConnectionToDatabase() throws SQLException {
        pgTest.connectToDB();
    }

    @When("I connect to the database")
    public void connectToDatabase() throws SQLException {
        if (connection != null) {
            try {
                pgTest.closeDB();
            } catch (SQLException e) {
                System.err.println("Error closing existing connection: " + e.getMessage());
            }
        }
        pgTest.connectToDB();
    }

    @Then("the connection should be successful")
    public void connectionShouldBeSuccessful() {
        if (!connectionResult) {
            throw new AssertionError("Database connection failed");
        }
    }

    @When("I execute the following SQL:")
    public void executeSQL(String sql) throws SQLException {
        if (connection == null) {
            throw new SQLException("No database connection");
        }
        
        try (Statement stmt = connection.createStatement()) {
            boolean isQuery = stmt.execute(sql);
            if (isQuery) {
                queryResult = stmt.getResultSet();
            } else {
                queryResult = null;
            }
        }
    }

    @When("I insert a record into {string} where {string} is {string}")
    public void insertRecord(String table, String column, String value) throws SQLException {
        insertIfNotExists(table, column, value);
    }

    @Then("the record in {string} where {string} is {string} should exist")
    public void recordShouldExist(String table, String column, String value) throws SQLException {
        if (!recordExists(table, column, value)) {
            throw new AssertionError("Record not found in " + table + " where " + column + " = " + value);
        }
    }

    @When("I insert a record into {string} linked to {string} by {string} where {string} is {string}")
    public void insertLinkedRecord(String targetTable, String sourceTable, String foreignKey, String sourceColumn, String sourceValue) throws SQLException {
        if (connection == null) {
            throw new SQLException("No database connection");
        }
        
        // First, find the primary key of the source record
        String findQuery = String.format("SELECT id FROM %s WHERE %s = ?", sourceTable, sourceColumn);
        try (PreparedStatement pstmt = connection.prepareStatement(findQuery)) {
            pstmt.setString(1, sourceValue);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (!rs.next()) {
                    throw new SQLException("Source record not found in " + sourceTable + " where " + sourceColumn + " = " + sourceValue);
                }
                
                int sourceId = rs.getInt("id");
                
                // Insert the linked record
                String insertQuery = String.format("INSERT INTO %s (%s) VALUES (?)", targetTable, foreignKey);
                try (PreparedStatement insertStmt = connection.prepareStatement(insertQuery)) {
                    insertStmt.setInt(1, sourceId);
                    insertStmt.executeUpdate();
                }
            }
        }
    }

    @Then("the record in {string} linked to {string} by {string} where {string} is {string} should exist")
    public void linkedRecordShouldExist(String targetTable, String sourceTable, String foreignKey, String sourceColumn, String sourceValue) throws SQLException {
        if (connection == null) {
            throw new SQLException("No database connection");
        }
        
        // First, find the source record ID
        String findSourceQuery = String.format("SELECT id FROM %s WHERE %s = ?", sourceTable, sourceColumn);
        try (PreparedStatement pstmt = connection.prepareStatement(findSourceQuery)) {
            pstmt.setString(1, sourceValue);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (!rs.next()) {
                    throw new SQLException("Source record not found in " + sourceTable + " where " + sourceColumn + " = " + sourceValue);
                }
                
                int sourceId = rs.getInt("id");
                
                // Check if there's a linked record
                String findLinkedQuery = String.format("SELECT * FROM %s WHERE %s = ?", targetTable, foreignKey);
                try (PreparedStatement linkedStmt = connection.prepareStatement(findLinkedQuery)) {
                    linkedStmt.setInt(1, sourceId);
                    try (ResultSet linkedRs = linkedStmt.executeQuery()) {
                        if (!linkedRs.next()) {
                            throw new AssertionError("Linked record not found in " + targetTable + " where " + foreignKey + " = " + sourceId);
                        }
                    }
                }
            }
        }
    }

    @When("I inspect the constraint")
    public void inspectConstraint() {
        // This is a placeholder step - actual implementation depends on specific constraint
    }

    @Then("the following tables should exist:")
    public void tablesShouldExist(DataTable table) throws SQLException {
        pgTest.tablesShouldExist(table);
    }

    @Then("the following columns should exist in {string}:")
    public void columnsShouldExistInTable(String tableName, DataTable columnsTable) throws SQLException {
        pgTest.columnsShouldExistInTable(tableName, columnsTable);
    }

    @Then("the following indexes should exist on {string}:")
    public void indexesShouldExistOnTable(String tableName, DataTable indexesTable) throws SQLException {
        pgTest.indexesShouldExistOnTable(tableName, indexesTable);
    }

    @Then("the foreign key {string} should exist on {string}")
    public void foreignKeyShouldExistOnTable(String fkName, String tableName) throws SQLException {
        pgTest.foreignKeyShouldExistOnTable(fkName, tableName);
    }

    @Then("it should reference the {string} table on column {string} with constraint {string}")
    public void referenceTableOnColumn(String refTable, String refColumn, String fkName) throws SQLException {
        pgTest.foreignKeyShouldReferenceTableOnColumn(refTable, refColumn, fkName);
    }

    @Given("the {string} table has a unique constraint on {string}")
    public void tableHasUniqueConstraintOnColumn(String tableName, String columnName) throws SQLException {
        pgTest.tableHasUniqueConstraintOnColumn(tableName, columnName);
    }

    @Given("the {string} column is NOT NULL")
    public void columnIsNotNull(String tableColumn) throws SQLException {
        pgTest.columnIsNotNull(tableColumn);
    }

    @Given("the {string} column has a default value of {string}")
    public void columnHasDefaultValue(String tableColumn, String expectedDefault) throws SQLException {
        pgTest.columnHasDefaultValue(tableColumn, expectedDefault);
    }

    @Given("the {string} column is of type {string}")
    public void columnIsOfType(String tableColumn, String expectedType) throws SQLException {
        pgTest.columnIsOfType(tableColumn, expectedType);
    }

    @Given("the foreign key {string} has ON DELETE CASCADE behavior")
    public void foreignKeyHasOnDeleteCascade(String fkName) throws SQLException {
        pgTest.foreignKeyHasOnDeleteCascade(fkName);
    }

    @When("I rollback the transaction")
    public void rollbackTransaction() throws SQLException {
        pgTest.rollbackTransaction();
    }

    // Hooks
    @Before
    public void beforeScenario() throws SQLException {
        // Make sure we have a clean connection
        if (pgTest.conn != null) {
            try {
                pgTest.closeDB();
            } catch (SQLException e) {
                System.err.println("Error closing connection: " + e.getMessage());
            }
        }

        // Initialize default values if not already set
        if (host == null) host = getEnvOrDefault("DB_HOST", "localhost");
        if (port == null) port = getEnvOrDefault("DB_PORT", "5433");
        if (user == null) user = getEnvOrDefault("DB_USER", "postgres");
        if (password == null) password = getEnvOrDefault("DB_PASSWORD", "postgres");
        if (dbname == null) dbname = getEnvOrDefault("DB_NAME", "demo_db");
    }

    @After
    public void afterScenario() throws SQLException {
        // Close the database connection
        try {
            pgTest.closeDB();
        } catch (SQLException e) {
            System.err.println("Error closing connection: " + e.getMessage());
        }
    }
}
