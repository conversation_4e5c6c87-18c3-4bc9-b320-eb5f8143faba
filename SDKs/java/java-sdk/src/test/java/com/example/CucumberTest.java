package com.example;

import io.cucumber.junit.Cucumber;
import io.cucumber.junit.CucumberOptions;
import org.junit.AfterClass;
import org.junit.BeforeClass;
import org.junit.runner.JUnitCore;
import org.junit.runner.Result;
import org.junit.runner.RunWith;
import org.junit.runner.notification.Failure;

import java.util.Arrays;

/**
 * Main Cucumber test runner that supports tag filtering to run DB, API, or MQ tests.
 * Examples:
 * - Run DB tests: mvn test -Dcucumber.filter.tags="@db"
 * - Run API tests: mvn test -Dcucumber.filter.tags="@api"
 * - Run MQ tests: mvn test -Dcucumber.filter.tags="@mq"
 * - Run multiple test types: mvn test -Dcucumber.filter.tags="@api or @db"
 * - Run with exclusions: mvn test -Dcucumber.filter.tags="@api and not @slow"
 */
@RunWith(Cucumber.class)
@CucumberOptions(
    features = "src/test/resources/features",
    glue = {"com.example.stepdefinitions"},
    plugin = {
        "pretty", 
        "html:target/cucumber-reports/cucumber-report.html",
        "json:target/cucumber-reports/cucumber.json"
    },
    monochrome = true
)
public class CucumberTest {
    
    /**
     * Main method to run Cucumber tests from command line with tag filtering.
     * Usage examples:
     * java -cp ... -Dcucumber.filter.tags="@db" com.example.CucumberTest
     * java -cp ... -Dcucumber.filter.tags="@api" com.example.CucumberTest
     * java -cp ... -Dcucumber.filter.tags="@mq" com.example.CucumberTest
     */
    public static void main(String[] args) {
        // Check for tags in system properties or environment variables
        String tags = System.getProperty("cucumber.filter.tags");
        
        if (tags == null || tags.isEmpty()) {
            tags = System.getenv("CUCUMBER_TAGS");
        }
        
        if (tags == null || tags.isEmpty()) {
            System.err.println("Error: No tags specified. Please provide tags using -Dcucumber.filter.tags or CUCUMBER_TAGS environment variable.");
            System.err.println("Examples:");
            System.err.println("  -Dcucumber.filter.tags=\"@db\"    (run database tests)");
            System.err.println("  -Dcucumber.filter.tags=\"@api\"   (run API tests)");
            System.err.println("  -Dcucumber.filter.tags=\"@mq\"    (run message queue tests)");
            System.exit(1);
        }
        
        // Set the tags system property if it wasn't set via command line
        if (System.getProperty("cucumber.filter.tags") == null) {
            System.setProperty("cucumber.filter.tags", tags);
        }
        
        // Set the glue path explicitly
        System.setProperty("cucumber.glue", "com.example.stepdefinitions");
        
        // Print out class loader info for debugging
        System.out.println("Class path: " + System.getProperty("java.class.path"));
        System.out.println("Looking for step definitions in: com.example.stepdefinitions");
        
        System.out.println("Filtering tests with tags: " + tags);
        
        // Run the Cucumber tests
        Result result = JUnitCore.runClasses(CucumberTest.class);
        
        // Print failures
        for (Failure failure : result.getFailures()) {
            System.err.println(failure.toString());
        }
        
        // Exit with appropriate code
        System.exit(result.wasSuccessful() ? 0 : 1);
    }
    
    @BeforeClass
    public static void setup() {
        System.out.println("========================================");
        System.out.println("Starting Cucumber tests...");
        
        // Print which types of tests will be run based on tags
        String tags = System.getProperty("cucumber.filter.tags", "");
        if (tags.contains("@db")) {
            System.out.println("DB tests will be included");
            
            // Check DB environment variables
            System.out.println("DB_HOST: " + getEnvOrDefault("DB_HOST", "not set"));
            System.out.println("DB_PORT: " + getEnvOrDefault("DB_PORT", "not set"));
            System.out.println("DB_NAME: " + getEnvOrDefault("DB_NAME", "not set"));
        }
        if (tags.contains("@api")) {
            System.out.println("API tests will be included");
        }
        if (tags.contains("@mq")) {
            System.out.println("Message Queue tests will be included");
        }
        System.out.println("========================================");
    }
    
    private static String getEnvOrDefault(String key, String defaultValue) {
        String value = System.getenv(key);
        return (value != null && !value.isEmpty()) ? value : defaultValue;
    }
    
    @AfterClass
    public static void teardown() {
        System.out.println("========================================");
        System.out.println("Cucumber tests completed.");
        System.out.println("========================================");
    }
}
