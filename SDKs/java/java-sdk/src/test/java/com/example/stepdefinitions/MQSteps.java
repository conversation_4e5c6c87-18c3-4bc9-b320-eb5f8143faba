package com.example.stepdefinitions;

import io.cucumber.java.en.Given;
import io.cucumber.java.en.When;
import io.cucumber.java.en.Then;
import io.cucumber.docstring.DocString;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.json.JSONObject;
import org.json.JSONException;

public class MQSteps {
    // State for MQ tests
    private boolean messagingAvailable = false;
    private final List<String> configuredQueues = new ArrayList<>();
    private final Map<String, List<Map<String, Object>>> messages = new ConcurrentHashMap<>();
    private final List<Map<String, Object>> sentMessages = new ArrayList<>();
    private final List<Map<String, Object>> receivedMessages = new ArrayList<>();
    private final Map<String, String> subscriberFilters = new HashMap<>();
    private int retryCount = 0;
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    // Helper functions
    private Object parseJsonOrRaw(String payload) {
        try {
            return new JSONObject(payload);
        } catch (JSONException e) {
            return payload;
        }
    }

    @Given("the messaging system is available")
    public void theMessagingSystemIsAvailable() {
        messagingAvailable = true;
        System.out.println("Messaging system is available");
    }

    @Given("the {string} topic\\/queue is configured")
    public void theTopicQueueIsConfigured(String queue) {
        configuredQueues.add(queue);
        messages.put(queue, Collections.synchronizedList(new ArrayList<>()));
        System.out.printf("Queue '%s' configured%n", queue);
    }

    @Given("the {string} queue is configured")
    public void theQueueIsConfigured(String queue) {
        configuredQueues.add(queue);
        messages.put(queue, Collections.synchronizedList(new ArrayList<>()));
        System.out.printf("Queue '%s' configured%n", queue);
    }

    @Given("the {string} queue is configured for failed messages")
    public void theQueueIsConfiguredForFailedMessages(String queue) {
        configuredQueues.add(queue);
        messages.put(queue, Collections.synchronizedList(new ArrayList<>()));
        System.out.printf("Failed message queue '%s' configured%n", queue);
    }

    @Given("messages with IDs {string}, {string}, {string}, {string} are sent to {string}")
    public void messagesWithIDsAreSentTo(String id1, String id2, String id3, String id4, String queueName) {
        String[] ids = {id1, id2, id3, id4};
        Set<String> seen = new HashSet<>();

        if (!messages.containsKey(queueName)) {
            messages.put(queueName, Collections.synchronizedList(new ArrayList<>()));
        }

        List<Map<String, Object>> queue = messages.get(queueName);

        for (String id : ids) {
            if (seen.contains(id)) {
                continue;
            }

            Map<String, Object> message = new HashMap<>();
            message.put("id", id);
            message.put("payload", Map.of("testData", "Message " + id));
            message.put("headers", Map.of("Content-Type", "application/json"));

            sentMessages.add(message);
            queue.add(message);
            seen.add(id);
        }
    }

    @Given("a message with ID {string} is sent to {string}")
    public void aMessageWithIDIsSentTo(String id, String queueName) {
        Map<String, Object> message = new HashMap<>();
        message.put("id", id);
        message.put("payload", Map.of("testData", "Message " + id));
        message.put("headers", Map.of("Content-Type", "application/json"));

        sentMessages.add(message);

        if (!messages.containsKey(queueName)) {
            messages.put(queueName, Collections.synchronizedList(new ArrayList<>()));
        }
        messages.get(queueName).add(message);
    }

    @Given("the consumer fails to process it initially")
    public void theConsumerFailsToProcessItInitially() {
        retryCount = 0;
        System.out.println("Consumer failed to process message initially");
    }

    @Given("a message with TTL of {int} seconds is sent to {string}")
    public void aMessageWithTTLOfSecondsIsSentTo(int ttlSec, String queueName) {
        String ttlMessageId = System.getenv("TTL_MESSAGE_ID") != null ? 
                              System.getenv("TTL_MESSAGE_ID") : "ttl-test";
        
        long expiresAt = System.currentTimeMillis() + (ttlSec * 1000L);
        
        Map<String, Object> message = new HashMap<>();
        message.put("id", ttlMessageId);
        message.put("payload", Map.of("testData", "TTL Test Message"));
        message.put("headers", Map.of("Content-Type", "application/json"));
        message.put("ttl", ttlSec * 1000);
        message.put("expiresAt", expiresAt);

        sentMessages.add(message);
        
        if (!messages.containsKey(queueName)) {
            messages.put(queueName, Collections.synchronizedList(new ArrayList<>()));
        }
        messages.get(queueName).add(message);
        
        System.out.printf(" Sent message to %s with TTL %d sec%n", queueName, ttlSec);
    }

    @Given("it is not consumed within {int} seconds")
    public void itIsNotConsumedWithinSeconds(int seconds) throws InterruptedException {
        // Simulate time passing without consumption
        long now = System.currentTimeMillis() + (seconds * 1000L);
        
        messages.forEach((queueName, queueMessages) -> {
            List<Map<String, Object>> updatedMessages = new ArrayList<>();
            
            for (Map<String, Object> msg : queueMessages) {
                if (msg.containsKey("expiresAt")) {
                    Long expiresAt = (Long) msg.get("expiresAt");
                    if (expiresAt <= now) {
                        // Message has expired, don't include it
                        continue;
                    }
                }
                updatedMessages.add(msg);
            }
            
            messages.put(queueName, updatedMessages);
        });
        
        System.out.printf("Waited %d seconds, expired messages removed%n", seconds);
    }

    @Given("a subscriber with filter {string} is listening on {string}")
    public void aSubscriberWithFilterIsListeningOn(String filter, String queueName) {
        subscriberFilters.put(queueName, filter);
        System.out.printf("Subscriber with filter '%s' listening on %s%n", filter, queueName);
    }

    @When("a message with payload:")
    public void aMessageWithPayload(DocString payload) {
        Object parsedPayload = parseJsonOrRaw(payload.getContent());
        
        Map<String, Object> message = new HashMap<>();
        message.put("payload", parsedPayload);
        message.put("headers", Map.of("Content-Type", "application/json"));
        
        sentMessages.add(message);
    }

    @When("the message is sent to {string}")
    public void theMessageIsSentToQueue(String queueName) {
        if (sentMessages.isEmpty()) {
            throw new IllegalStateException("No message available to send");
        }

        Map<String, Object> lastMessage = sentMessages.get(sentMessages.size() - 1);
        
        // Append message to the queue
        if (!messages.containsKey(queueName)) {
            messages.put(queueName, Collections.synchronizedList(new ArrayList<>()));
        }

        messages.get(queueName).add(lastMessage);
        System.out.printf(" Sent message to %s: %s%n", queueName, lastMessage.get("payload"));
    }

    @When("a message with status {string} is sent to {string}")
    public void aMessageWithStatusIsSentTo(String status, String queueName) {
        Map<String, Object> message = new HashMap<>();
        Map<String, String> payload = new HashMap<>();
        payload.put("status", status);
        
        message.put("payload", payload);
        message.put("headers", Map.of("Content-Type", "application/json"));
        
        sentMessages.add(message);
        
        if (!messages.containsKey(queueName)) {
            messages.put(queueName, Collections.synchronizedList(new ArrayList<>()));
        }
        messages.get(queueName).add(message);
    }

    @When("a signed and encrypted message is sent to {string}")
    public void aSignedAndEncryptedMessageIsSentTo(String queueName) {
        Map<String, Object> message = new HashMap<>();
        Map<String, String> payload = new HashMap<>();
        payload.put("secureData", "protected content");
        
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("X-Signature", "valid-signature");
        headers.put("X-Encryption", "encrypted");
        
        message.put("payload", payload);
        message.put("headers", headers);
        message.put("isEncrypted", true);
        message.put("isSigned", true);
        
        sentMessages.add(message);
        
        if (!messages.containsKey(queueName)) {
            messages.put(queueName, Collections.synchronizedList(new ArrayList<>()));
        }
        messages.get(queueName).add(message);
    }

    @Then("a message should be received from {string}")
    public void aMessageShouldBeReceivedFrom(String queueName) {
        if (!messages.containsKey(queueName) || messages.get(queueName).isEmpty()) {
            throw new AssertionError(String.format("No messages available in queue %s", queueName));
        }

        // Simulate receiving the first message
        Map<String, Object> message = messages.get(queueName).get(0);
        receivedMessages.add(message);
        
        System.out.printf(" Received message from %s: %s%n", queueName, message.get("payload"));
    }

    @Then("the message payload should include {string} = {string}")
    public void theMessagePayloadShouldInclude(String field, String value) {
        if (receivedMessages.isEmpty()) {
            throw new AssertionError("No message has been received");
        }
        
        Map<String, Object> lastMessage = receivedMessages.get(receivedMessages.size() - 1);
        
        if (!lastMessage.containsKey("payload")) {
            throw new AssertionError("Message has no payload");
        }
        
        Object payload = lastMessage.get("payload");
        
        if (payload instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> payloadMap = (Map<String, Object>) payload;
            
            if (!payloadMap.containsKey(field)) {
                throw new AssertionError(String.format("Field %s not found in message payload", field));
            }
            
            String actualValue = payloadMap.get(field).toString();
            if (!actualValue.equals(value)) {
                throw new AssertionError(String.format(
                    "Field %s has incorrect value. Expected: %s, Got: %s", 
                    field, value, actualValue));
            }
        } else if (payload instanceof JSONObject) {
            JSONObject jsonPayload = (JSONObject) payload;
            
            if (!jsonPayload.has(field)) {
                throw new AssertionError(String.format("Field %s not found in message payload", field));
            }
            
            String actualValue = jsonPayload.get(field).toString();
            if (!actualValue.equals(value)) {
                throw new AssertionError(String.format(
                    "Field %s has incorrect value. Expected: %s, Got: %s", 
                    field, value, actualValue));
            }
        } else {
            throw new AssertionError("Payload is not a map or JSON object");
        }
    }

    @Then("the message header {string} should be {string}")
    public void theMessageHeaderShouldBe(String header, String expectedValue) {
        if (receivedMessages.isEmpty()) {
            throw new AssertionError("No message has been received");
        }
        
        Map<String, Object> lastMessage = receivedMessages.get(receivedMessages.size() - 1);
        
        if (!lastMessage.containsKey("headers")) {
            throw new AssertionError("Message has no headers");
        }
        
        @SuppressWarnings("unchecked")
        Map<String, String> headers = (Map<String, String>) lastMessage.get("headers");
        
        if (!headers.containsKey(header)) {
            throw new AssertionError(String.format("Header %s not found in message headers", header));
        }
        
        String actualValue = headers.get(header);
        if (!actualValue.equals(expectedValue)) {
            throw new AssertionError(String.format(
                "Header %s has incorrect value. Expected: %s, Got: %s", 
                header, expectedValue, actualValue));
        }
    }

    @Then("messages received from {string} should be in order: {string}, {string}, {string}")
    public void messagesReceivedFromShouldBeInOrder(String queueName, String id1, String id2, String id3) {
        String[] expectedOrder = {id1, id2, id3};
        
        if (!messages.containsKey(queueName)) {
            throw new AssertionError(String.format("No messages in queue %s", queueName));
        }
        
        List<Map<String, Object>> queue = messages.get(queueName);
        
        // Filter only messages that have an 'id'
        List<Map<String, Object>> filteredMessages = new ArrayList<>();
        for (Map<String, Object> msg : queue) {
            if (msg.containsKey("id")) {
                filteredMessages.add(msg);
            }
        }
        
        receivedMessages.clear();
        receivedMessages.addAll(filteredMessages);
        
        if (filteredMessages.size() < expectedOrder.length) {
            throw new AssertionError(String.format(
                "Not enough messages with IDs in queue %s. Expected: %d, Got: %d", 
                queueName, expectedOrder.length, filteredMessages.size()));
        }
        
        for (int i = 0; i < expectedOrder.length; i++) {
            Map<String, Object> msg = receivedMessages.get(i);
            String actualId = (String) msg.get("id");
            
            if (!actualId.equals(expectedOrder[i])) {
                throw new AssertionError(String.format(
                    "Message at position %d has incorrect ID. Expected: %s, Got: %s", 
                    i, expectedOrder[i], actualId));
            }
        }
    }

    @Then("no duplicate messages should be received")
    public void noDuplicateMessagesShouldBeReceived() {
        List<String> messageIds = new ArrayList<>();
        Set<String> uniqueIds = new HashSet<>();
        
        for (Map<String, Object> msg : receivedMessages) {
            if (msg.containsKey("id")) {
                String id = (String) msg.get("id");
                messageIds.add(id);
                uniqueIds.add(id);
            }
        }
        
        if (messageIds.size() != uniqueIds.size()) {
            throw new AssertionError("Duplicate messages were received");
        }
    }

    @Then("the message should be retried up to {int} times")
    public void theMessageShouldBeRetriedUpToTimes(int maxRetries) {
        // Simulate message retry process
        retryCount = maxRetries;
        
        if (retryCount > maxRetries) {
            throw new AssertionError(String.format(
                "Message retry count exceeded maximum. Got: %d, Max: %d", 
                retryCount, maxRetries));
        }
    }

    @Then("the consumer should handle it idempotently")
    public void theConsumerShouldHandleItIdempotently() {
        // In a real implementation, this would verify idempotent behavior
        // For now, we're just acknowledging the requirement
    }

    @Then("it should be expired and not delivered from {string}")
    public void itShouldBeExpiredAndNotDelivered(String queueName) {
        if (!messages.containsKey(queueName)) {
            return; // Queue doesn't exist, so message is not there (pass)
        }
        
        List<Map<String, Object>> queue = messages.get(queueName);
        
        for (Map<String, Object> msg : queue) {
            if (msg.containsKey("id") && "ttl-test".equals(msg.get("id"))) {
                throw new AssertionError("Expired message was not removed from the queue");
            }
        }
    }

    @Then("the message should be delivered to the subscriber")
    public void theMessageShouldBeDeliveredToTheSubscriber() {
        // In a real implementation, this would check if the message passed the filter
        // For now, we're just acknowledging the requirement
    }

    @Then("the message should not be delivered to the subscriber")
    public void theMessageShouldNotBeDeliveredToTheSubscriber() {
        // In a real implementation, this would check if the message was filtered out
        // For now, we're just acknowledging the requirement
    }

    @Then("the message should be decrypted and verified successfully")
    public void theMessageShouldBeDecryptedAndVerifiedSuccessfully() {
        if (sentMessages.isEmpty()) {
            throw new AssertionError("No messages have been sent");
        }
        
        Map<String, Object> lastMessage = sentMessages.get(sentMessages.size() - 1);
        
        if (!Boolean.TRUE.equals(lastMessage.get("isEncrypted"))) {
            throw new AssertionError("Message was not encrypted");
        }
        
        if (!Boolean.TRUE.equals(lastMessage.get("isSigned"))) {
            throw new AssertionError("Message was not signed");
        }
        
        // In a real implementation, this would actually decrypt and verify
    }

    @Then("the message should be routed to {string}")
    public void theMessageShouldBeRoutedTo(String queueName) {
        if (!messages.containsKey(queueName) || messages.get(queueName).isEmpty()) {
            throw new AssertionError(String.format("Message not routed to %s", queueName));
        }
    }
}
