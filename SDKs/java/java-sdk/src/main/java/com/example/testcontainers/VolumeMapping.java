package com.example.testcontainers;

import java.util.Objects;

/**
 * Represents a volume mapping between a host path and a container path.
 * This class is immutable.
 */
public class VolumeMapping {
    private final String hostPath;
    private final String containerPath;

    /**
     * Creates a new volume mapping.
     *
     * @param hostPath      The path on the host machine
     * @param containerPath The path inside the container
     * @throws NullPointerException if either path is null
     */
    public VolumeMapping(String hostPath, String containerPath) {
        this.hostPath = Objects.requireNonNull(hostPath, "Host path cannot be null");
        this.containerPath = Objects.requireNonNull(containerPath, "Container path cannot be null");
    }

    /**
     * @return The path on the host machine
     */
    public String getHostPath() {
        return hostPath;
    }

    /**
     * @return The path inside the container
     */
    public String getContainerPath() {
        return containerPath;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        VolumeMapping that = (VolumeMapping) o;
        return Objects.equals(hostPath, that.hostPath) &&
               Objects.equals(containerPath, that.containerPath);
    }

    @Override
    public int hashCode() {
        return Objects.hash(hostPath, containerPath);
    }

    @Override
    public String toString() {
        return hostPath + ":" + containerPath;
    }
}