package com.example.testcontainers;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.containers.Network;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Utility class for managing Docker networks.
 * This class provides methods to create, retrieve, and clean up Docker networks.
 */
public final class CustomNetwork {
    private static final Logger LOGGER = LoggerFactory.getLogger(CustomNetwork.class);
    private static final Map<String, Network> NETWORK_CACHE = new ConcurrentHashMap<>();

    private CustomNetwork() {
        // Prevent instantiation
        throw new AssertionError("No instances of CustomNetwork allowed");
    }

    /**
     * Gets or creates a Docker network with the given name.
     *
     * @param networkName the network name
     * @return the network
     * @throws NullPointerException if networkName is null
     * @throws IllegalArgumentException if networkName is empty
     */
    public static Network getOrCreateNetwork(String networkName) {
        Objects.requireNonNull(networkName, "Network name cannot be null");
        if (networkName.isEmpty()) {
            throw new IllegalArgumentException("Network name cannot be empty");
        }

        return NETWORK_CACHE.computeIfAbsent(networkName, name -> {
            LOGGER.info("Creating new Docker network: {}", name);
            Network network = Network.newNetwork();
            return network;
        });
    }

    /**
     * Creates a Docker network with the given name.
     *
     * @param networkName the network name
     * @return the network name
     * @throws NullPointerException if networkName is null
     * @throws IllegalArgumentException if networkName is empty
     */
    public static String createNetwork(String networkName) {
        getOrCreateNetwork(networkName);
        return networkName;
    }
    
    /**
     * Removes a Docker network with the given name.
     *
     * @param networkName the network name
     * @throws NullPointerException if networkName is null
     */
    public static void removeNetwork(String networkName) {
        Objects.requireNonNull(networkName, "Network name cannot be null");
        
        Network network = NETWORK_CACHE.remove(networkName);
        if (network != null) {
            try {
                LOGGER.info("Removing Docker network: {}", networkName);
                network.close();
            } catch (Exception e) {
                LOGGER.warn("Failed to remove Docker network: {}", networkName, e);
            }
        }
    }
    
    /**
     * Removes all cached Docker networks.
     * This should be called during test cleanup.
     */
    public static void removeAllNetworks() {
        LOGGER.info("Removing all Docker networks");
        
        for (Map.Entry<String, Network> entry : NETWORK_CACHE.entrySet()) {
            try {
                LOGGER.info("Removing Docker network: {}", entry.getKey());
                entry.getValue().close();
            } catch (Exception e) {
                LOGGER.warn("Failed to remove Docker network: {}", entry.getKey(), e);
            }
        }
        
        NETWORK_CACHE.clear();
    }
    
    /**
     * Gets all cached network names.
     *
     * @return an unmodifiable map of network names to networks
     */
    public static Map<String, Network> getAllNetworks() {
        return Collections.unmodifiableMap(NETWORK_CACHE);
    }
}