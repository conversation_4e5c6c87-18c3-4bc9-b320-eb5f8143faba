package com.example.harness;

import com.example.testcontainers.BrickConfig;
import com.example.testcontainers.ContainerBrick;
import com.example.testcontainers.ContainerBrickFactory;
import com.example.testcontainers.GenericContainerBrick;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.utility.DockerImageName;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Provides test utilities for setting up test environments.
 */
public class HarnessUtilities {
    private static final Logger LOGGER = LoggerFactory.getLogger(HarnessUtilities.class);
    
    /**
     * A fake PostgreSQL database for testing.
     */
    public static class FakePostgres {
        private ContainerBrick containerBrick;
        private Connection connection;
        
        /**
         * Creates a new fake PostgreSQL instance.
         */
        public FakePostgres() {
        }
        
        /**
         * Initializes the PostgreSQL container.
         * 
         * @throws Exception if the initialization fails
         */
        public void initialize() throws Exception {
            // Configure the container using BrickConfig
            BrickConfig config = new BrickConfig.Builder("postgres", "fake-postgres")
                .environmentVariables(Map.of(
                    "POSTGRES_PASSWORD", "postgres",
                    "POSTGRES_USER", "postgres",
                    "POSTGRES_DB", "testdb"
                ))
                .exposedPort("5432/tcp")
                .hostPort("5432")
                .build();
                
            // Create and start the container brick
            containerBrick = ContainerBrickFactory.newContainerBrick(config);
            containerBrick.start();
            
            // Build connection string
            String host = "localhost";
            Optional<Integer> mappedPort = containerBrick.getMappedPort("5432/tcp");
            if (!mappedPort.isPresent()) {
                throw new RuntimeException("Failed to get mapped port for PostgreSQL container");
            }
            
            String connectionString = String.format(
                "**************************************************************", 
                host, 
                mappedPort.get()
            );
            
            // Retry connecting
            SQLException lastException = null;
            for (int i = 0; i < 10; i++) {
                try {
                    connection = DriverManager.getConnection(connectionString);
                    break;
                } catch (SQLException e) {
                    lastException = e;
                    try {
                        Thread.sleep(500);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
            
            if (connection == null && lastException != null) {
                throw new RuntimeException("Could not connect to PostgreSQL", lastException);
            }
        }
        
        /**
         * Gets the database connection.
         * 
         * @return the JDBC connection
         */
        public Connection getConnection() {
            return connection;
        }
        
        /**
         * Gets the connection string for the PostgreSQL instance.
         * 
         * @return the JDBC connection string
         */
        public String getConnectionString() {
            Optional<Integer> mappedPort = containerBrick.getMappedPort("5432/tcp");
            return String.format(
                "*********************************************************************", 
                mappedPort.orElse(5432)
            );
        }
        
        /**
         * Cleans up resources.
         * 
         * @throws Exception if cleanup fails
         */
        public void cleanup() throws Exception {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    LOGGER.warn("Error closing database connection", e);
                }
            }
            
            if (containerBrick != null) {
                containerBrick.stop();
            }
        }
    }
    
    /**
     * A fake Redis instance for testing.
     */
    public static class FakeRedis {
        private ContainerBrick containerBrick;
        private RedisClient client;
        private final Map<String, AtomicInteger> hitCount = new ConcurrentHashMap<>();
        
        /**
         * Creates a new fake Redis instance.
         */
        public FakeRedis() {
        }
        
        /**
         * Initializes the Redis container.
         * 
         * @throws Exception if the initialization fails
         */
        public void initialize() throws Exception {
            BrickConfig config = new BrickConfig.Builder("dapr-redis", "fake-redis")
                .exposedPort("6379/tcp")
                .hostPort("6379")
                .build();
                
            containerBrick = ContainerBrickFactory.newContainerBrick(config);
            containerBrick.start();
            
            String host = "localhost";
            Optional<Integer> mappedPort = containerBrick.getMappedPort("6379/tcp");
            if (!mappedPort.isPresent()) {
                throw new RuntimeException("Failed to get mapped port for Redis container");
            }
            
            // Initialize Redis client - this should be implemented by the consumer
            // using their preferred Redis client implementation
            client = createRedisClient(host, mappedPort.get());
        }
        
        /**
         * Factory method to create a Redis client.
         * This method should be overridden by consumers to provide their specific Redis client implementation.
         *
         * @param host the Redis host
         * @param port the Redis port
         * @return a Redis client implementation
         */
        protected RedisClient createRedisClient(String host, int port) {
            // Default implementation that throws an exception
            throw new UnsupportedOperationException(
                "No Redis client implementation provided. Override createRedisClient() method.");
        }
        
        /**
         * Gets the Redis client.
         * 
         * @return the Redis client
         */
        public RedisClient getClient() {
            return client;
        }
        
        /**
         * Gets a value from Redis and increments the hit counter for the key.
         * 
         * @param key the key to retrieve
         * @return the value for the key
         */
        public String getWithCounter(String key) {
            hitCount.computeIfAbsent(key, k -> new AtomicInteger(0)).incrementAndGet();
            return client.get(key);
        }
        
        /**
         * Gets the hit count for a key.
         * 
         * @param key the key
         * @return the number of times the key was accessed
         */
        public int getHitCount(String key) {
            return hitCount.getOrDefault(key, new AtomicInteger(0)).get();
        }
        
        /**
         * Cleans up resources.
         * 
         * @throws Exception if cleanup fails
         */
        public void cleanup() throws Exception {
            if (client != null) {
                client.close();
            }
            
            if (containerBrick != null) {
                containerBrick.stop();
            }
        }
    }
    
    /**
     * A simple interface for Redis clients.
     */
    public interface RedisClient {
        /**
         * Gets a value from Redis.
         *
         * @param key the key
         * @return the value, or null if not found
         */
        String get(String key);
        
        /**
         * Sets a value in Redis.
         *
         * @param key   the key
         * @param value the value
         */
        void set(String key, String value);
        
        /**
         * Sets a value in Redis with an expiration time.
         *
         * @param key       the key
         * @param value     the value
         * @param seconds   the expiration time in seconds
         */
        void setex(String key, int seconds, String value);
        
        /**
         * Closes the Redis client.
         */
        void close();
    }
} 