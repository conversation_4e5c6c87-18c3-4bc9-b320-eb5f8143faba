package com.example.testcontainers;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

/**
 * Factory for creating container bricks.
 * This factory supports registering custom brick implementations for different brick types.
 */
public final class ContainerBrickFactory {
    private static final Map<String, Function<BrickConfig, ContainerBrick>> BRICK_CREATORS = new HashMap<>();
    
    static {
        // Register default brick creator for all types
        registerDefaultBrickCreators();
    }
    
    private ContainerBrickFactory() {
        // Prevent instantiation
        throw new AssertionError("No instances of ContainerBrickFactory allowed");
    }

    /**
     * Creates a new ContainerBrick based on the given configuration.
     *
     * @param config the brick configuration
     * @return a new container brick
     * @throws IllegalArgumentException if the brick type is not supported
     * @throws NullPointerException if config is null
     */
    public static ContainerBrick newContainerBrick(BrickConfig config) {
        Objects.requireNonNull(config, "Configuration cannot be null");
        String brickType = config.getBrickType();
        
        if (brickType == null || brickType.isEmpty()) {
            throw new IllegalArgumentException("Brick type cannot be null or empty");
        }
        
        Function<BrickConfig, ContainerBrick> creator = BRICK_CREATORS.get(brickType);
        if (creator == null) {
            // Use the default generic creator if no specific one is registered
            creator = GenericContainerBrick::new;
        }
        
        return creator.apply(config);
    }
    
    /**
     * Registers a custom brick creator for a specific brick type.
     *
     * @param brickType the brick type
     * @param creator the function to create a ContainerBrick from a BrickConfig
     * @throws IllegalArgumentException if brickType is null or empty
     * @throws NullPointerException if creator is null
     */
    public static void registerBrickCreator(String brickType, Function<BrickConfig, ContainerBrick> creator) {
        if (brickType == null || brickType.isEmpty()) {
            throw new IllegalArgumentException("Brick type cannot be null or empty");
        }
        Objects.requireNonNull(creator, "Creator function cannot be null");
        
        BRICK_CREATORS.put(brickType, creator);
    }
    
    /**
     * Registers the default brick creators for standard container types.
     * This is called automatically during class initialization.
     */
    private static void registerDefaultBrickCreators() {
        // Register the generic container brick creator for all standard types
        for (String type : new String[] {
                "postgres", "mongodb", "redis", "mysql", "dapr", "dapr-zipkin", 
                "dapr-redis", "dapr-placement", "dapr-sentry", "dapr-dashboard",
                "mqtt", "gin-go", "fib-calc", "k6-gin"
        }) {
            BRICK_CREATORS.put(type, GenericContainerBrick::new);
        }
    }
}