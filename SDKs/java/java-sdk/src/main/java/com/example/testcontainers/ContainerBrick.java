package com.example.testcontainers;

import java.util.Map;
import java.util.Optional;

/**
 * Defines the lifecycle methods for a container brick.
 * A container brick is a higher-level abstraction over a container
 * that provides consistent lifecycle management.
 */
public interface ContainerBrick {
    /**
     * Starts the container brick and waits for it to be ready according
     * to its configured wait strategy.
     *
     * @throws Exception if the container could not be started
     */
    void start() throws Exception;

    /**
     * Stops and removes the container brick.
     *
     * @throws Exception if the container could not be stopped
     */
    void stop() throws Exception;
    
    /**
     * Gets the name of this container brick.
     *
     * @return the name of the container
     */
    String getName();
    
    /**
     * Gets the type of this container brick.
     *
     * @return the type of the container (e.g., "postgres", "redis")
     */
    String getType();
    
    /**
     * Gets the mapped port for the given exposed port.
     *
     * @param exposedPort the exposed port (e.g., "5432/tcp")
     * @return the mapped host port, or empty if not mapped
     */
    Optional<Integer> getMappedPort(String exposedPort);
    
    /**
     * Gets the container logs.
     *
     * @return the container logs
     */
    String getLogs();
    
    /**
     * Gets the container's environment variables.
     *
     * @return a map of environment variables
     */
    Map<String, String> getEnvironment();
    
    /**
     * Checks if the container is running.
     *
     * @return true if the container is running, false otherwise
     */
    boolean isRunning();
    
    /**
     * Gets the configuration used to create this brick.
     *
     * @return the brick configuration
     */
    BrickConfig getConfig();
}