package com.example.testcontainers;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Objects;

/**
 * Utility class for handling files and directories needed for testing.
 */
public final class FileUtils {

    private FileUtils() {
        // Prevent instantiation of utility class
        throw new AssertionError("No instances of FileUtils allowed");
    }

    /**
     * Gets the absolute path for a file or directory relative to the project root.
     *
     * @param relativePath The path relative to the project root
     * @return The absolute path
     * @throws IllegalArgumentException if the path is null or empty
     */
    public static String getTestFilePath(String relativePath) {
        Objects.requireNonNull(relativePath, "Path cannot be null");
        if (relativePath.isEmpty()) {
            throw new IllegalArgumentException("Path cannot be empty");
        }
        
        Path path = Paths.get(relativePath).toAbsolutePath();
        return path.toString();
    }

    /**
     * Gets the path to the init.sql file.
     *
     * @return The absolute path to the init.sql file
     */
    public static String getInitFilePath() {
        return getTestFilePath("init.sql");
    }

    /**
     * Gets the path to the components directory.
     *
     * @return The absolute path to the components directory
     */
    public static String getComponentsPath() {
        String path = getTestFilePath("components");
        ensureDirectoryExists(path);
        return path;
    }

    /**
     * Gets the path to the load test script.
     *
     * @return The absolute path to the load test script
     */
    public static String getLoadTestScriptPath() {
        return getTestFilePath("tests/performance/scripts/load-test.js");
    }

    /**
     * Gets the path to the reports directory.
     *
     * @return The absolute path to the reports directory
     */
    public static String getReportsPath() {
        String path = getTestFilePath("tests/performance/reports");
        ensureDirectoryExists(path);
        return path;
    }
    
    /**
     * Ensures a directory exists, creating it if necessary.
     *
     * @param directoryPath The path to check
     * @throws RuntimeException if the directory cannot be created
     */
    public static void ensureDirectoryExists(String directoryPath) {
        Path path = Paths.get(directoryPath);
        if (!Files.exists(path)) {
            try {
                Files.createDirectories(path);
            } catch (IOException e) {
                throw new RuntimeException("Failed to create directory: " + directoryPath, e);
            }
        } else if (!Files.isDirectory(path)) {
            throw new IllegalArgumentException("Path exists but is not a directory: " + directoryPath);
        }
    }
}
