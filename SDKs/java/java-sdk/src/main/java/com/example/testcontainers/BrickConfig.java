package com.example.testcontainers;

import org.testcontainers.containers.wait.strategy.WaitStrategy;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * Configuration for a container brick.
 * This class uses the builder pattern for construction and provides immutable access
 * to its properties.
 */
public class BrickConfig {
    private final String brickType;
    private final String instanceName;
    private final List<String> commandArguments;
    private final Map<String, String> environmentVariables;
    private final String exposedPort; // e.g., "5432/tcp"
    private final String hostPort; // e.g., "5433"
    private final List<VolumeMapping> volumes;
    private final HealthCheck healthCheck;
    private final List<String> networks; // e.g., ["platform-tests"]
    private final Map<String, List<String>> networkAliases; // e.g., {"platform-tests": ["postgres-db"]}
    private final Consumer<org.testcontainers.containers.Container.ExecResult> hostConfigModifier;
    private final WaitStrategy customWaitStrategy;

    /**
     * Private constructor used by the Builder.
     */
    private BrickConfig(Builder builder) {
        this.brickType = builder.brickType;
        this.instanceName = builder.instanceName;
        this.commandArguments = builder.commandArguments;
        this.environmentVariables = builder.environmentVariables;
        this.exposedPort = builder.exposedPort;
        this.hostPort = builder.hostPort;
        this.volumes = builder.volumes;
        this.healthCheck = builder.healthCheck;
        this.networks = builder.networks;
        this.networkAliases = builder.networkAliases;
        this.hostConfigModifier = builder.hostConfigModifier;
        this.customWaitStrategy = builder.customWaitStrategy;
    }

    /**
     * Builder for creating BrickConfig instances.
     */
    public static class Builder {
        // Required parameters
        private String brickType;
        private String instanceName;
        
        // Optional parameters - initialized with default values
        private List<String> commandArguments;
        private Map<String, String> environmentVariables = new HashMap<>();
        private String exposedPort;
        private String hostPort;
        private List<VolumeMapping> volumes = new ArrayList<>();
        private HealthCheck healthCheck;
        private List<String> networks = new ArrayList<>();
        private Map<String, List<String>> networkAliases = new HashMap<>();
        private Consumer<org.testcontainers.containers.Container.ExecResult> hostConfigModifier;
        private WaitStrategy customWaitStrategy;

        /**
         * Constructor with required parameters.
         *
         * @param brickType the type of the brick
         * @param instanceName the name of the instance
         */
        public Builder(String brickType, String instanceName) {
            this.brickType = Objects.requireNonNull(brickType, "Brick type cannot be null");
            this.instanceName = Objects.requireNonNull(instanceName, "Instance name cannot be null");
            if (brickType.isEmpty()) {
                throw new IllegalArgumentException("Brick type cannot be empty");
            }
            if (instanceName.isEmpty()) {
                throw new IllegalArgumentException("Instance name cannot be empty");
            }
        }

        /**
         * Sets the command arguments.
         *
         * @param commandArguments the command arguments
         * @return this builder
         */
        public Builder commandArguments(List<String> commandArguments) {
            this.commandArguments = commandArguments;
            return this;
        }

        /**
         * Sets the environment variables.
         *
         * @param environmentVariables the environment variables
         * @return this builder
         */
        public Builder environmentVariables(Map<String, String> environmentVariables) {
            this.environmentVariables = environmentVariables != null 
                ? new HashMap<>(environmentVariables) 
                : new HashMap<>();
            return this;
        }

        /**
         * Sets the exposed port.
         *
         * @param exposedPort the exposed port (e.g., "5432/tcp")
         * @return this builder
         */
        public Builder exposedPort(String exposedPort) {
            this.exposedPort = exposedPort;
            return this;
        }

        /**
         * Sets the host port.
         *
         * @param hostPort the host port (e.g., "5433")
         * @return this builder
         */
        public Builder hostPort(String hostPort) {
            this.hostPort = hostPort;
            return this;
        }

        /**
         * Sets the volumes.
         *
         * @param volumes the volumes
         * @return this builder
         */
        public Builder volumes(List<VolumeMapping> volumes) {
            this.volumes = volumes != null ? new ArrayList<>(volumes) : new ArrayList<>();
            return this;
        }

        /**
         * Adds a volume mapping.
         *
         * @param hostPath the host path
         * @param containerPath the container path
         * @return this builder
         */
        public Builder addVolume(String hostPath, String containerPath) {
            if (this.volumes == null) {
                this.volumes = new ArrayList<>();
            }
            this.volumes.add(new VolumeMapping(hostPath, containerPath));
            return this;
        }

        /**
         * Sets the health check.
         *
         * @param healthCheck the health check
         * @return this builder
         */
        public Builder healthCheck(HealthCheck healthCheck) {
            this.healthCheck = healthCheck;
            return this;
        }

        /**
         * Sets the networks.
         *
         * @param networks the networks
         * @return this builder
         */
        public Builder networks(List<String> networks) {
            this.networks = networks != null ? new ArrayList<>(networks) : new ArrayList<>();
            return this;
        }

        /**
         * Adds a network.
         *
         * @param network the network to add
         * @return this builder
         */
        public Builder addNetwork(String network) {
            if (this.networks == null) {
                this.networks = new ArrayList<>();
            }
            this.networks.add(network);
            return this;
        }

        /**
         * Sets the network aliases.
         *
         * @param networkAliases the network aliases
         * @return this builder
         */
        public Builder networkAliases(Map<String, List<String>> networkAliases) {
            this.networkAliases = networkAliases != null 
                ? new HashMap<>(networkAliases) 
                : new HashMap<>();
            return this;
        }

        /**
         * Adds a network alias.
         *
         * @param network the network
         * @param alias the alias
         * @return this builder
         */
        public Builder addNetworkAlias(String network, String alias) {
            if (this.networkAliases == null) {
                this.networkAliases = new HashMap<>();
            }
            this.networkAliases.computeIfAbsent(network, k -> new ArrayList<>()).add(alias);
            return this;
        }

        /**
         * Sets the host config modifier.
         *
         * @param hostConfigModifier the host config modifier
         * @return this builder
         */
        public Builder hostConfigModifier(Consumer<org.testcontainers.containers.Container.ExecResult> hostConfigModifier) {
            this.hostConfigModifier = hostConfigModifier;
            return this;
        }

        /**
         * Sets the custom wait strategy.
         *
         * @param customWaitStrategy the custom wait strategy
         * @return this builder
         */
        public Builder customWaitStrategy(WaitStrategy customWaitStrategy) {
            this.customWaitStrategy = customWaitStrategy;
            return this;
        }

        /**
         * Builds a new BrickConfig.
         *
         * @return the built BrickConfig
         */
        public BrickConfig build() {
            return new BrickConfig(this);
        }
    }

    /**
     * Creates a new builder with the required parameters.
     *
     * @param brickType the type of the brick
     * @param instanceName the name of the instance
     * @return a new builder
     */
    public static Builder builder(String brickType, String instanceName) {
        return new Builder(brickType, instanceName);
    }

    // Getters
    /**
     * Gets the brick type.
     *
     * @return the brick type
     */
    public String getBrickType() {
        return brickType;
    }

    /**
     * Gets the instance name.
     *
     * @return the instance name
     */
    public String getInstanceName() {
        return instanceName;
    }

    /**
     * Gets the command arguments.
     *
     * @return the command arguments
     */
    public List<String> getCommandArguments() {
        return commandArguments;
    }

    /**
     * Gets the environment variables.
     *
     * @return the environment variables
     */
    public Map<String, String> getEnvironmentVariables() {
        return environmentVariables;
    }

    /**
     * Gets the exposed port.
     *
     * @return the exposed port
     */
    public String getExposedPort() {
        return exposedPort;
    }

    /**
     * Gets the host port.
     *
     * @return the host port
     */
    public String getHostPort() {
        return hostPort;
    }

    /**
     * Gets the volumes.
     *
     * @return the volumes
     */
    public List<VolumeMapping> getVolumes() {
        return volumes;
    }

    /**
     * Gets the health check.
     *
     * @return the health check
     */
    public HealthCheck getHealthCheck() {
        return healthCheck;
    }

    /**
     * Gets the networks.
     *
     * @return the networks
     */
    public List<String> getNetworks() {
        return networks;
    }

    /**
     * Gets the network aliases.
     *
     * @return the network aliases
     */
    public Map<String, List<String>> getNetworkAliases() {
        return networkAliases;
    }

    /**
     * Gets the host config modifier.
     *
     * @return the host config modifier
     */
    public Consumer<org.testcontainers.containers.Container.ExecResult> getHostConfigModifier() {
        return hostConfigModifier;
    }

    /**
     * Gets the custom wait strategy.
     *
     * @return the custom wait strategy
     */
    public WaitStrategy getCustomWaitStrategy() {
        return customWaitStrategy;
    }
}