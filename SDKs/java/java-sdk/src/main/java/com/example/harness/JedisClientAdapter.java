package com.example.harness;

import redis.clients.jedis.Jedis;

/**
 * Adapter for Jedis Redis client to the harness RedisClient interface.
 */
public class JedisClientAdapter implements HarnessUtilities.RedisClient {
    private final Jedis jedis;
    
    /**
     * Creates a new Jedis client adapter.
     *
     * @param host the Redis host
     * @param port the Redis port
     */
    public JedisClientAdapter(String host, int port) {
        this.jedis = new Jedis(host, port);
    }
    
    @Override
    public String get(String key) {
        return jedis.get(key);
    }
    
    @Override
    public void set(String key, String value) {
        jedis.set(key, value);
    }
    
    @Override
    public void setex(String key, int seconds, String value) {
        jedis.setex(key, seconds, value);
    }
    
    @Override
    public void close() {
        jedis.close();
    }
} 