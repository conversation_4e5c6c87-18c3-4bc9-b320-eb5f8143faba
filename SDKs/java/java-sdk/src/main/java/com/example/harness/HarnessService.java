package com.example.harness;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Duration;
import java.util.Objects;
import java.util.function.Function;

import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * A service that provides user management functionality.
 */
public class HarnessService {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final Logger LOGGER = LoggerFactory.getLogger(HarnessService.class);
    
    /**
     * Represents a user in the system.
     */
    public static class User {
        private String id;
        private String name;
        private String email;
        
        // Default constructor for Jackson
        public User() {
        }
        
        /**
         * Creates a new user.
         *
         * @param id    the user ID
         * @param name  the user name
         * @param email the user email
         */
        public User(String id, String name, String email) {
            this.id = id;
            this.name = name;
            this.email = email;
        }
        
        public String getId() {
            return id;
        }
        
        public void setId(String id) {
            this.id = id;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getEmail() {
            return email;
        }
        
        public void setEmail(String email) {
            this.email = email;
        }
    }
    
    /**
     * A functional interface for sending emails.
     */
    @FunctionalInterface
    public interface EmailSender {
        /**
         * Sends an email.
         *
         * @param from    the sender email
         * @param to      the recipient emails
         * @param subject the email subject
         * @param body    the email body
         * @return true if the email was sent successfully
         */
        boolean send(String from, String[] to, String subject, String body);
    }
    
    private final Connection dbConnection;
    private final RedisClient redisClient;
    private final EmailSender emailSender;
    private final Logger logger;
    private final Duration cacheTimeout;
    
    /**
     * Creates a new user service.
     *
     * @param dbConnection the database connection
     * @param redisClient  the Redis client
     * @param emailSender  the email sender
     * @param logger       the logger
     */
    public HarnessService(
            Connection dbConnection,
            RedisClient redisClient,
            EmailSender emailSender,
            Logger logger) {
        this.dbConnection = Objects.requireNonNull(dbConnection, "Database connection cannot be null");
        this.redisClient = Objects.requireNonNull(redisClient, "Redis client cannot be null");
        this.emailSender = Objects.requireNonNull(emailSender, "Email sender cannot be null");
        this.logger = Objects.requireNonNull(logger, "Logger cannot be null");
        this.cacheTimeout = Duration.ofMinutes(10);
    }
    
    /**
     * Gets a user by ID, using cache when available.
     *
     * @param context the context containing correlation ID
     * @param userId  the user ID
     * @return the user, or null if not found
     * @throws Exception if an error occurs
     */
    public User getUser(Context context, String userId) throws Exception {
        String corrId = getCorrelationId(context);
        logger.info("Getting user: userId={}, correlationId={}", userId, corrId);
        
        // First check Redis cache
        String cacheKey = "user:" + userId;
        String cachedUser = redisClient.get(cacheKey);
        
        if (cachedUser != null) {
            // Cache hit
            logger.info("User found in cache: userId={}, correlationId={}", userId, corrId);
            return OBJECT_MAPPER.readValue(cachedUser, User.class);
        }
        
        // If not in cache, get from database
        try (PreparedStatement statement = dbConnection.prepareStatement(
                "SELECT id, name, email FROM users WHERE id = ?")) {
            statement.setString(1, userId);
            
            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    User user = new User(
                        resultSet.getString("id"),
                        resultSet.getString("name"),
                        resultSet.getString("email")
                    );
                    
                    // Store in cache
                    try {
                        String userJson = OBJECT_MAPPER.writeValueAsString(user);
                        redisClient.set(cacheKey, userJson, cacheTimeout.toMillis());
                        logger.info("User cached: userId={}, correlationId={}", userId, corrId);
                    } catch (Exception e) {
                        // Log the error but don't fail the request
                        logger.error("Failed to cache user: userId={}, correlationId={}, error={}",
                                userId, corrId, e.getMessage());
                    }
                    
                    return user;
                } else {
                    logger.warn("User not found: userId={}, correlationId={}", userId, corrId);
                    return null;
                }
            }
        } catch (SQLException e) {
            throw new Exception("Database error: " + e.getMessage(), e);
        }
    }
    
    /**
     * Sends a welcome email to a user.
     *
     * @param context the context containing correlation ID
     * @param user    the user to send the email to
     * @return true if the email was sent successfully
     */
    public boolean sendWelcomeEmail(Context context, User user) {
        String corrId = getCorrelationId(context);
        logger.info("Sending welcome email: email={}, correlationId={}", user.getEmail(), corrId);
        
        String from = "<EMAIL>";
        String[] to = {user.getEmail()};
        String subject = "Welcome to our service";
        String body = String.format("Hello %s, welcome to our service!", user.getName());
        
        boolean success = emailSender.send(from, to, subject, body);
        
        if (success) {
            logger.info("Welcome email sent: email={}, correlationId={}", user.getEmail(), corrId);
        } else {
            logger.error("Failed to send welcome email: email={}, correlationId={}",
                    user.getEmail(), corrId);
        }
        
        return success;
    }
    
    /**
     * Extracts the correlation ID from the context.
     *
     * @param context the context
     * @return the correlation ID, or an empty string if not found
     */
    private String getCorrelationId(Context context) {
        if (context == null) {
            return "";
        }
        
        String corrId = context.getCorrelationId();
        return corrId != null ? corrId : "";
    }
    
    /**
     * A simple context class for storing request context.
     */
    public static class Context {
        private final String correlationId;
        
        /**
         * Creates a new context.
         *
         * @param correlationId the correlation ID
         */
        public Context(String correlationId) {
            this.correlationId = correlationId;
        }
        
        /**
         * Gets the correlation ID.
         *
         * @return the correlation ID
         */
        public String getCorrelationId() {
            return correlationId;
        }
    }
    
    /**
     * A simple Redis client interface.
     */
    public interface RedisClient {
        /**
         * Gets a value from Redis.
         *
         * @param key the key
         * @return the value, or null if not found
         */
        String get(String key);
        
        /**
         * Sets a value in Redis with an expiration time.
         *
         * @param key     the key
         * @param value   the value
         * @param timeoutMs the expiration time in milliseconds
         */
        void set(String key, String value, long timeoutMs);
    }
} 