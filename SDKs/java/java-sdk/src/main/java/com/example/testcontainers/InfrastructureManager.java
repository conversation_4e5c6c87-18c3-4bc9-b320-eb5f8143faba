package com.example.testcontainers;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Manages a collection of container bricks for integration testing.
 * This class implements AutoCloseable to support use with try-with-resources.
 */
public class InfrastructureManager implements AutoCloseable {
    private static final Logger LOGGER = LoggerFactory.getLogger(InfrastructureManager.class);
    
    private final List<ContainerBrick> bricks = new ArrayList<>();
    private final AtomicBoolean started = new AtomicBoolean(false);
    private final AtomicBoolean closed = new AtomicBoolean(false);

    /**
     * Creates an InfrastructureManager from a list of brick configurations.
     *
     * @param configs the brick configurations
     * @return a new infrastructure manager
     * @throws NullPointerException if configs is null
     */
    public static InfrastructureManager from(List<BrickConfig> configs) {
        Objects.requireNonNull(configs, "Configurations cannot be null");
        
        InfrastructureManager manager = new InfrastructureManager();
        for (BrickConfig config : configs) {
            ContainerBrick brick = ContainerBrickFactory.newContainerBrick(config);
            manager.addBrick(brick);
        }
        return manager;
    }

    /**
     * Adds a brick to the manager.
     *
     * @param brick the brick to add
     * @throws NullPointerException if brick is null
     * @throws IllegalStateException if manager has already been started
     */
    public void addBrick(ContainerBrick brick) {
        Objects.requireNonNull(brick, "Brick cannot be null");
        if (started.get()) {
            throw new IllegalStateException("Cannot add brick after manager has been started");
        }
        bricks.add(brick);
    }

    /**
     * Starts all container bricks.
     *
     * @throws Exception if any container could not be started
     * @throws IllegalStateException if manager has already been started or closed
     */
    public void startAll() throws Exception {
        if (closed.get()) {
            throw new IllegalStateException("Manager has been closed");
        }
        if (!started.compareAndSet(false, true)) {
            throw new IllegalStateException("Manager has already been started");
        }
        
        LOGGER.info("Starting {} container bricks", bricks.size());
        Exception startupException = null;
        int startedCount = 0;
        
        for (ContainerBrick brick : bricks) {
            try {
                LOGGER.info("Starting brick: {}", brick.getName());
                brick.start();
                startedCount++;
            } catch (Exception e) {
                LOGGER.error("Failed to start brick: {}", brick.getName(), e);
                startupException = e;
                break;
            }
        }
        
        // If any brick failed to start, stop the ones that did
        if (startupException != null) {
            try {
                stopStartedBricks(startedCount);
            } catch (Exception e) {
                LOGGER.error("Error during cleanup after startup failure", e);
                // Add the cleanup exception as a suppressed exception
                startupException.addSuppressed(e);
            }
            throw startupException;
        }
        
        LOGGER.info("All {} container bricks started successfully", bricks.size());
    }

    /**
     * Stops all container bricks.
     *
     * @throws Exception if any container could not be stopped
     */
    public void stopAll() throws Exception {
        if (!started.get() || closed.get()) {
            return;
        }
        
        closed.set(true);
        LOGGER.info("Stopping all container bricks");
        stopStartedBricks(bricks.size());
        LOGGER.info("All container bricks stopped");
    }
    
    /**
     * Stops the first n container bricks.
     *
     * @param count the number of bricks to stop
     * @throws Exception if any container could not be stopped
     */
    private void stopStartedBricks(int count) throws Exception {
        List<Exception> exceptions = new ArrayList<>();
        
        // Stop bricks in reverse order of starting
        for (int i = Math.min(count, bricks.size()) - 1; i >= 0; i--) {
            ContainerBrick brick = bricks.get(i);
            try {
                LOGGER.info("Stopping brick: {}", brick.getName());
                brick.stop();
            } catch (Exception e) {
                LOGGER.error("Failed to stop brick: {}", brick.getName(), e);
                exceptions.add(e);
            }
        }
        
        // If any stop operations failed, throw an exception with suppressed exceptions
        if (!exceptions.isEmpty()) {
            Exception e = new Exception("Failed to stop one or more container bricks");
            for (Exception ex : exceptions) {
                e.addSuppressed(ex);
            }
            throw e;
        }
    }

    /**
     * Gets all bricks.
     *
     * @return an unmodifiable list of container bricks
     */
    public List<ContainerBrick> getBricks() {
        return Collections.unmodifiableList(bricks);
    }
    
    /**
     * Checks if the infrastructure has been started.
     *
     * @return true if started, false otherwise
     */
    public boolean isStarted() {
        return started.get();
    }
    
    /**
     * Checks if the infrastructure has been closed.
     *
     * @return true if closed, false otherwise
     */
    public boolean isClosed() {
        return closed.get();
    }
    
    /**
     * Provides a wrapper that automatically starts and stops the infrastructure.
     * 
     * @return a wrapper that starts this infrastructure when entered and stops it when exited
     * @throws IllegalStateException if the manager has already been started or closed
     */
    public ManagedInfrastructure managedInfrastructure() {
        return new ManagedInfrastructure(this);
    }
    
    /**
     * Closes this infrastructure manager, stopping all container bricks.
     * This method is idempotent.
     *
     * @throws Exception if any container could not be stopped
     */
    @Override
    public void close() throws Exception {
        stopAll();
    }
    
    /**
     * A wrapper for InfrastructureManager that implements AutoCloseable.
     * This class starts the infrastructure when constructed and stops it when closed.
     */
    public static class ManagedInfrastructure implements AutoCloseable {
        private final InfrastructureManager manager;
        
        private ManagedInfrastructure(InfrastructureManager manager) {
            this.manager = manager;
        }
        
        /**
         * Starts the infrastructure.
         *
         * @return this managed infrastructure
         * @throws Exception if the infrastructure could not be started
         */
        public ManagedInfrastructure start() throws Exception {
            manager.startAll();
            return this;
        }
        
        /**
         * Closes the infrastructure, stopping all container bricks.
         *
         * @throws Exception if the infrastructure could not be stopped
         */
        @Override
        public void close() throws Exception {
            manager.close();
        }
    }
}