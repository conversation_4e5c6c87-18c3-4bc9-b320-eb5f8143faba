package com.example.testcontainers;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * Configuration for Docker health checks.
 * This class follows the immutable pattern and uses a builder for construction.
 */
public class HealthCheck {
    private final List<String> test;
    private final Duration interval;
    private final int retries;
    private final Duration startPeriod;
    private final Duration timeout;

    private HealthCheck(Builder builder) {
        this.test = Collections.unmodifiableList(builder.test);
        this.interval = builder.interval;
        this.retries = builder.retries;
        this.startPeriod = builder.startPeriod;
        this.timeout = builder.timeout;
    }

    /**
     * @return The test command to run inside the container
     */
    public List<String> getTest() {
        return test;
    }

    /**
     * @return The interval between health checks
     */
    public Duration getInterval() {
        return interval;
    }

    /**
     * @return The number of retries before considering the container unhealthy
     */
    public int getRetries() {
        return retries;
    }

    /**
     * @return The initialization time to wait before starting health checks
     */
    public Duration getStartPeriod() {
        return startPeriod;
    }

    /**
     * @return The timeout for a single health check execution
     */
    public Duration getTimeout() {
        return timeout;
    }

    /**
     * Builder for creating HealthCheck instances.
     */
    public static class Builder {
        private List<String> test;
        private Duration interval = Duration.ofSeconds(5);
        private int retries = 3;
        private Duration startPeriod = Duration.ofSeconds(0);
        private Duration timeout = Duration.ofSeconds(30);

        /**
         * Sets the test command.
         *
         * @param test The command to run inside the container
         * @return this builder
         */
        public Builder test(List<String> test) {
            this.test = Objects.requireNonNull(test, "Test command cannot be null");
            return this;
        }

        /**
         * Sets the interval between health checks.
         *
         * @param interval The interval
         * @return this builder
         */
        public Builder interval(Duration interval) {
            this.interval = Objects.requireNonNull(interval, "Interval cannot be null");
            return this;
        }

        /**
         * Sets the number of retries.
         *
         * @param retries The number of retries
         * @return this builder
         */
        public Builder retries(int retries) {
            if (retries < 1) {
                throw new IllegalArgumentException("Retries must be at least 1");
            }
            this.retries = retries;
            return this;
        }

        /**
         * Sets the initialization period before starting health checks.
         *
         * @param startPeriod The start period
         * @return this builder
         */
        public Builder startPeriod(Duration startPeriod) {
            this.startPeriod = Objects.requireNonNull(startPeriod, "Start period cannot be null");
            return this;
        }

        /**
         * Sets the timeout for a single health check execution.
         *
         * @param timeout The timeout
         * @return this builder
         */
        public Builder timeout(Duration timeout) {
            this.timeout = Objects.requireNonNull(timeout, "Timeout cannot be null");
            return this;
        }

        /**
         * Builds a new HealthCheck instance.
         *
         * @return A new HealthCheck instance
         * @throws IllegalStateException if the test command is not set
         */
        public HealthCheck build() {
            if (test == null || test.isEmpty()) {
                throw new IllegalStateException("Test command must be provided");
            }
            return new HealthCheck(this);
        }
    }

    /**
     * Creates a new builder for HealthCheck.
     *
     * @return a new builder
     */
    public static Builder builder() {
        return new Builder();
    }
}
