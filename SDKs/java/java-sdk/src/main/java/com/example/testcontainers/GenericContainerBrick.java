package com.example.testcontainers;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.Network;
import org.testcontainers.containers.output.Slf4jLogConsumer;
import org.testcontainers.containers.wait.strategy.Wait;
import org.testcontainers.containers.wait.strategy.WaitStrategy;
import org.testcontainers.utility.DockerImageName;

import java.time.Duration;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Implementation of the ContainerBrick interface using the Testcontainers library.
 * This class provides a generic container implementation that can be configured
 * for various container types.
 */
public class GenericContainerBrick implements ContainerBrick {
    private static final Logger LOGGER = LoggerFactory.getLogger(GenericContainerBrick.class);
    private static final int DEFAULT_STARTUP_TIMEOUT_SECONDS = 120;
    private static final Map<String, DockerImageName> IMAGE_REGISTRY = new HashMap<>();
    
    static {
        registerDefaultImages();
    }
    
    private final BrickConfig config;
    private GenericContainer<?> container;
    private final AtomicBoolean started = new AtomicBoolean(false);

    /**
     * Creates a new GenericContainerBrick with the given configuration.
     *
     * @param config the brick configuration
     * @throws NullPointerException if config is null
     */
    public GenericContainerBrick(BrickConfig config) {
        this.config = Objects.requireNonNull(config, "Configuration cannot be null");
    }

    /**
     * Registers default Docker images for known brick types.
     */
    private static void registerDefaultImages() {
        IMAGE_REGISTRY.put("dapr", DockerImageName.parse("daprio/daprd:1.14.4"));
        IMAGE_REGISTRY.put("dapr-zipkin", DockerImageName.parse("openzipkin/zipkin:latest"));
        IMAGE_REGISTRY.put("dapr-redis", DockerImageName.parse("redis:latest"));
        IMAGE_REGISTRY.put("dapr-placement", DockerImageName.parse("daprio/dapr-placement:latest"));
        IMAGE_REGISTRY.put("dapr-sentry", DockerImageName.parse("daprio/dapr-sentry:latest"));
        IMAGE_REGISTRY.put("dapr-dashboard", DockerImageName.parse("daprio/dapr-dashboard:latest"));
        IMAGE_REGISTRY.put("mqtt", DockerImageName.parse("eclipse-mosquitto:latest"));
        IMAGE_REGISTRY.put("postgres", DockerImageName.parse("postgres:15-alpine"));
        IMAGE_REGISTRY.put("mysql", DockerImageName.parse("mysql:8.0"));
        IMAGE_REGISTRY.put("mongodb", DockerImageName.parse("mongo:latest"));
        IMAGE_REGISTRY.put("redis", DockerImageName.parse("redis:latest"));
        IMAGE_REGISTRY.put("gin-go", DockerImageName.parse("platform-tests-gin-go:latest"));
        IMAGE_REGISTRY.put("fib-calc", DockerImageName.parse("platform-tests-fib-calc:latest"));
        IMAGE_REGISTRY.put("k6-gin", DockerImageName.parse("grafana/k6:latest"));
    }

    /**
     * Gets the Docker image name for the given brick type.
     *
     * @param brickType the brick type
     * @return the Docker image name
     * @throws IllegalArgumentException if the brick type is unknown
     */
    private DockerImageName getImageForBrickType(String brickType) {
        DockerImageName image = IMAGE_REGISTRY.get(brickType);
        if (image == null) {
            throw new IllegalArgumentException("Unknown brick type: " + brickType);
        }
        return image;
    }

    /**
     * Registers a custom Docker image for a brick type.
     *
     * @param brickType the brick type
     * @param imageName the Docker image name
     * @throws IllegalArgumentException if brickType is null or empty
     * @throws NullPointerException if imageName is null
     */
    public static void registerImage(String brickType, DockerImageName imageName) {
        if (brickType == null || brickType.isEmpty()) {
            throw new IllegalArgumentException("Brick type cannot be null or empty");
        }
        Objects.requireNonNull(imageName, "Image name cannot be null");
        
        IMAGE_REGISTRY.put(brickType, imageName);
    }

    @Override
    public void start() throws Exception {
        if (started.get()) {
            LOGGER.info("Container already started: {}", getName());
            return;
        }
        
        if (!started.compareAndSet(false, true)) {
            LOGGER.info("Container already starting: {}", getName());
            return;
        }
        
        try {
            LOGGER.info("Starting container: {}", getName());
            DockerImageName image = getImageForBrickType(config.getBrickType());
            
            // Create container with the appropriate image
            container = new GenericContainer<>(image)
                    .withNetworkAliases(config.getInstanceName())
                    .withLabels(Map.of(
                            "com.docker.compose.project", "platform-tests",
                            "com.testcontainers.brick.type", config.getBrickType()
                    ));
            
            configureContainer();
            
            // Attach logger to container output
            Slf4jLogConsumer logConsumer = new Slf4jLogConsumer(
                    LoggerFactory.getLogger(config.getBrickType() + "." + config.getInstanceName())
            );
            container.withLogConsumer(logConsumer);
            
            // Start the container
            container.start();
            LOGGER.info("Container started: {}", getName());
        } catch (Exception e) {
            started.set(false);
            LOGGER.error("Failed to start container: {}", getName(), e);
            throw new Exception("Failed to start container: " + getName(), e);
        }
    }
    
    /**
     * Configures the container with the settings from the brick configuration.
     */
    private void configureContainer() {
        // Set container name
        if (config.getInstanceName() != null && !config.getInstanceName().isEmpty()) {
            container.withCreateContainerCmdModifier(cmd -> cmd.withName(config.getInstanceName()));
        }
        
        // Set exposed ports
        if (config.getExposedPort() != null && !config.getExposedPort().isEmpty()) {
            int port = extractPortNumber(config.getExposedPort());
            container.addExposedPort(port);
            
            // Set host port mapping if specified
            if (config.getHostPort() != null && !config.getHostPort().isEmpty()) {
                String hostPortBinding = String.format("%s:%s", config.getHostPort(), port);
                container.setPortBindings(List.of(hostPortBinding));
            }
        }
        
        // Set environment variables
        if (config.getEnvironmentVariables() != null) {
            for (Map.Entry<String, String> entry : config.getEnvironmentVariables().entrySet()) {
                container.withEnv(entry.getKey(), entry.getValue());
            }
        }
        
        // Set command arguments
        if (config.getCommandArguments() != null && !config.getCommandArguments().isEmpty()) {
            container.withCommand(config.getCommandArguments().toArray(new String[0]));
        }
        
        // Set networks
        if (config.getNetworks() != null) {
            for (String networkName : config.getNetworks()) {
                Network network = CustomNetwork.getOrCreateNetwork(networkName);
                container.withNetwork(network);
                
                // Set network aliases for this network
                if (config.getNetworkAliases() != null && config.getNetworkAliases().containsKey(networkName)) {
                    for (String alias : config.getNetworkAliases().get(networkName)) {
                        container.withNetworkAliases(alias);
                    }
                }
            }
        }
        
        // Set volumes
        if (config.getVolumes() != null) {
            for (VolumeMapping volume : config.getVolumes()) {
                container.withFileSystemBind(volume.getHostPath(), volume.getContainerPath());
            }
        }
        
        // Set wait strategy
        WaitStrategy waitStrategy;
        if (config.getCustomWaitStrategy() != null) {
            waitStrategy = config.getCustomWaitStrategy();
        } else if (config.getHealthCheck() != null) {
            waitStrategy = configureHealthCheck(config.getHealthCheck());
        } else if (config.getExposedPort() != null && !config.getExposedPort().isEmpty()) {
            int port = extractPortNumber(config.getExposedPort());
            waitStrategy = Wait.forListeningPort()
                    .withStartupTimeout(Duration.ofSeconds(DEFAULT_STARTUP_TIMEOUT_SECONDS));
        } else {
            // Default to waiting for container to start
            waitStrategy = Wait.forLogMessage(".*Container started.*", 1)
                    .withStartupTimeout(Duration.ofSeconds(DEFAULT_STARTUP_TIMEOUT_SECONDS));
        }
        
        container.waitingFor(waitStrategy);
    }
    
    /**
     * Configures a health check wait strategy based on the health check configuration.
     * 
     * @param healthCheck the health check configuration
     * @return a wait strategy based on the health check
     */
    private WaitStrategy configureHealthCheck(HealthCheck healthCheck) {
        // For specific health check commands, use custom wait strategies
        if (healthCheck.getTest() != null && !healthCheck.getTest().isEmpty()) {
            String firstCommand = healthCheck.getTest().get(0);
            if ("pg_isready".equals(firstCommand)) {
                return Wait.forLogMessage(".*database system is ready to accept connections.*", 1)
                        .withStartupTimeout(healthCheck.getTimeout());
            } else if ("redis-cli".equals(firstCommand)) {
                return Wait.forLogMessage(".*Ready to accept connections.*", 1)
                        .withStartupTimeout(healthCheck.getTimeout());
            }
        }
        
        // Default to waiting for the container to report healthy
        return Wait.forHealthcheck()
                .withStartupTimeout(healthCheck.getTimeout());
    }

    /**
     * Extracts the port number from a port string.
     *
     * @param portString the port string (e.g., "5432/tcp")
     * @return the port number
     * @throws IllegalArgumentException if the port string is invalid
     */
    private int extractPortNumber(String portString) {
        if (portString == null || portString.isEmpty()) {
            throw new IllegalArgumentException("Port cannot be null or empty");
        }
        
        // Extract the numeric part of the port string
        try {
            return Integer.parseInt(portString.split("/")[0]);
        } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
            throw new IllegalArgumentException("Invalid port format: " + portString, e);
        }
    }

    @Override
    public void stop() throws Exception {
        if (!started.get() || container == null) {
            LOGGER.info("Container not started: {}", getName());
            return;
        }
        
        try {
            LOGGER.info("Stopping container: {}", getName());
            container.stop();
            LOGGER.info("Container stopped: {}", getName());
        } catch (Exception e) {
            LOGGER.error("Failed to stop container: {}", getName(), e);
            throw new Exception("Failed to stop container: " + getName(), e);
        } finally {
            started.set(false);
        }
    }

    @Override
    public String getName() {
        return config.getInstanceName();
    }

    @Override
    public String getType() {
        return config.getBrickType();
    }

    @Override
    public Optional<Integer> getMappedPort(String exposedPort) {
        if (!started.get() || container == null) {
            return Optional.empty();
        }
        
        try {
            int port = extractPortNumber(exposedPort);
            return Optional.of(container.getMappedPort(port));
        } catch (Exception e) {
            LOGGER.warn("Failed to get mapped port for {}: {}", exposedPort, e.getMessage());
            return Optional.empty();
        }
    }

    @Override
    public String getLogs() {
        if (!started.get() || container == null) {
            return "";
        }
        return container.getLogs();
    }

    @Override
    public Map<String, String> getEnvironment() {
        return config.getEnvironmentVariables() != null 
                ? Collections.unmodifiableMap(config.getEnvironmentVariables()) 
                : Collections.emptyMap();
    }

    @Override
    public boolean isRunning() {
        return started.get() && container != null && container.isRunning();
    }

    @Override
    public BrickConfig getConfig() {
        return config;
    }

    /**
     * Gets the underlying container.
     *
     * @return the container, or null if not started
     */
    public GenericContainer<?> getContainer() {
        return container;
    }
}