package main.test;

import static org.hamcrest.Matchers.anyOf;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;

import io.restassured.RestAssured;
import io.restassured.http.ContentType;

public class WireMockEndpointsTest {

    @BeforeAll
    public static void setup() {
        RestAssured.baseURI = "http://localhost";
        RestAssured.port = 8080;
    }

    @Test
    void testHealth() {
        RestAssured.get("/health")
            .then()
            .statusCode(200)
            .body("status", equalTo("healthy"));
    }

    @Test
    void testUpload() {
        RestAssured.given()
            .multiPart("file", "test.txt", "hello world".getBytes())
        .when()
            .post("/upload")
        .then()
            .statusCode(201)
            .body("message", equalTo("File uploaded successfully"));
    }

    @Test
    void testSecureResource() {
        RestAssured.given()
            .header("Authorization", "Bearer abcdef123456")
        .when()
            .get("/secure/resource")
        .then()
            .statusCode(200)
            .body("message", equalTo("Authorized access granted"));
    }

    @Test
    void testApiJson() {
        RestAssured.given()
            .contentType(ContentType.JSON)
            .body("{\"data\":\"test\"}")
        .when()
            .post("/api/json")
        .then()
            .statusCode(200)
            .body("message", equalTo("JSON processed"));
    }

    @Test
    void testApiXml() {
        RestAssured.given()
            .log().all()
            .header("Content-Type", "application/xml")
            .body("<root><data>test</data></root>")
        .when()
            .post("/api/xml")
        .then()
            .log().all()
            .statusCode(anyOf(is(200), is(404)));
    }

    @Test
    void testCounterScenario() {
        // Reset scenarios before starting
        RestAssured.post("/__admin/scenarios/reset").then().statusCode(anyOf(is(200), is(204)));
        RestAssured.get("/counter").then().statusCode(200).body("count", equalTo(1));
        RestAssured.get("/counter").then().statusCode(200).body("count", equalTo(2));
        RestAssured.get("/counter").then().statusCode(200).body("count", equalTo(3));
    }

    @Test
    void testError() {
        RestAssured.get("/error")
            .then()
            .statusCode(500)
            .body("error", equalTo("Internal Server Error"));
    }

    @Test
    void testUnstableService() {
        long start = System.currentTimeMillis();
        RestAssured.get("/unstable-service")
            .then()
            .statusCode(503)
            .body("error", equalTo("Service unavailable due to timeout"));
        long elapsed = System.currentTimeMillis() - start;
        assert elapsed >= 3000 : "Expected at least 3s delay, got " + elapsed + "ms";
    }

    @Test
    void testNotFound() {
        RestAssured.get("/does-not-exist")
            .then()
            .statusCode(404)
            .body("error", equalTo("No matching stub found"));
    }
}
