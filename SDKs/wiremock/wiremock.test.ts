import axios from "axios";

const baseURL = "http://localhost:8080";

describe("WireMock Endpoints", () => {
  it("GET /health", async () => {
    const resp = await axios.get(`${baseURL}/health`);
    expect(resp.status).toBe(200);
    expect(resp.data).toEqual({ status: "healthy" });
  });

  it("POST /upload (multipart)", async () => {
    // Use FormData for multipart
    const FormData = require("form-data");
    const form = new FormData();
    form.append("file", Buffer.from("hello world"), "test.txt");
    const resp = await axios.post(`${baseURL}/upload`, form, {
      headers: form.getHeaders(),
      validateStatus: () => true,
    });
    expect(resp.status).toBe(201);
    expect(resp.data).toEqual({ message: "File uploaded successfully" });
  });

  it("GET /secure/resource (with Authorization)", async () => {
    const resp = await axios.get(`${baseURL}/secure/resource`, {
      headers: { Authorization: "Bearer abcdef123456" },
    });
    expect(resp.status).toBe(200);
    expect(resp.data).toEqual({ message: "Authorized access granted" });
  });

  it("POST /api/json (JSON body)", async () => {
    const resp = await axios.post(
      `${baseURL}/api/json`,
      { data: "test" },
      { headers: { "Content-Type": "application/json" } }
    );
    expect(resp.status).toBe(200);
    expect(resp.data).toEqual({ message: "JSON processed" });
  });

  it("POST /api/xml (XML body)", async () => {
    const xml = `<root><data>test</data></root>`;
    const resp = await axios.post(`${baseURL}/api/xml`, xml, {
      headers: { "Content-Type": "application/xml" },
      responseType: "text",
    });
    expect(resp.status).toBe(200);
    expect(resp.data).toBe("<response>XML processed</response>");
  });

  it("GET /counter (scenario stateful)", async () => {
    // Reset scenarios before starting
    await axios.post(`${baseURL}/__admin/scenarios/reset`);
    const expected = [{ count: 1 }, { count: 2 }, { count: 3 }];
    for (const [i, exp] of expected.entries()) {
      const resp = await axios.get(`${baseURL}/counter`);
      expect(resp.status).toBe(200);
      expect(resp.data).toEqual(exp);
    }
  });

  it("GET /error (500)", async () => {
    const resp = await axios.get(`${baseURL}/error`, { validateStatus: () => true });
    expect(resp.status).toBe(500);
    expect(resp.data).toEqual({ error: "Internal Server Error" });
  });

  it("GET /unstable-service (503, delayed)", async () => {
    const start = Date.now();
    const resp = await axios.get(`${baseURL}/unstable-service`, { validateStatus: () => true });
    const elapsed = Date.now() - start;
    expect(resp.status).toBe(503);
    expect(resp.data).toEqual({ error: "Service unavailable due to timeout" });
    expect(elapsed).toBeGreaterThanOrEqual(3000);
  });

  it("GET /does-not-exist (404)", async () => {
    const resp = await axios.get(`${baseURL}/does-not-exist`, { validateStatus: () => true });
    expect(resp.status).toBe(404);
    expect(resp.data).toEqual({ error: "No matching stub found" });
  });
});
