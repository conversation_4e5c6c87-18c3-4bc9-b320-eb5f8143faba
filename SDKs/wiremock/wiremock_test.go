package main

import (
	"bytes"
	"encoding/json"
	"io"
	"mime/multipart"
	"net/http"
	"strings"
	"testing"
	"time"
)

func TestHealth(t *testing.T) {
	resp, err := http.Get("http://localhost:8080/health")
	if err != nil {
		t.Fatal(err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		t.Fatalf("Expected 200, got %d", resp.StatusCode)
	}
	var body map[string]string
	if err := json.NewDecoder(resp.Body).Decode(&body); err != nil {
		t.Fatal(err)
	}
	if body["status"] != "healthy" {
		t.Fatalf("Expected status=healthy, got %v", body)
	}
}

func TestUpload(t *testing.T) {
	var b bytes.Buffer
	w := multipart.NewWriter(&b)
	fw, err := w.CreateFormFile("file", "test.txt")
	if err != nil {
		t.<PERSON>al(err)
	}
	_, err = fw.Write([]byte("hello world"))
	if err != nil {
		t.Fatal(err)
	}
	w.Close()
	req, err := http.NewRequest("POST", "http://localhost:8080/upload", &b)
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("Content-Type", w.FormDataContentType())
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		t.Fatal(err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != 201 {
		t.Fatalf("Expected 201, got %d", resp.StatusCode)
	}
	var body map[string]string
	if err := json.NewDecoder(resp.Body).Decode(&body); err != nil {
		t.Fatal(err)
	}
	if body["message"] != "File uploaded successfully" {
		t.Fatalf("Expected upload message, got %v", body)
	}
}

func TestSecureResource(t *testing.T) {
	req, _ := http.NewRequest("GET", "http://localhost:8080/secure/resource", nil)
	req.Header.Set("Authorization", "Bearer abcdef123456")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		t.Fatal(err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		t.Fatalf("Expected 200, got %d", resp.StatusCode)
	}
	var body map[string]string
	if err := json.NewDecoder(resp.Body).Decode(&body); err != nil {
		t.Fatal(err)
	}
	if body["message"] != "Authorized access granted" {
		t.Fatalf("Expected authorized message, got %v", body)
	}
}

func TestAPIJson(t *testing.T) {
	payload := `{"data":"test"}`
	req, _ := http.NewRequest("POST", "http://localhost:8080/api/json", strings.NewReader(payload))
	req.Header.Set("Content-Type", "application/json")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		t.Fatal(err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		t.Fatalf("Expected 200, got %d", resp.StatusCode)
	}
	var body map[string]string
	if err := json.NewDecoder(resp.Body).Decode(&body); err != nil {
		t.Fatal(err)
	}
	if body["message"] != "JSON processed" {
		t.Fatalf("Expected JSON processed, got %v", body)
	}
}

func TestAPIXML(t *testing.T) {
	xml := `<root><data>test</data></root>`
	req, _ := http.NewRequest("POST", "http://localhost:8080/api/xml", strings.NewReader(xml))
	req.Header.Set("Content-Type", "application/xml")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		t.Fatal(err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != 200 {
		t.Fatalf("Expected 200, got %d", resp.StatusCode)
	}
	body, _ := io.ReadAll(resp.Body)
	if string(body) != "<response>XML processed</response>" {
		t.Fatalf("Expected XML processed, got %s", string(body))
	}
}

func TestCounterScenario(t *testing.T) {
	// Reset scenarios before starting
	http.Post("http://localhost:8080/__admin/scenarios/reset", "application/json", nil)
	url := "http://localhost:8080/counter"
	for i, expected := range []string{`{"count":1}`, `{"count":2}`, `{"count":3}`} {
		resp, err := http.Get(url)
		if err != nil {
			t.Fatal(err)
		}
		defer resp.Body.Close()
		body, _ := io.ReadAll(resp.Body)
		if resp.StatusCode != 200 {
			t.Fatalf("Expected 200, got %d, body: %s", resp.StatusCode, string(body))
		}
		if strings.TrimSpace(string(body)) != expected {
			t.Fatalf("Counter step %d: expected %s, got %s", i+1, expected, string(body))
		}
	}
}

func TestError(t *testing.T) {
	resp, err := http.Get("http://localhost:8080/error")
	if err != nil {
		t.Fatal(err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != 500 {
		t.Fatalf("Expected 500, got %d", resp.StatusCode)
	}
	var body map[string]string
	if err := json.NewDecoder(resp.Body).Decode(&body); err != nil {
		t.Fatal(err)
	}
	if body["error"] != "Internal Server Error" {
		t.Fatalf("Expected Internal Server Error, got %v", body)
	}
}

func TestUnstableService(t *testing.T) {
	start := time.Now()
	resp, err := http.Get("http://localhost:8080/unstable-service")
	if err != nil {
		t.Fatal(err)
	}
	defer resp.Body.Close()
	elapsed := time.Since(start)
	if resp.StatusCode != 503 {
		t.Fatalf("Expected 503, got %d", resp.StatusCode)
	}
	var body map[string]string
	if err := json.NewDecoder(resp.Body).Decode(&body); err != nil {
		t.Fatal(err)
	}
	if body["error"] != "Service unavailable due to timeout" {
		t.Fatalf("Expected timeout error, got %v", body)
	}
	if elapsed < 3*time.Second {
		t.Fatalf("Expected at least 3s delay, got %v", elapsed)
	}
}

func TestNotFound(t *testing.T) {
	resp, err := http.Get("http://localhost:8080/does-not-exist")
	if err != nil {
		t.Fatal(err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != 404 {
		t.Fatalf("Expected 404, got %d", resp.StatusCode)
	}
	var body map[string]string
	if err := json.NewDecoder(resp.Body).Decode(&body); err != nil {
		t.Fatal(err)
	}
	if body["error"] != "No matching stub found" {
		t.Fatalf("Expected no matching stub, got %v", body)
	}
}
