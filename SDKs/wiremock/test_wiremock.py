import requests
import time

def test_health():
    resp = requests.get("http://localhost:8080/health")
    assert resp.status_code == 200
    assert resp.json() == {"status": "healthy"}

def test_upload():
    files = {'file': ('test.txt', b'hello world')}
    resp = requests.post("http://localhost:8080/upload", files=files)
    assert resp.status_code == 201
    assert resp.json() == {"message": "File uploaded successfully"}

def test_secure_resource():
    headers = {"Authorization": "Bearer abcdef123456"}
    resp = requests.get("http://localhost:8080/secure/resource", headers=headers)
    assert resp.status_code == 200
    assert resp.json() == {"message": "Authorized access granted"}

def test_api_json():
    headers = {"Content-Type": "application/json"}
    data = {"data": "test"}
    resp = requests.post("http://localhost:8080/api/json", json=data, headers=headers)
    assert resp.status_code == 200
    assert resp.json() == {"message": "JSON processed"}

def test_api_xml():
    headers = {"Content-Type": "application/xml"}
    xml = "<root><data>test</data></root>"
    resp = requests.post("http://localhost:8080/api/xml", data=xml, headers=headers)
    assert resp.status_code == 200
    assert resp.text == "<response>XML processed</response>"

def test_counter_scenario():
    requests.post("http://localhost:8080/__admin/scenarios/reset")
    url = "http://localhost:8080/counter"
    resp1 = requests.get(url)
    assert resp1.status_code == 200
    assert resp1.json() == {"count": 1}
    resp2 = requests.get(url)
    assert resp2.status_code == 200
    assert resp2.json() == {"count": 2}
    resp3 = requests.get(url)
    assert resp3.status_code == 200
    assert resp3.json() == {"count": 3}

def test_error():
    resp = requests.get("http://localhost:8080/error")
    assert resp.status_code == 500
    assert resp.json() == {"error": "Internal Server Error"}

def test_unstable_service():
    start = time.time()
    resp = requests.get("http://localhost:8080/unstable-service")
    elapsed = time.time() - start
    assert resp.status_code == 503
    assert resp.json() == {"error": "Service unavailable due to timeout"}
    assert elapsed >= 3  # 3 seconds delay

def test_not_found():
    resp = requests.get("http://localhost:8080/does-not-exist")
    assert resp.status_code == 404
    assert resp.json() == {"error": "No matching stub found"}

if __name__ == "__main__":
    test_health()
    test_upload()
    test_secure_resource()
    test_api_json()
    test_api_xml()
    test_counter_scenario()
    test_error()
    test_unstable_service()
    test_not_found()
    print("All WireMock endpoint tests passed!")
