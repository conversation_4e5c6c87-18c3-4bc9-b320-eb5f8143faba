import dotenv from "dotenv";
import { z } from "zod";
import { test as base, BrowserContext, Page, expect, chromium, firefox, webkit } from '@playwright/test';
import fs from 'fs';
import path from 'path';

// Load .env file
dotenv.config();

// Enums for strict typing
export enum Environment {
  DEV = "DEV",
  QA = "QA",
  STAGE = "STAGE"
}

export enum BrowserType {
  CHROMIUM = "chromium",
  FIREFOX = "firefox",
  WEBKIT = "webkit"
}

// Zod schema for validation
const SettingsSchema = z.object({
  environment: z.nativeEnum(Environment),
  baseURL: z.string().url(),
  browser: z.nativeEnum(BrowserType),
  timeout: z.number().int().positive(),
  viewport: z.object({
    width: z.number().int().positive(),
    height: z.number().int().positive(),
  }),
  flags: z.object({
    headless: z.boolean(),
    video: z.boolean(),
    trace: z.boolean(),
  }),
  parallelWorkerCount: z.number().int().positive(),
});

export type TestSettings = z.infer<typeof SettingsSchema>;

function parseBool(val: unknown, fallback: boolean): boolean {
  if (typeof val === "boolean") return val;
  if (typeof val === "string") return ["1", "true", "yes", "on"].includes(val.toLowerCase());
  return fallback;
}

// Best practice: Use PARALLEL_WORKERS as the single source of truth for both Playwright and settings.parallelWorkerCount
// Set PARALLEL_WORKERS in your environment or .env file to control parallelism everywhere
const parallelWorkers = process.env.PARALLEL_WORKERS ? Number(process.env.PARALLEL_WORKERS) : 2;

const merged = {
  // Environment: DEV by default
  environment: (process.env.ENVIRONMENT as Environment) || Environment.DEV,
  // Base URL: localhost by default
  baseURL: process.env.BASE_URL || "http://localhost:3000",
  // Browser: chromium by default
  browser: (process.env.BROWSER as BrowserType) || BrowserType.CHROMIUM,
  // Timeout: 30000ms by default
  timeout: process.env.TIMEOUT ? Number(process.env.TIMEOUT) : 30000,
  // Viewport: 1280x720 by default
  viewport: {
    width: process.env.VIEWPORT_WIDTH ? Number(process.env.VIEWPORT_WIDTH) : 1280,
    height: process.env.VIEWPORT_HEIGHT ? Number(process.env.VIEWPORT_HEIGHT) : 720,
  },
  // Flags: headless true, video/trace false by default
  flags: {
    headless: parseBool(process.env.HEADLESS, true),
    video: parseBool(process.env.VIDEO, false),
    trace: parseBool(process.env.TRACE, false),
  },
  // Parallel workers: single source of truth
  parallelWorkerCount: parallelWorkers,
};

// Validate and freeze settings
let settings: TestSettings;
try {
  settings = SettingsSchema.parse(merged);
  Object.freeze(settings);
} catch (err) {
  if (err instanceof z.ZodError) {
    console.error("\n❌ Invalid test settings:");
    for (const issue of err.issues) {
      console.error(`- ${issue.path.join('.')}: ${issue.message}`);
    }
    process.exit(1);
  }
  throw err;
}

/**
 * browser_fixture: A reusable Playwright fixture for unified browser setup.
 *
 * - Reads from TestSettings (settings)
 * - Launches the correct browser (chromium, firefox, webkit) with headless, trace, and video options
 * - Enables trace/video only on failure if flags are true
 * - Injects x-test-run-id header into every request
 * - Exposes page and context to every test (no extra import needed)
 * - Saves artifacts under artifacts/<date>/<spec>/
 * - Compatible with Playwright's fixture extension mechanism
 * - Can be overridden by consumer repos
 *
 * Usage:
 *   import { test, browser_fixture } from 'test_settings';
 *   test.use(browser_fixture);
 *   test('my test', async ({ page, context }) => { ... });
 */

const today = () => {
  const d = new Date();
  return `${d.getFullYear()}-${String(d.getMonth()+1).padStart(2,'0')}-${String(d.getDate()).padStart(2,'0')}`;
};

function getBrowserType(name: string) {
  switch (name) {
    case 'chromium': return chromium;
    case 'firefox': return firefox;
    case 'webkit': return webkit;
    default: throw new Error(`Unknown browser: ${name}`);
  }
}

export const browser_fixture = base.extend<{
  page: Page;
  context: BrowserContext;
}>({
  context: async ({}, use, testInfo) => {
    const { browser, flags } = settings;
    const runId = testInfo.project.name + '-' + testInfo.workerIndex + '-' + Date.now();
    const artifactDir = path.join('artifacts', today(), testInfo.file.replace(/[\/]/g, '_'));
    fs.mkdirSync(artifactDir, { recursive: true });

    const browserType = getBrowserType(browser);
    const context = await browserType.launchPersistentContext('', {
      headless: flags.headless,
      viewport: settings.viewport,
      recordVideo: flags.video ? { dir: artifactDir } : undefined,
      ignoreHTTPSErrors: true,
    });

    // Attach x-test-run-id to every request
    context.route('**', (route: any, request: any) => {
      const headers = {
        ...request.headers(),
        'x-test-run-id': runId,
      };
      route.continue({ headers });
    });

    // Start tracing if enabled
    if (flags.trace) {
      await context.tracing.start({ screenshots: true, snapshots: true });
    }

    await use(context);

    // On failure, save trace and video
    if (flags.trace && testInfo.status !== testInfo.expectedStatus) {
      await context.tracing.stop({ path: path.join(artifactDir, 'trace.zip') });
    } else if (flags.trace) {
      await context.tracing.stop();
    }
    await context.close();
  },
  page: async ({ context }, use) => {
    await use(context.pages()[0] || await context.newPage());
  },
});

export * from '@playwright/test';
export { settings }; 