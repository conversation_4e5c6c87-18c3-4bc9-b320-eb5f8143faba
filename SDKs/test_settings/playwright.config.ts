import { defineConfig, devices } from '@playwright/test';

// Helper to parse booleans from env
function parseBool(val: unknown, fallback: boolean): boolean {
  if (typeof val === 'boolean') return val;
  if (typeof val === 'string') return ['1', 'true', 'yes', 'on'].includes(val.toLowerCase());
  return fallback;
}

const allProjects = [
  {
    name: 'chromium',
    use: { ...devices['Desktop Chrome'] },
  },
  {
    name: 'firefox',
    use: { ...devices['Desktop Firefox'] },
  },
  {
    name: 'webkit',
    use: { ...devices['Desktop Safari'] },
  },
];

const selectedProjects = process.env.BROWSER
  ? allProjects.filter(p => p.name === process.env.BROWSER)
  : allProjects;

// Conditionally set viewport only if PLAYWRIGHT_DEVICE is not set
const baseUse: any = {
  baseURL: process.env.BASE_URL || 'http://localhost:3000',
  headless: parseBool(process.env.HEADLESS, true),
  actionTimeout: process.env.ACTION_TIMEOUT ? Number(process.env.ACTION_TIMEOUT) : 0,
  trace: parseBool(process.env.TRACE, false) ? 'on' : 'off',
  video: parseBool(process.env.VIDEO, false) ? 'on' : 'off',
  screenshot: parseBool(process.env.SCREENSHOT, false) ? 'on' : 'off',
  ignoreHTTPSErrors: parseBool(process.env.IGNORE_HTTPS_ERRORS, false),
  ...devices[process.env.PLAYWRIGHT_DEVICE || 'Desktop Chrome'],
};

if (!process.env.PLAYWRIGHT_DEVICE) {
  baseUse.viewport = {
    width: process.env.VIEWPORT_WIDTH ? Number(process.env.VIEWPORT_WIDTH) : 1280,
    height: process.env.VIEWPORT_HEIGHT ? Number(process.env.VIEWPORT_HEIGHT) : 720,
  };
}

// Set PARALLEL_WORKERS in your environment or .env file to control parallelism everywhere
const parallelWorkers = process.env.PARALLEL_WORKERS ? Number(process.env.PARALLEL_WORKERS) : (process.env.CI ? 2 : undefined);

export default defineConfig({
  testDir: process.env.TEST_DIR || './tests',
  timeout: process.env.TIMEOUT ? Number(process.env.TIMEOUT) : 30_000,
  expect: {
    timeout: process.env.EXPECT_TIMEOUT ? Number(process.env.EXPECT_TIMEOUT) : 5_000,
  },
  fullyParallel: parseBool(process.env.FULLY_PARALLEL, true),
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: parallelWorkers,
  reporter: process.env.PLAYWRIGHT_REPORTER || 'html',
  use: baseUse,
  projects: selectedProjects,
  outputDir: process.env.PLAYWRIGHT_OUTPUT_DIR || 'test-results',
  globalSetup: process.env.PLAYWRIGHT_GLOBAL_SETUP,
  globalTeardown: process.env.PLAYWRIGHT_GLOBAL_TEARDOWN,
  /*
    To extend this config in a consumer project:
    import baseConfig from 'test_settings/playwright.config';
    export default defineConfig({
      ...baseConfig,
      // override or add more config here
    });
  */
});
