import logging
import j<PERSON>
import pytest
from harness.harness_service import User, UserService
from harness.harness_utilities import FakePostgres, FakeRedis, MockSMTP


@pytest.fixture
def fake_postgres():
    """Fixture for PostgreSQL container"""
    postgres = FakePostgres()
    postgres.initialize()
    
    # Add test data
    conn = postgres.get_connection()
    cursor = conn.cursor()
    cursor.execute("INSERT INTO users (id, name, email) VALUES (%s, %s, %s)",
                  ("user1", "Test User", "<EMAIL>"))
    conn.commit()
    cursor.close()
    
    yield postgres
    
    postgres.cleanup()


@pytest.fixture
def fake_redis():
    """Fixture for Redis container"""
    redis = FakeRedis()
    redis.initialize()
    yield redis
    redis.cleanup()


@pytest.fixture
def mock_smtp():
    """Fixture for SMTP mock"""
    return MockSMTP()


@pytest.fixture
def logger():
    """Fixture for logger"""
    logging.basicConfig(level=logging.INFO)
    return logging.getLogger("test")


@pytest.fixture
def user_service(fake_postgres, fake_redis, mock_smtp, logger):
    """Fixture for UserService"""
    return UserService(
        db_connection=fake_postgres.get_connection(),
        redis_client=fake_redis.get_client(),
        smtp_sender=mock_smtp.send_email,
        logger=logger
    )


class TestUserService:
    """Test suite for UserService"""
    
    def test_get_user_from_db(self, user_service, fake_redis):
        """Test getting a user from the database when not in cache"""
        # Context with correlation ID
        ctx = {"x-correlation-id": "test-123"}
        
        # Get the user
        user = user_service.get_user(ctx, "user1")
        
        # Verify the user
        assert user is not None
        assert user.id == "user1"
        assert user.name == "Test User"
        assert user.email == "<EMAIL>"
        
        # Verify it was cached
        cache_key = "user:user1"
        cached_value = fake_redis.get_client().get(cache_key)
        assert cached_value is not None
        
        cached_user = json.loads(cached_value)
        assert cached_user["id"] == "user1"
        assert cached_user["name"] == "Test User"
        assert cached_user["email"] == "<EMAIL>"
    
    def test_get_user_from_cache(self, user_service, fake_redis):
        """Test getting a user from the cache"""
        # Set up cache with a user
        user_data = {"id": "user2", "name": "Cached User", "email": "<EMAIL>"}
        fake_redis.get_client().setex("user:user2", 600, json.dumps(user_data))
        
        # Context with correlation ID
        ctx = {"x-correlation-id": "test-456"}
        
        # Get the user
        user = user_service.get_user(ctx, "user2")
        
        # Verify the user comes from cache, not DB
        assert user is not None
        assert user.id == "user2"
        assert user.name == "Cached User"
        assert user.email == "<EMAIL>"
    
    def test_get_nonexistent_user(self, user_service):
        """Test getting a user that doesn't exist"""
        # Context with correlation ID
        ctx = {"x-correlation-id": "test-789"}
        
        # Get a non-existent user
        user = user_service.get_user(ctx, "nonexistent")
        
        # Verify the result is None
        assert user is None
    
    def test_send_welcome_email(self, user_service, mock_smtp):
        """Test sending a welcome email"""
        # Context with correlation ID
        ctx = {"x-correlation-id": "test-email-123"}
        
        # Create a user
        user = User(id="user1", name="Test User", email="<EMAIL>")
        
        # Send welcome email
        result = user_service.send_welcome_email(ctx, user)
        
        # Verify the result
        assert result is True
        
        # Verify the email was sent
        emails = mock_smtp.get_sent_emails()
        assert len(emails) == 1
        
        email = emails[0]
        assert email["from"] == "<EMAIL>"
        assert email["to"] == ["<EMAIL>"]
        assert email["subject"] == "Welcome to our service"
        assert "Hello Test User" in email["body"]