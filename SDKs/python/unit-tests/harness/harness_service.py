import json
import logging
from typing import Dict, List, Optional, Callable
import redis
import psycopg2
from psycopg2.extras import RealDictCursor


class User:
    """Represents a user in the system"""
    
    def __init__(self, id: str, name: str, email: str):
        self.id = id
        self.name = name
        self.email = email
    
    def to_dict(self) -> Dict:
        """Convert User to dictionary"""
        return {
            "id": self.id,
            "name": self.name,
            "email": self.email
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'User':
        """Create User from dictionary"""
        return cls(
            id=data.get("id", ""),
            name=data.get("name", ""),
            email=data.get("email", "")
        )


class UserService:
    """Provides user management functionality"""
    
    def __init__(
        self,
        db_connection,
        redis_client: redis.Redis,
        smtp_sender: Callable[[str, List[str], str, str], None],
        logger: logging.Logger,
        cache_timeout: int = 600  # 10 minutes in seconds
    ):
        self.db = db_connection
        self.redis_client = redis_client
        self.smtp_sender = smtp_sender
        self.logger = logger
        self.cache_timeout = cache_timeout
    
    def get_user(self, ctx: Dict, user_id: str) -> Optional[User]:
        """Retrieves a user by ID, using cache when available"""
        corr_id = get_correlation_id(ctx)
        self.logger.info(f"Getting user, userID={user_id}, correlationID={corr_id}")
        
        # First check Redis cache
        cache_key = f"user:{user_id}"
        try:
            cached_user = self.redis_client.get(cache_key)
            if cached_user:
                # Cache hit
                self.logger.info(f"User found in cache, userID={user_id}, correlationID={corr_id}")
                user_dict = json.loads(cached_user)
                return User.from_dict(user_dict)
        except redis.RedisError as e:
            # Unexpected Redis error
            self.logger.error(f"Redis error: {e}")
            return None
        
        # If not in cache, get from database
        query = "SELECT id, name, email FROM users WHERE id = %s"
        try:
            cursor = self.db.cursor(cursor_factory=RealDictCursor)
            cursor.execute(query, (user_id,))
            row = cursor.fetchone()
            cursor.close()
            
            if not row:
                self.logger.warning(f"User not found, userID={user_id}, correlationID={corr_id}")
                return None
            
            user = User(id=row['id'], name=row['name'], email=row['email'])
            
            # Store in cache
            try:
                user_json = json.dumps(user.to_dict())
                self.redis_client.setex(cache_key, self.cache_timeout, user_json)
                self.logger.info(f"User cached, userID={user_id}, correlationID={corr_id}")
            except Exception as e:
                # Log the error but don't fail the request
                self.logger.error(f"Failed to cache user, userID={user_id}, correlationID={corr_id}, error={e}")
            
            return user
            
        except psycopg2.Error as e:
            self.logger.error(f"Database error: {e}")
            return None
    
    def send_welcome_email(self, ctx: Dict, user: User) -> bool:
        """Sends a welcome email to a user"""
        corr_id = get_correlation_id(ctx)
        self.logger.info(f"Sending welcome email, email={user.email}, correlationID={corr_id}")
        
        from_email = "<EMAIL>"
        to = [user.email]
        subject = "Welcome to our service"
        body = f"Hello {user.name}, welcome to our service!"
        
        try:
            self.smtp_sender(from_email, to, subject, body)
            self.logger.info(f"Welcome email sent, email={user.email}, correlationID={corr_id}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to send welcome email, email={user.email}, correlationID={corr_id}, error={e}")
            return False


def get_correlation_id(ctx: Optional[Dict]) -> str:
    """Extracts the correlation ID from the context"""
    if ctx is None:
        return ""
    
    return ctx.get("x-correlation-id", "")
