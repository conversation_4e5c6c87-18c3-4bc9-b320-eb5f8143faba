import time
import logging
from typing import List, Dict, Optional
from test_containers.infrastructure_manager import InfrastructureManager
from test_containers.brick_config import BrickConfig, HealthCheck
import redis
import psycopg2
from psycopg2.extras import RealDictCursor


class FakePostgres:
    def __init__(self):
        self.container_port = "5432"
        self.host_port = "55432"
        self.db_name = "testdb"
        self.user = "postgres"
        self.password = "postgres"
        self.instance_name = "pg-test"

        self.brick_config = BrickConfig(
            brick_type="postgres",
            instance_name=self.instance_name,
            environment_variables={
                "POSTGRES_PASSWORD": self.password,
                "POSTGRES_USER": self.user,
                "POSTGRES_DB": self.db_name,
            },
            exposed_port=self.container_port,
            host_port=self.host_port,
            health_check=HealthCheck(
                test=["pg_isready", "-U", self.user],
                interval=1,
                retries=10,
                start_period=0,
                timeout=2
            )
        )

        self.infra = InfrastructureManager([self.brick_config])
        self.connection = None

    def initialize(self) -> None:
        self.infra.start_all()
        time.sleep(2)  # Let Postgres warm up

        conn_str = self._get_connection_string()
        for attempt in range(10):
            try:
                self.connection = psycopg2.connect(conn_str)
                break
            except psycopg2.OperationalError:
                time.sleep(1)
        self._init_schema()

    def _get_connection_string(self) -> str:
        return f"postgresql://{self.user}:{self.password}@localhost:{self.host_port}/{self.db_name}"

    def _init_schema(self) -> None:
        cursor = self.connection.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id VARCHAR(50) PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100) NOT NULL
            )
        ''')
        self.connection.commit()
        cursor.close()

    def get_connection(self) -> psycopg2.extensions.connection:
        return self.connection

    def cleanup(self) -> None:
        if self.connection:
            self.connection.close()
        self.infra.stop_all()


class FakeRedis:
    def __init__(self):
        self.container_port = "6379"
        self.host_port = "63791"
        self.instance_name = "redis-test"

        self.brick_config = BrickConfig(
            brick_type="dapr-redis",
            instance_name=self.instance_name,
            exposed_port=self.container_port,
            host_port=self.host_port,
            health_check=HealthCheck(
                test=["redis-cli", "ping"],
                interval=1,
                retries=10,
                start_period=0,
                timeout=2
            )
        )

        self.infra = InfrastructureManager([self.brick_config])
        self.client = None
        self.hit_count: Dict[str, int] = {}

    def initialize(self) -> None:
        self.infra.start_all()
        time.sleep(2)  # Let Redis warm up

        for _ in range(10):
            try:
                self.client = redis.Redis(host="localhost", port=int(self.host_port))
                if self.client.ping():
                    break
            except redis.ConnectionError:
                time.sleep(1)

    def get_client(self) -> redis.Redis:
        return self.client

    def get_with_counter(self, key: str) -> Optional[bytes]:
        self.hit_count[key] = self.hit_count.get(key, 0) + 1
        return self.client.get(key)

    def get_hit_count(self, key: str) -> int:
        return self.hit_count.get(key, 0)

    def cleanup(self) -> None:
        if self.client:
            self.client.close()
        self.infra.stop_all()


class MockSMTP:
    def __init__(self):
        self.sent_emails: List[Dict] = []

    def send_email(self, from_addr: str, to_addrs: List[str], subject: str, body: str) -> None:
        self.sent_emails.append({
            "from": from_addr,
            "to": to_addrs,
            "subject": subject,
            "body": body
        })

    def get_sent_emails(self) -> List[Dict]:
        return self.sent_emails

    def clear(self) -> None:
        self.sent_emails = []
