from typing import Callable, Dict, List, Optional, Any, Union
from dataclasses import dataclass, field

@dataclass
class HealthCheckConfig:
    """Configuration for container health checks.
    
    Attributes:
        test: Command to run for checking container health
        interval: Time in seconds between health checks
        retries: Number of consecutive failures needed to report unhealthy
        start_period: Grace period in seconds before starting health checks
        timeout: Maximum time in seconds to wait for one health check to complete
    """
    test: List[str]
    interval: int  # seconds
    retries: int
    start_period: int  # seconds
    timeout: int  # seconds


@dataclass
class BrickConfig:
    """Configuration for a container brick.
    
    Attributes:
        brick_type: Identifies the type of container (e.g., "postgres", "redis")
        instance_name: Name assigned to the container
        command_arguments: Arguments passed to the container entrypoint
        environment_variables: Environment variables set in the container
        exposed_port: Container port to expose (e.g., "5432/tcp")
        host_port: Port on the host to bind to the exposed container port
        volumes: Volume mappings from host to container as "<host_path>:<container_path>"
        health_check: Container health check configuration
        networks: Docker networks to connect the container to
        network_aliases: Aliases for the container in each network
        host_config_modifier: Function to modify the container's host configuration
        custom_wait_strategy: Strategy for waiting for the container to be ready
    """
    brick_type: str
    instance_name: str
    command_arguments: Optional[List[str]] = None
    environment_variables: Optional[Dict[str, str]] = field(default_factory=dict)
    exposed_port: Optional[str] = None
    host_port: Optional[str] = None
    volumes: Optional[List[str]] = field(default_factory=list)  # list of "<host>:<container>" strings
    health_check: Optional[HealthCheckConfig] = None
    networks: Optional[List[str]] = field(default_factory=list)
    network_aliases: Optional[Dict[str, List[str]]] = field(default_factory=dict)
    host_config_modifier: Optional[Callable[..., Any]] = None
    custom_wait_strategy: Optional[Callable[..., Any]] = None
