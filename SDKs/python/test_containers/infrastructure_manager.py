from typing import List, Optional, Dict, Any
import logging
from contextlib import contextmanager

from test_containers.brick_config import BrickConfig
from test_containers.container_brick import ContainerBrick
from test_containers.container_brick_factory import new_container_brick


logger = logging.getLogger(__name__)


class InfrastructureManager:
    """Manages the lifecycle of a collection of container bricks.
    
    This class is responsible for starting and stopping multiple container bricks
    in the correct order.
    """
    
    def __init__(self, configs: List[BrickConfig]):
        """Initialize the infrastructure manager with the given configurations.
        
        Args:
            configs: List of brick configurations to manage
        """
        self.bricks: List[ContainerBrick] = []
        for config in configs:
            brick = new_container_brick(config)
            self.bricks.append(brick)
    
    def start_all(self, context: Optional[Any] = None) -> None:
        """Start all container bricks in the order they were added.
        
        Args:
            context: Optional context object for the start operation
            
        Raises:
            Exception: If any brick fails to start
        """
        started_bricks = []
        try:
            for brick in self.bricks:
                logger.info(f"Starting {brick.config.instance_name}...")
                brick.start(context)
                started_bricks.append(brick)
        except Exception as e:
            logger.error(f"Failed to start brick: {e}")
            # Attempt to stop any bricks that were started
            for brick in reversed(started_bricks):
                try:
                    logger.info(f"Stopping {brick.config.instance_name} after failure...")
                    brick.stop(context)
                except Exception as stop_error:
                    logger.error(f"Failed to stop {brick.config.instance_name}: {stop_error}")
            raise

    def stop_all(self, context: Optional[Any] = None) -> None:
        """Stop all container bricks in reverse order.
        
        Args:
            context: Optional context object for the stop operation
        """
        errors = []
        for brick in reversed(self.bricks):
            try:
                logger.info(f"Stopping {brick.config.instance_name}...")
                brick.stop(context)
            except Exception as e:
                logger.error(f"Failed to stop {brick.config.instance_name}: {e}")
                errors.append((brick.config.instance_name, str(e)))
        
        if errors:
            error_msg = "; ".join([f"{name}: {error}" for name, error in errors])
            logger.warning(f"Failed to stop some containers: {error_msg}")
    
    @contextmanager
    def managed_infrastructure(self, context: Optional[Any] = None):
        """Context manager for starting and stopping infrastructure.
        
        This is a convenient way to ensure that infrastructure is properly cleaned up,
        even if an exception occurs.
        
        Args:
            context: Optional context object for the operations
            
        Yields:
            The infrastructure manager itself
        """
        try:
            self.start_all(context)
            yield self
        finally:
            self.stop_all(context)
