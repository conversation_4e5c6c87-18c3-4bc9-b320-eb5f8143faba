import time
import logging
import subprocess
import json
import os
from typing import Optional, Dict, List, Any, Tuple

from test_containers.brick_config import BrickConfig, HealthCheckConfig
from test_containers.container_brick import ContainerBrick


logger = logging.getLogger(__name__)


def get_image_for_brick_type(brick_type: str) -> str:
    """Get the Docker image for a specific brick type.
    
    Args:
        brick_type: The type of brick
        
    Returns:
        The Docker image name for the brick type
        
    Raises:
        ValueError: If the brick type is unknown
    """
    image_map = {
        "dapr": "daprio/daprd:1.14.4",
        "dapr-zipkin": "openzipkin/zipkin",
        "dapr-redis": "redis:latest",
        "dapr-placement": "daprio/dapr-placement:latest",
        "dapr-sentry": "daprio/dapr-sentry:latest",
        "dapr-dashboard": "daprio/dapr-dashboard:latest",
        "mqtt": "eclipse-mosquitto:latest",
        "postgres": "postgres:15-alpine",
        "gin-go": "platform-tests-gin-go:latest",
        "fib-calc": "platform-tests-fib-calc:latest",
        "k6-gin": "grafana/k6:latest",
        "mongodb": "mongo:latest",
    }
    
    if brick_type not in image_map:
        raise ValueError(f"Unknown brick type: {brick_type}")
    
    return image_map[brick_type]


class GenericContainerBrick(ContainerBrick):
    """Generic implementation of a container brick using subprocess to call Docker CLI.
    
    This class provides a generic implementation of the ContainerBrick interface
    that can be used for most container types by invoking Docker CLI commands.
    """
    
    def __init__(self, config: BrickConfig):
        """Initialize the generic container brick.
        
        Args:
            config: The configuration for the brick
        """
        self.config = config
        self._container_id = None
    
    def start(self, context: Optional[Any] = None) -> None:
        """Start the container.
        
        Args:
            context: Optional context object for the start operation
            
        Raises:
            Exception: If the container fails to start
        """
        image = get_image_for_brick_type(self.config.brick_type)
        
        try:
            # Build docker run command
            cmd = ['docker', 'run', '-d']
            
            # Add name
            cmd.extend(['--name', self.config.instance_name])
            
            # Add labels
            cmd.extend(['--label', 'com.docker.compose.project=platform-tests'])
            
            # Add environment variables
            if self.config.environment_variables:
                for key, value in self.config.environment_variables.items():
                    cmd.extend(['-e', f"{key}={value}"])
            
            # Add port mappings
            if self.config.exposed_port:
                port_spec = f"{self.config.host_port}:{self.config.exposed_port}" if self.config.host_port else self.config.exposed_port
                cmd.extend(['-p', port_spec])
            
            # Add volumes
            if self.config.volumes:
                for volume in self.config.volumes:
                    cmd.extend(['-v', volume])
            
            # Add networks
            if self.config.networks:
                for network in self.config.networks:
                    cmd.extend(['--network', network])
            
            # Add image
            cmd.append(image)
            
            # Add command arguments
            if self.config.command_arguments:
                cmd.extend(self.config.command_arguments)
            
            # Start container
            logger.info(f"Starting container {self.config.instance_name} with image {image}...")
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            self._container_id = result.stdout.strip()
            logger.info(f"Container {self.config.instance_name} started with ID: {self._container_id}")
            
            # Add network aliases if any
            if self.config.network_aliases:
                for network, aliases in self.config.network_aliases.items():
                    for alias in aliases:
                        self._add_network_alias(network, alias)
            
            # Health check
            if self.config.health_check:
                self._perform_health_check()
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to start container {self.config.instance_name}: {e.stderr}")
            raise RuntimeError(f"Failed to start container: {e.stderr}")
        except Exception as e:
            logger.error(f"Failed to start container {self.config.instance_name}: {e}")
            # Attempt to clean up if container was created
            if self._container_id:
                try:
                    self.stop(context)
                except Exception as stop_error:
                    logger.error(f"Failed to stop container after start failure: {stop_error}")
            raise
    
    def _add_network_alias(self, network: str, alias: str) -> None:
        """Add a network alias to the container.
        
        Args:
            network: The network to add the alias to
            alias: The alias to add
            
        Raises:
            RuntimeError: If the alias cannot be added
        """
        if not self._container_id:
            raise RuntimeError("Container is not running")
        
        try:
            # Get network details
            net_inspect_cmd = ['docker', 'network', 'inspect', network]
            net_result = subprocess.run(net_inspect_cmd, capture_output=True, text=True, check=True)
            net_data = json.loads(net_result.stdout)
            
            # Check if container is connected to the network
            containers = net_data[0].get('Containers', {})
            if not any(c.get('Name') == self.config.instance_name for c in containers.values()):
                # Connect container to network
                connect_cmd = ['docker', 'network', 'connect', '--alias', alias, network, self._container_id]
                subprocess.run(connect_cmd, capture_output=True, text=True, check=True)
                logger.info(f"Connected {self.config.instance_name} to network {network} with alias {alias}")
            else:
                logger.info(f"Container {self.config.instance_name} already connected to network {network}")
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to add network alias: {e.stderr}")
            raise RuntimeError(f"Failed to add network alias: {e.stderr}")
    
    def _perform_health_check(self) -> None:
        """Perform health check on the container.
        
        Raises:
            Exception: If the health check fails
        """
        if not self._container_id or not self.config.health_check:
            return
        
        health_check = self.config.health_check
        retries = health_check.retries
        interval = health_check.interval
        
        logger.info(f"Performing health check for {self.config.instance_name}...")
        
        for attempt in range(retries):
            try:
                # Execute the health check command in the container
                exec_cmd = ['docker', 'exec', self._container_id]
                exec_cmd.extend(health_check.test)
                
                result = subprocess.run(exec_cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    logger.info(f"Health check passed for {self.config.instance_name}")
                    return
                logger.warning(
                    f"Health check attempt {attempt+1}/{retries} failed for {self.config.instance_name}: "
                    f"exit code {result.returncode}"
                )
            except Exception as e:
                logger.warning(
                    f"Health check attempt {attempt+1}/{retries} failed for {self.config.instance_name}: {e}"
                )
            
            # Not the last attempt, so sleep and retry
            if attempt < retries - 1:
                time.sleep(interval)
        
        raise RuntimeError(f"Health check failed for {self.config.instance_name} after {retries} attempts")
    
    def stop(self, context: Optional[Any] = None) -> None:
        """Stop the container.
        
        Args:
            context: Optional context object for the stop operation
            
        Raises:
            Exception: If the container fails to stop
        """
        if not self._container_id:
            logger.warning(f"Container {self.config.instance_name} is not running")
            return
        
        try:
            logger.info(f"Stopping container {self.config.instance_name}...")
            stop_cmd = ['docker', 'stop', self._container_id]
            subprocess.run(stop_cmd, capture_output=True, text=True, check=True)
            
            # Also remove the container
            rm_cmd = ['docker', 'rm', '-f', self._container_id]
            subprocess.run(rm_cmd, capture_output=True, text=True, check=True)
            
            logger.info(f"Container {self.config.instance_name} stopped and removed.")
            self._container_id = None
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to stop container {self.config.instance_name}: {e.stderr}")
            raise RuntimeError(f"Failed to stop container: {e.stderr}")
    
    @property
    def container_id(self) -> str:
        """Get the ID of the container.
        
        Returns:
            The ID of the container
            
        Raises:
            ValueError: If the container is not running
        """
        if not self._container_id:
            raise ValueError(f"Container {self.config.instance_name} is not running")
        
        return self._container_id
    
    def get_mapped_port(self, port: str) -> str:
        """Get the host port mapped to the given container port.
        
        Args:
            port: The container port to get the mapping for
            
        Returns:
            The host port mapped to the container port
            
        Raises:
            ValueError: If the port is not mapped or the container is not running
        """
        if not self._container_id:
            raise ValueError(f"Container {self.config.instance_name} is not running")
        
        try:
            # Get port mapping from docker port command
            cmd = ['docker', 'port', self._container_id, port]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # Parse the output (format: 0.0.0.0:12345)
            port_mapping = result.stdout.strip()
            if not port_mapping:
                raise ValueError(f"No port mapping found for port {port}")
            
            # Extract the host port
            mapped_port = port_mapping.split(':')[-1]
            return mapped_port
        except subprocess.CalledProcessError as e:
            raise ValueError(f"Failed to get mapped port for {port}: {e.stderr}")
    
    def exec(self, command: List[str]) -> Tuple[int, str, str]:
        """Execute a command in the container.
        
        Args:
            command: The command to execute
            
        Returns:
            A tuple of (exit_code, stdout, stderr)
            
        Raises:
            ValueError: If the container is not running
        """
        if not self._container_id:
            raise ValueError(f"Container {self.config.instance_name} is not running")
        
        try:
            # Build docker exec command
            cmd = ['docker', 'exec', self._container_id]
            cmd.extend(command)
            
            # Execute the command
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.returncode, result.stdout, result.stderr
        except Exception as e:
            raise RuntimeError(f"Failed to execute command in container: {e}")
    
    def get_logs(self) -> str:
        """Get the logs from the container.
        
        Returns:
            The container logs
            
        Raises:
            ValueError: If the container is not running
        """
        if not self._container_id:
            raise ValueError(f"Container {self.config.instance_name} is not running")
        
        try:
            # Get container logs
            cmd = ['docker', 'logs', self._container_id]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return result.stdout
        except subprocess.CalledProcessError as e:
            raise RuntimeError(f"Failed to get logs from container: {e.stderr}")
