from typing import Dict, Type, Optional
from test_containers.brick_config import BrickConfig
from test_containers.container_brick import ContainerBrick
from test_containers.generic_container_brick import GenericContainerBrick


class ContainerBrickFactory:
    """Factory for creating container bricks.
    
    This factory creates container bricks based on the provided configuration.
    It supports registering custom brick implementations for specific brick types.
    """
    
    def __init__(self):
        self._brick_impls: Dict[str, Type[ContainerBrick]] = {}
    
    def register_brick_impl(self, brick_type: str, brick_class: Type[ContainerBrick]) -> None:
        """Register a brick implementation for a specific brick type.
        
        Args:
            brick_type: The brick type to register the implementation for
            brick_class: The brick class to use for the brick type
        """
        self._brick_impls[brick_type] = brick_class
    
    def create_brick(self, config: BrickConfig) -> ContainerBrick:
        """Create a container brick based on the provided configuration.
        
        Args:
            config: The configuration for the brick
            
        Returns:
            A container brick instance
        """
        brick_class = self._brick_impls.get(config.brick_type, GenericContainerBrick)
        return brick_class(config)


# Global factory instance for backward compatibility
_factory = ContainerBrickFactory()


def new_container_brick(config: BrickConfig) -> ContainerBrick:
    """Create a new container brick based on the provided configuration.
    
    This is a package-level function for backward compatibility.
    
    Args:
        config: The configuration for the brick
        
    Returns:
        A container brick instance
    """
    return _factory.create_brick(config)


def register_brick_impl(brick_type: str, brick_class: Type[ContainerBrick]) -> None:
    """Register a brick implementation for a specific brick type.
    
    This is a package-level function for backward compatibility.
    
    Args:
        brick_type: The brick type to register the implementation for
        brick_class: The brick class to use for the brick type
    """
    _factory.register_brick_impl(brick_type, brick_class)
