"""
Module for Docker network management.

This module provides functions for creating, connecting to, and cleaning up Docker networks.
It uses subprocess to run Docker CLI commands to interact with the Docker daemon.
"""

import logging
import os
import subprocess
import json
from typing import List, Optional, Dict, Any, Union

logger = logging.getLogger(__name__)


class NetworkManager:
    """Manager for Docker networks using subprocess calls to Docker CLI.
    
    This class provides methods for creating, retrieving, and removing Docker networks.
    """
    
    def __init__(self):
        """Initialize the network manager and verify Docker is available."""
        try:
            # Check if Docker CLI is available
            result = subprocess.run(
                ['docker', 'version', '--format', '{{.Server.Version}}'],
                capture_output=True,
                text=True,
                check=True
            )
            version = result.stdout.strip()
            logger.info(f"Successfully connected to Docker daemon (version {version})")
        except subprocess.CalledProcessError as err:
            logger.error(f"Error connecting to Docker: {err.stderr}")
            raise RuntimeError(f"Failed to connect to Docker daemon: {err.stderr}")
        except Exception as err:
            logger.error(f"Unexpected error: {err}")
            raise
    
    def create_network(self, network_name: str, driver: str = "bridge", 
                      internal: bool = False, labels: Optional[Dict[str, str]] = None) -> str:
        """Create a Docker network with the given name and parameters.
        
        Args:
            network_name: The desired name for the Docker network
            driver: The network driver to use (default: "bridge")
            internal: Whether the network should be internal (not connected to outside)
            labels: Labels to add to the network
            
        Returns:
            The ID of the created network
            
        Raises:
            RuntimeError: If there is an error creating the Docker network
        """
        try:
            cmd = ['docker', 'network', 'create', '--driver', driver]
            
            if internal:
                cmd.append('--internal')
            
            if labels:
                for key, value in labels.items():
                    cmd.extend(['--label', f"{key}={value}"])
            
            cmd.append(network_name)
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            network_id = result.stdout.strip()
            logger.info(f"Created network: {network_name} (ID: {network_id})")
            return network_id
        except subprocess.CalledProcessError as err:
            # If the network already exists, return its ID
            if "already exists" in err.stderr:
                logger.info(f"Network {network_name} already exists, retrieving its ID")
                return self.get_network_id(network_name)
            logger.error(f"Error creating Docker network: {err.stderr}")
            raise RuntimeError(f"Failed to create Docker network: {err.stderr}")
    
    def get_network_id(self, network_name: str) -> str:
        """Get a Docker network ID by name.
        
        Args:
            network_name: The name of the Docker network
            
        Returns:
            The ID of the Docker network
            
        Raises:
            RuntimeError: If there is an error retrieving the Docker network
        """
        try:
            result = subprocess.run(
                ['docker', 'network', 'inspect', '--format', '{{.ID}}', network_name],
                capture_output=True,
                text=True,
                check=True
            )
            network_id = result.stdout.strip()
            return network_id
        except subprocess.CalledProcessError as err:
            logger.error(f"Error retrieving Docker network: {err.stderr}")
            raise RuntimeError(f"Failed to retrieve Docker network: {err.stderr}")
    
    def remove_network(self, network_name: str) -> bool:
        """Remove a Docker network by name.
        
        Args:
            network_name: The name of the Docker network
            
        Returns:
            True if the network was removed, False otherwise
        """
        try:
            subprocess.run(
                ['docker', 'network', 'rm', network_name],
                capture_output=True,
                text=True,
                check=True
            )
            logger.info(f"Removed network: {network_name}")
            return True
        except subprocess.CalledProcessError as err:
            logger.warning(f"Error removing Docker network: {err.stderr}")
            return False


# Global network manager instance for backward compatibility
_network_manager = NetworkManager()


def create_network(network_name: str, driver: str = "bridge",
                 internal: bool = False, labels: Optional[Dict[str, str]] = None) -> str:
    """Create a Docker network with the given name and returns the network name.
    
    This is a package-level function for backward compatibility.
    
    Args:
        network_name: The desired name for the Docker network
        driver: The network driver to use (default: "bridge")
        internal: Whether the network should be internal (not connected to outside)
        labels: Labels to add to the network
        
    Returns:
        The name of the created network
        
    Raises:
        RuntimeError: If there is an error creating the Docker network
    """
    _network_manager.create_network(network_name, driver, internal, labels)
    return network_name


def get_network_id(network_name: str) -> str:
    """Get a Docker network ID by name.
    
    This is a package-level function for backward compatibility.
    
    Args:
        network_name: The name of the Docker network
        
    Returns:
        The ID of the Docker network
        
    Raises:
        RuntimeError: If there is an error retrieving the Docker network
    """
    return _network_manager.get_network_id(network_name)


def remove_network(network_name: str) -> bool:
    """Remove a Docker network by name.
    
    This is a package-level function for backward compatibility.
    
    Args:
        network_name: The name of the Docker network
        
    Returns:
        True if the network was removed, False otherwise
    """
    return _network_manager.remove_network(network_name)


# The following conditional provides a simple test routine if this module is executed directly.
if __name__ == '__main__':
    import sys
    logging.basicConfig(level=logging.INFO)

    if len(sys.argv) != 2:
        print("Usage: python network.py <network_name>")
        sys.exit(1)

    network_name = sys.argv[1]
    try:
        created_network = create_network(network_name)
        print(f"Successfully created Docker network: {created_network}")
        
        # Test retrieving the network ID
        network_id = get_network_id(network_name)
        print(f"Network ID: {network_id}")
        
        # Test removing the network
        if remove_network(network_name):
            print(f"Successfully removed Docker network: {network_name}")
        else:
            print(f"Failed to remove Docker network: {network_name}")
    except Exception as e:
        print(f"An error occurred: {e}")
        sys.exit(1)
