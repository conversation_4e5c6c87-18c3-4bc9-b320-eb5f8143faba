from abc import ABC, abstractmethod
from typing import Optional, Any, Dict


class ContainerBrick(ABC):
    """Abstract base class for container bricks.
    
    Defines the common interface for all container bricks, which are
    responsible for managing the lifecycle of a Docker container.
    """
    
    @abstractmethod
    def start(self, context: Optional[Any] = None) -> None:
        """Start the container.
        
        Args:
            context: Optional context object for the start operation
            
        Raises:
            Exception: If the container fails to start
        """
        pass

    @abstractmethod
    def stop(self, context: Optional[Any] = None) -> None:
        """Stop the container.
        
        Args:
            context: Optional context object for the stop operation
            
        Raises:
            Exception: If the container fails to stop
        """
        pass
    
    @property
    @abstractmethod
    def container_id(self) -> str:
        """Get the ID of the container.
        
        Returns:
            The ID of the container
        """
        pass
    
    @abstractmethod
    def get_mapped_port(self, port: str) -> str:
        """Get the host port mapped to the given container port.
        
        Args:
            port: The container port to get the mapping for
            
        Returns:
            The host port mapped to the container port
            
        Raises:
            ValueError: If the port is not mapped
        """
        pass
