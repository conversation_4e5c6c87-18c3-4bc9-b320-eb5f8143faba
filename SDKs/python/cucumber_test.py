#!/usr/bin/env python3

import os
import sys
import argparse
import glob

try:
    from behave.__main__ import main as behave_main
except ImportError:
    print("Error: behave package not installed. Run 'pip install -r requirements.txt' to install dependencies.")
    sys.exit(1)

# Add the current directory to path to ensure imports work
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def run_cucumber_tests(tags=None):
    """
    Run the cucumber tests using behave framework
    """
    # Get tags from argument or environment variables
    if not tags:
        tags = os.environ.get('BEHAVE_TAGS', '')
    
    # Exit if no tags are specified
    if not tags:
        print("Error: No tags specified. Please provide tags using --tags argument or BEHAVE_TAGS environment variable.")
        return 1

    args = ['--format', 'pretty', '--no-skipped']

    print(f"Filtering tests with tags: {tags}")
    args.extend(['--tags', tags])
    
    # Only include feature files that match the specified tags
    args.append('features')

    print("Running Behave with args:", args)
    return behave_main(args)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Run cucumber tests with Behave')
    parser.add_argument('--tags', help='Tags to filter tests (e.g. "@api,~@db")')
    args = parser.parse_args()

    exit_code = run_cucumber_tests(args.tags)
    sys.exit(exit_code)
