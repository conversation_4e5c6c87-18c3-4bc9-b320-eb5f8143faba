#!/usr/bin/env python3
import time
from test_containers.brick_config import BrickConfig
from test_containers.infrastructure_manager import InfrastructureManager

def main():
    # Create a configuration for a Dapr brick.
    config = BrickConfig(
        brick_type="dapr",
        instance_name="dapr-service1",
        command_arguments=["daprd", "--app-id", "service1", "--app-port", "5001"],
        environment_variables={},
        exposed_port="3500/tcp",
        host_port="3500"
    )

    # Create the infrastructure manager with this single brick configuration.
    manager = InfrastructureManager([config])

    print("Starting all containers...")
    manager.start_all()

    print("Containers are running. Sleeping for 10 seconds...")
    time.sleep(10)

    print("Stopping all containers...")
    manager.stop_all()
    print("All containers stopped.")

if __name__ == "__main__":
    main()
