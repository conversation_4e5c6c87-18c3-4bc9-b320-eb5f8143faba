import time
import os
import logging
import sys
import subprocess
import pytest
from contextlib import contextmanager
from typing import Optional, List, Dict, Any, Generator

from test_containers.brick_config import BrickConfig, HealthCheckConfig
from test_containers.infrastructure_manager import InfrastructureManager
from test_containers.network import create_network, remove_network


# Configure logging to stdout
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)


def get_file_path(relative_path: str) -> str:
    """Get the absolute path for a file relative to this script.
    
    Args:
        relative_path: The relative path to resolve
        
    Returns:
        The absolute path
    """
    return os.path.abspath(os.path.join(os.path.dirname(__file__), relative_path))


@contextmanager
def ensure_network(network_name: str) -> Generator[str, None, None]:
    """Context manager to ensure a Docker network exists and is cleaned up.
    
    Args:
        network_name: The name of the network to create
        
    Yields:
        The name of the created network
        
    Raises:
        pytest.fail: If the network cannot be created
    """
    try:
        network = create_network(network_name)
        logger.info(f"Created network: {network}")
        yield network
    except Exception as e:
        pytest.fail(f"Failed to create network '{network_name}': {e}")
    finally:
        try:
            remove_network(network_name)
            logger.info(f"Removed network: {network_name}")
        except Exception as e:
            logger.warning(f"Failed to remove network '{network_name}': {e}")


def ensure_test_files_exist():
    """Ensure all required test files and directories exist."""
    # Setup paths
    init_sql = get_file_path("../../services/gin-go-demo/init.sql")
    components_path = get_file_path("../../components")
    load_test_script = get_file_path("../../tests/performance/scripts/load-test.js")
    reports_path = get_file_path("../../tests/performance/reports")
    
    logger.info(f"Using paths: init_sql={init_sql}, components={components_path}")
    
    # Ensure these directories exist
    for path in [init_sql, components_path, load_test_script, reports_path]:
        logger.info(f"Checking path: {path}")
        if not os.path.exists(path):
            parent_dir = os.path.dirname(path)
            if not os.path.exists(parent_dir):
                os.makedirs(parent_dir)
                logger.info(f"Created directory: {parent_dir}")
    
    # Create sample files if they don't exist
    if not os.path.exists(init_sql):
        with open(init_sql, 'w') as f:
            f.write("CREATE TABLE IF NOT EXISTS users (id SERIAL PRIMARY KEY, name VARCHAR(100));\n")
        logger.info(f"Created sample init.sql file")
    
    if not os.path.exists(components_path):
        os.makedirs(components_path)
        logger.info(f"Created components directory")
    
    if not os.path.exists(os.path.dirname(load_test_script)):
        os.makedirs(os.path.dirname(load_test_script))
    
    if not os.path.exists(load_test_script):
        with open(load_test_script, 'w') as f:
            f.write("import http from 'k6/http';\n")
            f.write("export default function() {\n")
            f.write("  http.get('http://dapr-gin:3500/v1.0/invoke/gin-go-demo/method');\n")
            f.write("}\n")
        logger.info(f"Created sample load-test.js file")
    
    if not os.path.exists(reports_path):
        os.makedirs(reports_path)
        logger.info(f"Created reports directory")
    
    return init_sql, components_path, load_test_script, reports_path


def setup_container_configs(network_name, init_sql, components_path, load_test_script, reports_path):
    """Set up all container configurations."""
    # Define brick configurations
    postgres = BrickConfig(
        brick_type="postgres",
        instance_name="postgres-db",
        environment_variables={
            "POSTGRES_USER": "postgres",
            "POSTGRES_PASSWORD": "postgres",
            "POSTGRES_DB": "demo_db",
        },
        exposed_port="5432/tcp",
        host_port="5433",
        volumes=[f"{init_sql}:/docker-entrypoint-initdb.d/init.sql"],
        networks=[network_name],
        network_aliases={network_name: ["db"]},
        health_check=HealthCheckConfig(
            test=["pg_isready", "-U", "postgres", "-d", "demo_db"],
            interval=5,
            retries=10,
            start_period=5,
            timeout=5,
        ),
    )
    
    gin_go = BrickConfig(
        brick_type="gin-go",
        instance_name="gin-go",
        environment_variables={
            "DB_HOST": "postgres-db",
            "DB_PORT": "5432",
            "DB_USER": "postgres",
            "DB_PASSWORD": "postgres",
            "DB_NAME": "demo_db",
            "JWT_SECRET": "supersecretkey",
            "DAPR_BASE_URL": "http://dapr-gin:3500/v1.0",
        },
        exposed_port="8081/tcp",
        host_port="8081",
        networks=[network_name],
    )
    
    fib_calc = BrickConfig(
        brick_type="fib-calc",
        instance_name="fib-calc",
        environment_variables={
            "DB_HOST": "postgres-db",
            "DB_PORT": "5432",
            "DB_USER": "postgres",
            "DB_PASSWORD": "postgres",
            "DB_NAME": "demo_db",
            "FIB_CALC_URL": "http://fib-calc-service:8082",
            "DAPR_BASE_URL": "http://dapr-fib:3501/v1.0/invoke",
        },
        exposed_port="8082/tcp",
        host_port="8082",
        networks=[network_name],
    )
    
    dapr_gin = BrickConfig(
        brick_type="dapr",
        instance_name="dapr-gin",
        command_arguments=[
            "/daprd",
            "--app-id", "gin-go-demo",
            "--app-channel-address", "gin-go",
            "--app-port", "8081",
            "--dapr-http-port", "3500",
            "--resources-path", "/app/.dapr/components",
        ],
        exposed_port="3500/tcp",
        host_port="3500",
        networks=[network_name],
        volumes=[f"{components_path}:/app/.dapr/components"],
    )
    
    dapr_fib = BrickConfig(
        brick_type="dapr",
        instance_name="dapr-fib",
        command_arguments=[
            "/daprd",
            "--app-id", "fib-calc",
            "--app-channel-address", "fib-calc",
            "--app-port", "8082",
            "--dapr-http-port", "3502",
            "--dapr-grpc-port", "50001",
            "--resources-path", "/app/.dapr/components",
        ],
        exposed_port="3502/tcp",
        host_port="3502",
        networks=[network_name],
        volumes=[f"{components_path}:/app/.dapr/components"],
    )
    
    k6_gin = BrickConfig(
        brick_type="k6-gin",
        instance_name="k6-gin",
        command_arguments=["run", "/src/load-test.js"],
        exposed_port="8080/tcp",
        environment_variables={
            "BASE_URL": "http://dapr-gin:3500/v1.0/invoke/gin-go-demo/method",
        },
        networks=[network_name],
        volumes=[
            f"{load_test_script}:/src/load-test.js",   
            f"{reports_path}:/src/reports", 
        ],
    )
    
    # Order matters for dependencies
    return [postgres, gin_go, fib_calc, dapr_gin, dapr_fib, k6_gin]


def verify_containers_running(container_names):
    """Verify that all containers are running."""
    for container_name in container_names:
        try:
            result = subprocess.run(
                ['docker', 'ps', '--filter', f'name={container_name}', '--format', '{{.Names}}'],
                capture_output=True, 
                text=True, 
                check=True
            )
            found_name = result.stdout.strip()
            assert found_name == container_name, f"Expected {container_name}, got {found_name}"
            logger.info(f"Container {container_name} is running")
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to verify container {container_name}: {e.stderr}")
            pytest.fail(f"Container verification failed for {container_name}: {e.stderr}")
        except AssertionError as e:
            pytest.fail(str(e))


def test_docker_compose_integration():
    """Integration test using Docker Compose-like functionality.
    
    This test sets up all containers and verifies they are running correctly.
    """
    network_name = "platform-tests-bridge"
    
    # Setup paths and ensure files exist
    init_sql, components_path, load_test_script, reports_path = ensure_test_files_exist()
    
    with ensure_network(network_name):
        # Set up all container configurations
        bricks = setup_container_configs(network_name, init_sql, components_path, load_test_script, reports_path)
        
        # Create infrastructure manager with all containers
        infra = InfrastructureManager(bricks)
        
        # Use the context manager to ensure cleanup
        with infra.managed_infrastructure():
            logger.info("All containers started. Waiting for 15 seconds for services to initialize...")
            time.sleep(15)
            
            # Verify that all containers are running
            container_names = [brick.config.instance_name for brick in bricks]
            verify_containers_running(container_names)
            
            logger.info("All containers verified as running. Test successful.")
            
            # Wait a bit longer to allow containers to do some work
            logger.info("Waiting for 15 more seconds to allow services to run...")
            time.sleep(15)
            
            logger.info("Test complete.")


if __name__ == "__main__":
    # Run the test directly when script is executed
    try:
        test_docker_compose_integration()
        logger.info("Integration test completed successfully")
    except Exception as e:
        logger.error(f"Integration test failed: {e}")
        sys.exit(1)
