@db
Feature: Database validation and data integrity checks for PostgreSQL

  Background:
    Given a PostgreSQL database is running
    And I have a valid connection to the database

   Scenario: Verify DB connection with dynamic params
    Given I set DB host to "localhost"
    And I set DB port to "5433"
    And I set <PERSON> user to "postgres"
    And I set DB password to "postgres"
    And I set DB name to "demo_db"
    And a PostgreSQL database is running
    When I connect to the database
    Then the connection should be successful
    
    Scenario: Create users and orders tables
    When I execute the following SQL:
      """
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        email TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT now(),
        is_seed BOOLEAN DEFAULT FALSE
      );

      CREATE TABLE IF NOT EXISTS orders (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        order_date TIMESTAMP DEFAULT now(),
        CONSTRAINT orders_user_id_fkey FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      );

      CREATE UNIQUE INDEX IF NOT EXISTS users_email_idx ON users(email);
      """
    Then the following tables should exist:
      | table_name |
      | users      |
      | orders     |

  Scenario: Insert a record if it does not exist
    When I insert a record into "users" where "email" is "<EMAIL>"
    Then the record in "users" where "email" is "<EMAIL>" should exist

  Scenario: Insert a linked record
    When I insert a child record into "orders" with foreign key "user_id" from "users" where "email" is "<EMAIL>" 
    Then the child record in "orders" linked to "users" by "user_id" where "email" is "<EMAIL>" should exist


  Scenario: Check table, column, and index existence
    Given the following tables should exist:
      | table_name     |
      | users          |
      | orders         |
    And the following columns should exist in "users":
      | column_name | data_type | is_nullable |
      | id          | integer   | NO          |
      | email       | text      | NO          |
    And the following indexes should exist on "users":
      | index_name           |
      | users_email_idx      |

  Scenario: Validate foreign key constraints
    Given the foreign key "orders_user_id_fkey" should exist on "orders"
    When I inspect the constraint
Then it should reference the "users" table on column "id" with constraint "orders_user_id_fkey"

  Scenario: Verify uniqueness and not null constraints
    Given the "users" table has a unique constraint on "email"
    And the "users" table's "email" column is NOT NULL

  Scenario: Verify default values and data types
    Given the "orders.order_date" column has a default value of "now()"
    And the "users.id" column is of type "serial"

  Scenario: Validate cascade rules on foreign keys
    Given the foreign key "orders_user_id_fkey" has ON DELETE CASCADE behavior
