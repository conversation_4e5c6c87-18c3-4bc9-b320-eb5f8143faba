import psycopg2
from behave import given, when, then
from psycopg2 import sql

# Store connection globally in context
@given('a PostgreSQL database is running')
def step_impl(context):
    # Ideally, you'd check if PostgreSQL service is up — skipping actual check for brevity
    pass


@given('I have a valid connection to the database')
def step_impl(context):
    context.connection = psycopg2.connect(
        dbname="demo_db",
        user="postgres",
        password="postgres",
        host="localhost",
        port="5433"
    )
    context.cursor = context.connection.cursor()


@given('I set DB {key} to "{value}"')
def step_impl(context, key, value):
    if not hasattr(context, "db_config"):
        context.db_config = {}
    context.db_config[key.lower()] = value


@when('I connect to the database')
def step_impl(context):
    cfg = context.db_config
    context.connection = psycopg2.connect(
        dbname=cfg.get("name", "demo_db"),
        user=cfg.get("user", "postgres"),
        password=cfg.get("password", "postgres"),
        host=cfg.get("host", "localhost"),
        port=cfg.get("port", "5432")
    )
    context.cursor = context.connection.cursor()


@then('the connection should be successful')
def step_impl(context):
    assert context.connection is not None


@when('I execute the following SQL')
def step_impl(context):
    context.cursor.execute(context.text)
    context.connection.commit()


@then('the following tables should exist')
@given('the following tables should exist')
def step_impl(context):
    context.cursor.execute("""
        SELECT table_name FROM information_schema.tables
        WHERE table_schema='public';
    """)
    existing_tables = {row[0] for row in context.cursor.fetchall()}
    for row in context.table:
        assert row['table_name'] in existing_tables


@given('the following columns should exist in "{table_name}"')
def step_impl(context, table_name):
    context.cursor.execute(sql.SQL("""
        SELECT column_name FROM information_schema.columns
        WHERE table_name = %s;
    """), [table_name])
    existing_columns = {row[0] for row in context.cursor.fetchall()}
    for row in context.table:
        assert row['column_name'] in existing_columns


@given('the following indexes should exist on "{table_name}"')
def step_impl(context, table_name):
    context.cursor.execute(sql.SQL("""
        SELECT indexname FROM pg_indexes
        WHERE tablename = %s;
    """), [table_name])
    existing_indexes = {row[0] for row in context.cursor.fetchall()}
    for row in context.table:
        assert row['index_name'] in existing_indexes


@given('the foreign key "{fk_name}" should exist on "{table_name}"')
def step_impl(context, fk_name, table_name):
    context.cursor.execute("""
        SELECT constraint_name FROM information_schema.table_constraints
        WHERE table_name = %s AND constraint_type = 'FOREIGN KEY';
    """, [table_name])
    fks = {row[0] for row in context.cursor.fetchall()}
    assert fk_name in fks


@when('I inspect the constraint')
def step_impl(context):
    # Placeholder for potential future context setting
    pass


@then('it should reference the "{ref_table}" table on column "{ref_column}" with constraint "{fk_name}"')
def step_impl(context, ref_table, ref_column, fk_name):
    context.cursor.execute("""
        SELECT
            kcu.column_name, ccu.table_name AS foreign_table_name, ccu.column_name AS foreign_column_name
        FROM
            information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
            ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
            ON ccu.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.constraint_name = %s;
    """, [fk_name])
    result = context.cursor.fetchone()
    assert result is not None
    assert result[1] == ref_table
    assert result[2] == ref_column


@when('I insert a record into "{table}" where "{column}" is "{value}"')
def step_impl(context, table, column, value):
    # First check if the record already exists
    check_query = sql.SQL("SELECT 1 FROM {} WHERE {} = %s").format(
        sql.Identifier(table),
        sql.Identifier(column)
    )
    context.cursor.execute(check_query, [value])
    if context.cursor.fetchone() is None:
        query = sql.SQL("INSERT INTO {} ({}) VALUES (%s)").format(
            sql.Identifier(table),
            sql.Identifier(column)
        )
        context.cursor.execute(query, [value])
        context.connection.commit()


@then('the record in "{table}" where "{column}" is "{value}" should exist')
def step_impl(context, table, column, value):
    query = sql.SQL("SELECT 1 FROM {} WHERE {} = %s").format(
        sql.Identifier(table),
        sql.Identifier(column)
    )
    context.cursor.execute(query, [value])
    assert context.cursor.fetchone() is not None


@when('I insert a child record into "{child_table}" with foreign key "{fk_column}" from "{parent_table}" where "{parent_column}" is "{parent_value}"')
def step_impl(context, child_table, fk_column, parent_table, parent_column, parent_value):
    # Get parent ID
    context.cursor.execute(sql.SQL("SELECT id FROM {} WHERE {} = %s").format(
        sql.Identifier(parent_table),
        sql.Identifier(parent_column)
    ), [parent_value])
    parent_id = context.cursor.fetchone()
    assert parent_id is not None

    # Insert into child table
    context.cursor.execute(sql.SQL("INSERT INTO {} ({}) VALUES (%s)").format(
        sql.Identifier(child_table),
        sql.Identifier(fk_column)
    ), [parent_id[0]])
    context.connection.commit()




@then('the child record in "{child_table}" linked to "{parent_table}" by "{fk_column}" where "{parent_column}" is "{parent_value}" should exist')
def step_impl(context, child_table, parent_table, fk_column, parent_column, parent_value):
    context.cursor.execute(sql.SQL("SELECT id FROM {} WHERE {} = %s").format(
        sql.Identifier(parent_table),
        sql.Identifier(parent_column)
    ), [parent_value])
    parent_id = context.cursor.fetchone()
    assert parent_id is not None

    context.cursor.execute(sql.SQL("SELECT 1 FROM {} WHERE {} = %s").format(
        sql.Identifier(child_table),
        sql.Identifier(fk_column)
    ), [parent_id[0]])
    assert context.cursor.fetchone() is not None


@given('the "{table}" table\'s "{column}" column is NOT NULL')
def step_impl(context, table, column):
    context.cursor.execute("""
        SELECT is_nullable FROM information_schema.columns
        WHERE table_name = %s AND column_name = %s
    """, [table, column])
    result = context.cursor.fetchone()
    assert result is not None and result[0] == 'NO'



@given('the "{table}" table has a unique constraint on "{column}"')
def step_impl(context, table, column):
    # Check both for unique constraints and unique indexes
    try:
        context.cursor.execute("""
            SELECT COUNT(1) FROM (
                SELECT tc.constraint_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.constraint_column_usage ccu
                    ON tc.constraint_name = ccu.constraint_name
                WHERE tc.table_name = %s 
                AND (tc.constraint_type = 'UNIQUE' OR tc.constraint_type = 'PRIMARY KEY')
                AND ccu.column_name = %s
                UNION
                SELECT indexname
                FROM pg_indexes
                WHERE tablename = %s AND indexdef LIKE '%%UNIQUE%%' AND indexdef LIKE '%%' || %s || '%%'
            ) AS unique_constraints
        """, [table, column, table, column])
        
        count = context.cursor.fetchone()[0]
        assert count > 0, f"No unique constraint found for {column} in {table}"
    except Exception as e:
        # Alternate simpler check that just looks at the index name
        context.cursor.execute("""
            SELECT indexname FROM pg_indexes
            WHERE tablename = %s AND indexdef LIKE '%%UNIQUE%%'
        """, [table])
        indexes = context.cursor.fetchall()
        
        # Check if any index name contains the column name or if it's a users_email_idx
        found = False
        for idx in indexes:
            if column in idx[0] or (table == 'users' and column == 'email' and idx[0] == 'users_email_idx'):
                found = True
                break
                
        assert found, f"No unique constraint or index found for {column} in {table}"


@given('the "{table_column}" column has a default value of "{default}"')
def step_impl(context, table_column, default):
    table, column = table_column.split('.')
    context.cursor.execute("""
        SELECT column_default FROM information_schema.columns
        WHERE table_name = %s AND column_name = %s
    """, [table, column])
    result = context.cursor.fetchone()
    assert result is not None and default in (result[0] or '')



@given('the "{table_column}" column is of type "{data_type}"')
def step_impl(context, table_column, data_type):
    table, column = table_column.split('.')
    
    # Special handling for serial type
    if data_type.lower() == 'serial':
        context.cursor.execute("""
            SELECT data_type, column_default
            FROM information_schema.columns
            WHERE table_name = %s AND column_name = %s
        """, [table, column])
        result = context.cursor.fetchone()
        assert result is not None
        # A serial column is an integer with a nextval sequence default
        assert result[0] == 'integer' and 'nextval' in (result[1] or '')
    else:
        context.cursor.execute("""
            SELECT data_type, udt_name
            FROM information_schema.columns
            WHERE table_name = %s AND column_name = %s
        """, [table, column])
        result = context.cursor.fetchone()
        assert result is not None
        assert data_type.lower() in (result[1] or result[0] or '').lower()



@given('the foreign key "{fk_name}" has ON DELETE CASCADE behavior')
def step_impl(context, fk_name):
    context.cursor.execute("""
        SELECT delete_rule FROM information_schema.referential_constraints
        WHERE constraint_name = %s
    """, [fk_name])
    result = context.cursor.fetchone()
    assert result is not None and result[0] == "CASCADE"



