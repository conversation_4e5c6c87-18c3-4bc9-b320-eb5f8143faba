package main

import (
	"context"
	"fmt"
	"log"
	"time"

	tc "github.com/Matrics-io/platform-tests/testcontainers"
)

func main() {
	ctx := context.Background()

	// Define configurations for all containers
	configs := []tc.BrickConfig{
		{
			BrickType:    "postgres",
			InstanceName: "postgres-db",
			EnvironmentVariables: map[string]string{
				"POSTGRES_USER":     "postgres",
				"POSTGRES_PASSWORD": "postgres",
				"POSTGRES_DB":       "demo_db",
			},
			ExposedPort: "5432/tcp",
			HostPort:    "5433",
		},
		{
			BrickType:    "gin-go",
			InstanceName: "gin-go",
			EnvironmentVariables: map[string]string{
				"DB_HOST":       "postgres-db",
				"DB_PORT":       "5432",
				"DB_USER":       "postgres",
				"DB_PASSWORD":   "postgres",
				"DB_NAME":       "demo_db",
				"JWT_SECRET":    "supersecretkey",
				"DAPR_BASE_URL": "http://dapr-gin:3500/v1.0",
			},
			ExposedPort: "8081/tcp",
			HostPort:    "8081",
		},
		{
			BrickType:    "fib-calc",
			InstanceName: "fib-calc",
			EnvironmentVariables: map[string]string{
				"DB_HOST":       "postgres-db",
				"DB_PORT":       "5432",
				"DB_USER":       "postgres",
				"DB_PASSWORD":   "postgres",
				"DB_NAME":       "demo_db",
				"FIB_CALC_URL":  "http://fib-calc-service:8082",
				"DAPR_BASE_URL": "http://dapr-fib:3501/v1.0/invoke",
			},
			ExposedPort: "8082/tcp",
			HostPort:    "8082",
		},
		{
			BrickType:    "dapr",
			InstanceName: "dapr-gin",
			CommandArguments: []string{
				"--app-id", "gin-go-demo",
				"--app-channel-address", "gin-go",
				"--app-port", "8081",
				"--dapr-http-port", "3500",
				"--resources-path", "/app/.dapr/components",
			},
			ExposedPort: "3500/tcp",
			HostPort:    "3500",
		},
		{
			BrickType:    "dapr",
			InstanceName: "dapr-fib",
			CommandArguments: []string{
				"--app-id", "fib-calc",
				"--app-channel-address", "fib-calc",
				"--app-port", "8082",
				"--dapr-http-port", "3501",
				"--dapr-grpc-port", "50001",
				"--resources-path", "/app/.dapr/components",
			},
			ExposedPort: "3501/tcp",
			HostPort:    "3501",
		},
	}

	// Create an Infrastructure Manager
	// infraManager := tc.NewInfrastructureManager(configs)

	// Start all containers sequentially
	fmt.Println("Starting containers...")

	// Start PostgreSQL first and wait for it to be ready
	postgresConfig := configs[0]
	postgresBrick := tc.NewContainerBrick(postgresConfig)
	if err := postgresBrick.Start(ctx); err != nil {
		log.Fatalf("Failed to start Postgres container: %v", err)
	}
	fmt.Println("PostgreSQL container started. Waiting for it to be ready...")
	time.Sleep(15 * time.Second)

	// Start gin-go app
	ginConfig := configs[1]
	ginBrick := tc.NewContainerBrick(ginConfig)
	if err := ginBrick.Start(ctx); err != nil {
		log.Fatalf("Failed to start gin-go container: %v", err)
	}
	fmt.Println("Gin-go container started. Waiting...")
	time.Sleep(5 * time.Second)

	// Start fib-calc app
	fibConfig := configs[2]
	fibBrick := tc.NewContainerBrick(fibConfig)
	if err := fibBrick.Start(ctx); err != nil {
		log.Fatalf("Failed to start fib-calc container: %v", err)
	}
	fmt.Println("Fib-calc container started. Waiting...")
	time.Sleep(5 * time.Second)

	// Start dapr for gin-go
	daprGinConfig := configs[3]
	daprGinBrick := tc.NewContainerBrick(daprGinConfig)
	if err := daprGinBrick.Start(ctx); err != nil {
		log.Fatalf("Failed to start dapr-gin container: %v", err)
	}
	fmt.Println("dapr-gin container started. Waiting...")
	time.Sleep(5 * time.Second)

	// Start dapr for fib-calc
	daprFibConfig := configs[4]
	daprFibBrick := tc.NewContainerBrick(daprFibConfig)
	if err := daprFibBrick.Start(ctx); err != nil {
		log.Fatalf("Failed to start dapr-fib container: %v", err)
	}
	fmt.Println("dapr-fib container started. Waiting...")
	time.Sleep(5 * time.Second)

	fmt.Println("All containers started successfully!")
	fmt.Println("Press Ctrl+C to stop containers...")

	// Keep the program running until interrupted
	select {}
}
