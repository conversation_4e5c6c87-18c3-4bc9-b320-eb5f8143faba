package testcontainers

import (
	"context"

	"github.com/testcontainers/testcontainers-go"
)

// ContainerBrick defines the lifecycle methods for a container brick.
// It represents a managed Docker container that can be started and stopped.
type ContainerBrick interface {
	// Start initializes and starts the container with the given context.
	Start(ctx context.Context) error

	// Stop terminates the container with the given context.
	Stop(ctx context.Context) error

	// GetContainer returns the underlying testcontainers.Container instance.
	// This allows direct access to container functionality when needed.
	GetContainer() testcontainers.Container
}
