package testcontainers

// ContainerBrickFactory provides methods for creating container bricks.
type ContainerBrickFactory struct{}

// NewContainerBrickFactory creates a new ContainerBrickFactory.
func NewContainerBrickFactory() *ContainerBrickFactory {
	return &ContainerBrickFactory{}
}

// CreateBrick creates a new ContainerBrick based on the given configuration.
func (f *ContainerBrickFactory) CreateBrick(config BrickConfig) ContainerBrick {
	return &GenericContainerBrick{
		config: config,
	}
}

// NewContainerBrick creates a new ContainerBrick based on the given configuration.
// This is a package-level function for backward compatibility.
func NewContainerBrick(config BrickConfig) ContainerBrick {
	factory := NewContainerBrickFactory()
	return factory.CreateBrick(config)
}
