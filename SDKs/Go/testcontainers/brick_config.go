package testcontainers

import (
	"time"

	"github.com/docker/docker/api/types/container"
	"github.com/testcontainers/testcontainers-go/wait"
)

// VolumeMapping maps a host path to a container path.
type VolumeMapping struct {
	// HostPath is the path on the host machine
	HostPath string
	// ContainerPath is the path inside the container
	ContainerPath string
}

// HealthCheckConfig holds health-check configuration.
type HealthCheckConfig struct {
	// Test is the command to run to check health (e.g., ["CMD", "curl", "-f", "http://localhost"])
	Test []string
	// Interval is the time between health checks
	Interval time.Duration
	// Retries is the number of consecutive failures needed to report unhealthy
	Retries int
	// StartPeriod is the initialization time needed before starting health checks
	StartPeriod time.Duration
	// Timeout is the maximum time to wait for one health check to complete
	Timeout time.Duration
}

// BrickConfig holds configuration for a container brick.
type BrickConfig struct {
	// BrickType identifies the type of container (e.g., "postgres", "redis")
	BrickType string
	// InstanceName is the name assigned to the container
	InstanceName string
	// CommandArguments are the arguments passed to the container entrypoint
	CommandArguments []string
	// EnvironmentVariables are the environment variables set in the container
	EnvironmentVariables map[string]string
	// ExposedPort is the container port to expose (e.g., "5432/tcp")
	ExposedPort string
	// HostPort is the port on the host to bind to the exposed container port (e.g., "5433")
	HostPort string
	// Volumes are the volume mappings from host to container
	Volumes []VolumeMapping
	// HealthCheck holds the container health check configuration
	HealthCheck *HealthCheckConfig
	// Networks are the Docker networks to connect the container to
	Networks []string
	// NetworkAliases are the aliases for the container in each network
	NetworkAliases map[string][]string
	// HostConfigModifier is a function to modify the container's host configuration
	HostConfigModifier func(*container.HostConfig)
	// CustomWaitStrategy defines a strategy for waiting for the container to be ready
	// If set, this strategy will be used instead of the default behavior
	CustomWaitStrategy wait.Strategy
}
