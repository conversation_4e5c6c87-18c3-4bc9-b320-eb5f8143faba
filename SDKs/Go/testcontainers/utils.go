package testcontainers

import (
	"context"
	"fmt"
	"net/url"
	"time"

	"github.com/docker/go-connections/nat"
	"github.com/testcontainers/testcontainers-go/wait"
)

// DefaultStartupTimeout is the default timeout for container startup operations
const DefaultStartupTimeout = 60 * time.Second

// CreatePostgresContainer creates a PostgreSQL container with the given configuration
func CreatePostgresContainer(ctx context.Context, username, password, database string, port string) (ContainerBrick, error) {
	config := BrickConfig{
		BrickType:    "postgres",
		InstanceName: fmt.Sprintf("postgres-%s", database),
		EnvironmentVariables: map[string]string{
			"POSTGRES_USER":     username,
			"POSTGRES_PASSWORD": password,
			"POSTGRES_DB":       database,
		},
		ExposedPort: "5432/tcp",
		HostPort:    port,
		Networks:    []string{"platform-tests"},
		NetworkAliases: map[string][]string{
			"platform-tests": {fmt.Sprintf("postgres-%s", database)},
		},
		CustomWaitStrategy: wait.ForSQL("5432/tcp", "postgres", func(host string, port nat.Port) string {
			return fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable",
				username, password, host, port.Port(), database)
		}).WithStartupTimeout(DefaultStartupTimeout),
	}

	brick := NewContainerBrick(config)
	if err := brick.Start(ctx); err != nil {
		return nil, fmt.Errorf("failed to start PostgreSQL container: %w", err)
	}

	return brick, nil
}

// CreateMongoDBContainer creates a MongoDB container with the given configuration
func CreateMongoDBContainer(ctx context.Context, username, password string, port string) (ContainerBrick, error) {
	config := BrickConfig{
		BrickType:    "mongodb",
		InstanceName: "mongodb",
		EnvironmentVariables: map[string]string{
			"MONGO_INITDB_ROOT_USERNAME": username,
			"MONGO_INITDB_ROOT_PASSWORD": password,
		},
		ExposedPort: "27017/tcp",
		HostPort:    port,
		Networks:    []string{"platform-tests"},
		NetworkAliases: map[string][]string{
			"platform-tests": {"mongodb"},
		},
		CustomWaitStrategy: wait.ForListeningPort("27017/tcp").WithStartupTimeout(DefaultStartupTimeout),
	}

	brick := NewContainerBrick(config)
	if err := brick.Start(ctx); err != nil {
		return nil, fmt.Errorf("failed to start MongoDB container: %w", err)
	}

	return brick, nil
}

// CreateRedisContainer creates a Redis container with the given configuration
func CreateRedisContainer(ctx context.Context, port string) (ContainerBrick, error) {
	config := BrickConfig{
		BrickType:    "dapr-redis",
		InstanceName: "redis",
		ExposedPort:  "6379/tcp",
		HostPort:     port,
		Networks:     []string{"platform-tests"},
		NetworkAliases: map[string][]string{
			"platform-tests": {"redis"},
		},
		CustomWaitStrategy: wait.ForListeningPort("6379/tcp").WithStartupTimeout(DefaultStartupTimeout),
	}

	brick := NewContainerBrick(config)
	if err := brick.Start(ctx); err != nil {
		return nil, fmt.Errorf("failed to start Redis container: %w", err)
	}

	return brick, nil
}

// GetContainerConnectionString returns a connection string for the given container
func GetContainerConnectionString(ctx context.Context, brick ContainerBrick, scheme, containerPort string) (string, error) {
	container := brick.GetContainer()
	if container == nil {
		return "", fmt.Errorf("container is not started")
	}

	host, err := container.Host(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get container host: %w", err)
	}

	mappedPort, err := container.MappedPort(ctx, nat.Port(containerPort))
	if err != nil {
		return "", fmt.Errorf("failed to get mapped port for %s: %w", containerPort, err)
	}

	u := url.URL{
		Scheme: scheme,
		Host:   fmt.Sprintf("%s:%s", host, mappedPort.Port()),
	}

	return u.String(), nil
}

// BuildWaitForHTTPStrategy creates a strategy that waits for an HTTP endpoint to be ready
func BuildWaitForHTTPStrategy(port string, path string, statusCode int) wait.Strategy {
	return wait.ForHTTP(path).
		WithPort(nat.Port(port)).
		WithStatusCodeMatcher(func(status int) bool {
			return status == statusCode
		}).
		WithStartupTimeout(DefaultStartupTimeout)
}

// BuildWaitForLogStrategy creates a strategy that waits for a log message
func BuildWaitForLogStrategy(logPattern string) wait.Strategy {
	return wait.ForLog(logPattern).WithStartupTimeout(DefaultStartupTimeout)
}
