package testcontainers

import (
	"context"
	"fmt"

	"github.com/testcontainers/testcontainers-go"
)

// CreateNetwork creates a Docker network with the given name.
// Returns the network name if successful.
func CreateNetwork(ctx context.Context, networkName string) (string, error) {
	provider, err := testcontainers.NewDockerProvider()
	if err != nil {
		return "", fmt.Errorf("failed to create Docker provider: %w", err)
	}

	networkRequest := testcontainers.NetworkRequest{
		Name:           networkName,
		CheckDuplicate: true,
	}

	// We're ignoring the network ID returned by CreateNetwork because we just need the name
	_, err = provider.CreateNetwork(ctx, networkRequest)
	if err != nil {
		return "", fmt.Errorf("failed to create network %s: %w", networkName, err)
	}

	return networkName, nil
}
