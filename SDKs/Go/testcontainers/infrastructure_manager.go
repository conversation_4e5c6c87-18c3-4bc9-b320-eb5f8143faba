package testcontainers

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// defaultTimeout is the default timeout for context operations.
const defaultTimeout = 30 * time.Second

// InfrastructureManager manages a collection of container bricks.
type InfrastructureManager struct {
	bricks []ContainerBrick
	mutex  sync.RWMutex
}

// NewInfrastructureManager creates an InfrastructureManager from a slice of BrickConfig.
func NewInfrastructureManager(configs []BrickConfig) *InfrastructureManager {
	factory := NewContainerBrickFactory()
	bricks := make([]ContainerBrick, 0, len(configs))

	for _, config := range configs {
		brick := factory.CreateBrick(config)
		bricks = append(bricks, brick)
	}

	return &InfrastructureManager{
		bricks: bricks,
		mutex:  sync.RWMutex{},
	}
}

// AddBrick adds a new brick to the infrastructure manager.
func (im *InfrastructureManager) AddBrick(config BrickConfig) {
	im.mutex.Lock()
	defer im.mutex.Unlock()

	factory := NewContainerBrickFactory()
	brick := factory.CreateBrick(config)
	im.bricks = append(im.bricks, brick)
}

// StartAll starts all container bricks.
// If any brick fails to start, it attempts to stop all started bricks before returning the error.
func (im *InfrastructureManager) StartAll(ctx context.Context) error {
	im.mutex.Lock()
	defer im.mutex.Unlock()

	var startedBricks []ContainerBrick

	for i, brick := range im.bricks {
		if err := brick.Start(ctx); err != nil {
			// Attempt to stop all previously started bricks
			stopCtx, cancel := context.WithTimeout(context.Background(), defaultTimeout)
			defer cancel()

			im.stopBricks(stopCtx, startedBricks)

			return fmt.Errorf("failed to start brick %d: %w", i, err)
		}

		startedBricks = append(startedBricks, brick)
	}

	return nil
}

// StopAll stops all container bricks.
// It attempts to stop all bricks even if some fail, collecting and returning all errors.
func (im *InfrastructureManager) StopAll(ctx context.Context) error {
	im.mutex.Lock()
	defer im.mutex.Unlock()

	return im.stopBricks(ctx, im.bricks)
}

// stopBricks is a helper method to stop the given bricks.
func (im *InfrastructureManager) stopBricks(ctx context.Context, bricks []ContainerBrick) error {
	var errs []error

	// Try to stop all bricks, collecting errors
	for i, brick := range bricks {
		if err := brick.Stop(ctx); err != nil {
			errs = append(errs, fmt.Errorf("failed to stop brick %d: %w", i, err))
		}
	}

	// If there were any errors, combine them into a single error
	if len(errs) > 0 {
		var errMsg string
		for i, err := range errs {
			if i > 0 {
				errMsg += "; "
			}
			errMsg += err.Error()
		}
		return fmt.Errorf("errors stopping bricks: %s", errMsg)
	}

	return nil
}

// GetBricks returns the managed bricks.
func (im *InfrastructureManager) GetBricks() []ContainerBrick {
	im.mutex.RLock()
	defer im.mutex.RUnlock()

	// Return a copy to prevent race conditions
	result := make([]ContainerBrick, len(im.bricks))
	copy(result, im.bricks)
	return result
}
