package testcontainers

import (
	"context"
	"fmt"
	"time"

	"github.com/docker/docker/api/types/container"
	"github.com/docker/go-connections/nat"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/wait"
)

// ContainerTypeRegistry maps container types to their Docker images
var ContainerTypeRegistry = map[string]string{
	// Dapr components
	"dapr":           "daprio/daprd:1.14.4",
	"dapr-zipkin":    "openzipkin/zipkin:latest",
	"dapr-redis":     "redis:latest",
	"dapr-placement": "daprio/dapr-placement:latest",
	"dapr-sentry":    "daprio/dapr-sentry:latest",
	"dapr-dashboard": "daprio/dapr-dashboard:latest",

	// Databases
	"postgres": "postgres:15-alpine",
	"mongodb":  "mongo:latest",

	// Messaging
	"mqtt": "eclipse-mosquitto:latest",

	// Application containers
	"gin-go":   "platform-tests-gin-go:latest",
	"fib-calc": "platform-tests-fib-calc:latest",
	"k6-gin":   "grafana/k6:latest",
}

// RegisterContainerType adds or updates a container type in the registry
func RegisterContainerType(containerType, image string) {
	ContainerTypeRegistry[containerType] = image
}

// GetImageForContainerType returns the Docker image for a given container type
func GetImageForContainerType(containerType string) (string, error) {
	image, exists := ContainerTypeRegistry[containerType]
	if !exists {
		return "", fmt.Errorf("unknown container type: %s", containerType)
	}
	return image, nil
}

// GenericContainerBrick implements the ContainerBrick interface.
type GenericContainerBrick struct {
	config    BrickConfig
	container testcontainers.Container
}

// Start initializes and starts the container.
func (g *GenericContainerBrick) Start(ctx context.Context) error {
	image, err := GetImageForContainerType(g.config.BrickType)
	if err != nil {
		return fmt.Errorf("failed to get image for brick type: %w", err)
	}

	// Prepare the waiting strategy
	waitStrategy := g.determineWaitStrategy()

	// Create the container request
	req := g.createContainerRequest(image, waitStrategy)

	// Start the container
	container, err := testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: req,
		Started:          true,
	})
	if err != nil {
		return fmt.Errorf("failed to start container: %w", err)
	}

	g.container = container
	return nil
}

// determineWaitStrategy selects an appropriate wait strategy based on the configuration.
func (g *GenericContainerBrick) determineWaitStrategy() wait.Strategy {
	// If a custom strategy is provided, use it
	if g.config.CustomWaitStrategy != nil {
		return g.config.CustomWaitStrategy
	}

	// If the exposed port is defined, wait for it to be ready
	if g.config.ExposedPort != "" {
		return wait.ForListeningPort(nat.Port(g.config.ExposedPort)).WithStartupTimeout(60 * time.Second)
	}

	// Default: wait for container to be healthy
	return wait.ForHealthCheck().WithStartupTimeout(60 * time.Second)
}

// createContainerRequest builds the container request based on the configuration.
func (g *GenericContainerBrick) createContainerRequest(image string, waitStrategy wait.Strategy) testcontainers.ContainerRequest {
	return testcontainers.ContainerRequest{
		Image:          image,
		Name:           g.config.InstanceName,
		ExposedPorts:   []string{g.config.ExposedPort},
		Cmd:            g.config.CommandArguments,
		Env:            g.config.EnvironmentVariables,
		Labels:         map[string]string{"com.docker.compose.project": "platform-tests"},
		Networks:       g.config.Networks,
		NetworkAliases: g.config.NetworkAliases,
		WaitingFor:     waitStrategy,
		HostConfigModifier: func(hostConfig *container.HostConfig) {
			// Apply default host port mapping if a HostPort is specified
			if g.config.HostPort != "" {
				hostConfig.PortBindings = nat.PortMap{
					nat.Port(g.config.ExposedPort): []nat.PortBinding{
						{HostIP: "0.0.0.0", HostPort: g.config.HostPort},
					},
				}
			}

			// Configure volumes if specified
			if len(g.config.Volumes) > 0 {
				binds := make([]string, 0, len(g.config.Volumes))
				for _, vol := range g.config.Volumes {
					binds = append(binds, fmt.Sprintf("%s:%s", vol.HostPath, vol.ContainerPath))
				}
				hostConfig.Binds = binds
			}

			// Apply custom modifications, if provided
			if g.config.HostConfigModifier != nil {
				g.config.HostConfigModifier(hostConfig)
			}
		},
	}
}

// GetContainer returns the underlying testcontainers.Container instance.
func (g *GenericContainerBrick) GetContainer() testcontainers.Container {
	return g.container
}

// Stop terminates the container.
func (g *GenericContainerBrick) Stop(ctx context.Context) error {
	if g.container != nil {
		return g.container.Terminate(ctx)
	}
	return nil
}

// GetMappedPort returns the host port mapped to the given container port.
func (g *GenericContainerBrick) GetMappedPort(ctx context.Context, containerPort string) (string, error) {
	if g.container == nil {
		return "", fmt.Errorf("container is not started")
	}

	mappedPort, err := g.container.MappedPort(ctx, nat.Port(containerPort))
	if err != nil {
		return "", fmt.Errorf("failed to get mapped port for %s: %w", containerPort, err)
	}

	return mappedPort.Port(), nil
}

// GetHost returns the host where the container is running.
func (g *GenericContainerBrick) GetHost(ctx context.Context) (string, error) {
	if g.container == nil {
		return "", fmt.Errorf("container is not started")
	}

	host, err := g.container.Host(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to get container host: %w", err)
	}

	return host, nil
} 