# Go Testcontainers Package

This package provides a clean, idiomatic Go wrapper around the [Testcontainers-Go](https://github.com/testcontainers/testcontainers-go) library, making it easier to set up and manage Docker containers for integration testing.

## Features

- Simplified container creation and lifecycle management
- Built-in support for common databases and services
- Structured configuration with sensible defaults
- Thread-safe infrastructure management
- Helper utilities for networking and container access

## Installation

```bash
go get github.com/testcontainers/testcontainers-go
```

## Basic Usage

### Creating a Simple Container

```go
package main

import (
	"context"
	"fmt"
	"log"

	"github.com/Matrics-io/platform-tests/SDKs/Go/testcontainers"
)

func main() {
	ctx := context.Background()

	// Create a network
	networkName, err := testcontainers.CreateNetwork(ctx, "test-network")
	if err != nil {
		log.Fatalf("Failed to create network: %v", err)
	}

	// Configure a PostgreSQL container
	config := testcontainers.BrickConfig{
		BrickType:    "postgres",
		InstanceName: "postgres-test",
		EnvironmentVariables: map[string]string{
			"POSTGRES_USER":     "test",
			"POSTGRES_PASSWORD": "test",
			"POSTGRES_DB":       "testdb",
		},
		ExposedPort: "5432/tcp",
		HostPort:    "5432",
		Networks:    []string{networkName},
	}

	// Create and start the container
	brick := testcontainers.NewContainerBrick(config)
	if err := brick.Start(ctx); err != nil {
		log.Fatalf("Failed to start container: %v", err)
	}
	defer brick.Stop(ctx)

	// Use the container...
	fmt.Println("Container started successfully!")
}
```

### Using Utility Functions

```go
package main

import (
	"context"
	"fmt"
	"log"

	"github.com/Matrics-io/platform-tests/SDKs/Go/testcontainers"
)

func main() {
	ctx := context.Background()

	// Create a network
	networkName, err := testcontainers.CreateNetwork(ctx, "test-network")
	if err != nil {
		log.Fatalf("Failed to create network: %v", err)
	}

	// Create a PostgreSQL container using the utility function
	postgres, err := testcontainers.CreatePostgresContainer(ctx, "test", "test", "testdb", "5432")
	if err != nil {
		log.Fatalf("Failed to create PostgreSQL container: %v", err)
	}
	defer postgres.Stop(ctx)

	// Get a connection string
	connStr, err := testcontainers.GetContainerConnectionString(ctx, postgres, "postgres", "5432/tcp")
	if err != nil {
		log.Fatalf("Failed to get connection string: %v", err)
	}

	fmt.Printf("Connection string: %s\n", connStr)
}
```

### Managing Multiple Containers

```go
package main

import (
	"context"
	"log"

	"github.com/Matrics-io/platform-tests/SDKs/Go/testcontainers"
)

func main() {
	ctx := context.Background()

	// Create configurations for multiple containers
	postgresConfig := testcontainers.BrickConfig{
		BrickType:    "postgres",
		InstanceName: "postgres-test",
		EnvironmentVariables: map[string]string{
			"POSTGRES_USER":     "test",
			"POSTGRES_PASSWORD": "test",
			"POSTGRES_DB":       "testdb",
		},
		ExposedPort: "5432/tcp",
		HostPort:    "5432",
	}

	redisConfig := testcontainers.BrickConfig{
		BrickType:    "dapr-redis",
		InstanceName: "redis-test",
		ExposedPort:  "6379/tcp",
		HostPort:     "6379",
	}

	// Create an infrastructure manager
	manager := testcontainers.NewInfrastructureManager([]testcontainers.BrickConfig{
		postgresConfig,
		redisConfig,
	})

	// Start all containers
	if err := manager.StartAll(ctx); err != nil {
		log.Fatalf("Failed to start containers: %v", err)
	}

	// Use the containers...

	// Stop all containers when done
	if err := manager.StopAll(ctx); err != nil {
		log.Fatalf("Failed to stop containers: %v", err)
	}
}
```

## Advanced Configuration

### Custom Waiting Strategies

```go
import (
	"time"
	
	"github.com/docker/go-connections/nat"
	"github.com/testcontainers/testcontainers-go/wait"
)

config := testcontainers.BrickConfig{
	// ... other configuration ...
	CustomWaitStrategy: wait.ForHTTP("/health").
		WithPort(nat.Port("8080/tcp")).
		WithStatusCodeMatcher(func(status int) bool {
			return status == 200
		}).
		WithStartupTimeout(60 * time.Second),
}
```

### Volume Mappings

```go
config := testcontainers.BrickConfig{
	// ... other configuration ...
	Volumes: []testcontainers.VolumeMapping{
		{
			HostPath:      "/path/on/host",
			ContainerPath: "/path/in/container",
		},
	},
}
```

## Extending the Package

### Adding Custom Container Types

```go
func init() {
	// Register a custom container type
	testcontainers.RegisterContainerType("my-service", "my-org/my-service:latest")
}
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request. 