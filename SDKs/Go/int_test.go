package main

import (
	"context"
	"fmt"
	"path/filepath"
	"runtime"
	"testing"
	"time"

	tc "github.com/Matrics-io/platform-tests/testcontainers"
	"github.com/docker/docker/api/types/container"
	dockerClient "github.com/docker/docker/client"
	"github.com/docker/go-connections/nat"

	_ "github.com/lib/pq" // Postgres driver
)

// getTestFilePath returns an absolute path for a file or directory relative to this source file.
func getTestFilePath(relPath string) string {
	_, currentFile, _, ok := runtime.Caller(0)
	if !ok {
		panic("unable to get caller information")
	}
	return filepath.Join(filepath.Dir(currentFile), relPath)
}

// mergeWithExtraBinding wraps an existing HostConfigModifier (if any)
// and ensures that container port "5432/tcp" is bound to host port "5432".
func mergeWithExtraBinding(original func(*container.HostConfig)) func(*container.HostConfig) {
	return func(hc *container.HostConfig) {
		if original != nil {
			original(hc)
		}
		if hc.PortBindings == nil {
			hc.PortBindings = make(map[nat.Port][]nat.PortBinding)
		}
		port := nat.Port("5432/tcp")
		binding := nat.PortBinding{HostIP: "0.0.0.0", HostPort: "5432"}
		hc.PortBindings[port] = append(hc.PortBindings[port], binding)
	}
}

// containerIDGetter is a small interface to extract a container's ID.
type containerIDGetter interface {
	ContainerID() string
}

func TestDockerComposeIntegration(t *testing.T) {
	ctx := context.Background()

	// Compute absolute paths.
	initFilePath := getTestFilePath("../../services/gin-go-demo/init.sql")
	componentsPath := getTestFilePath("../../components")
	loadTestScript := getTestFilePath("../../tests/performance/scripts/load-test.js")
	reportsPath := getTestFilePath("../../tests/performance/reports")

	// Create a user-defined network "platform-tests-bridge".
	networkName, err := tc.CreateNetwork(ctx, "platform-tests-bridge")
	if err != nil {
		t.Fatalf("Failed to create network 'platform-tests-bridge': %v", err)
	}
	t.Logf("Created network: %s", networkName)

	// 1. Postgres container.
	dbConfig := tc.BrickConfig{
		BrickType:    "postgres",
		InstanceName: "postgres-db",
		EnvironmentVariables: map[string]string{
			"POSTGRES_USER":     "postgres",
			"POSTGRES_PASSWORD": "postgres",
			"POSTGRES_DB":       "demo_db",
		},
		ExposedPort: "5432/tcp",
		Networks:    []string{"platform-tests-bridge"},
		NetworkAliases: map[string][]string{
			"platform-tests-bridge": {"db"},
		},
		HealthCheck: &tc.HealthCheckConfig{
			Test:        []string{"CMD-SHELL", "pg_isready -U postgres -d demo_db"},
			Interval:    10 * time.Second,
			Retries:     10,
			StartPeriod: 5 * time.Second,
			Timeout:     5 * time.Second,
		},
		HostConfigModifier: func(hc *container.HostConfig) {
			// Bind mount init.sql.
			if hc.Binds == nil {
				hc.Binds = []string{}
			}
			mount := fmt.Sprintf("%s:/docker-entrypoint-initdb.d/init.sql", initFilePath)
			hc.Binds = append(hc.Binds, mount)
			// Map container port 5432 to host port 5433.
			if hc.PortBindings == nil {
				hc.PortBindings = make(map[nat.Port][]nat.PortBinding)
			}
			port := nat.Port("5432/tcp")
			binding := nat.PortBinding{HostIP: "0.0.0.0", HostPort: "5433"}
			hc.PortBindings[port] = append(hc.PortBindings[port], binding)
		},
	}
	dbConfig.HostConfigModifier = mergeWithExtraBinding(dbConfig.HostConfigModifier)

	// 2. gin-go container.
	ginGoConfig := tc.BrickConfig{
		BrickType:    "gin-go",
		InstanceName: "gin-go",
		EnvironmentVariables: map[string]string{
			"DB_HOST":       "postgres-db",
			"DB_PORT":       "5432",
			"DB_USER":       "postgres",
			"DB_PASSWORD":   "postgres",
			"DB_NAME":       "demo_db",
			"JWT_SECRET":    "supersecretkey",
			"DAPR_BASE_URL": "http://dapr-gin:3500/v1.0",
		},
		ExposedPort: "8081/tcp",
		HostPort:    "8081",
		Networks:    []string{"platform-tests-bridge"},
		HostConfigModifier: func(hc *container.HostConfig) {
			hc.PortBindings = map[nat.Port][]nat.PortBinding{
				"8081/tcp": {{HostIP: "0.0.0.0", HostPort: "8081"}},
			}
		},
	}
	ginGoConfig.HostConfigModifier = mergeWithExtraBinding(ginGoConfig.HostConfigModifier)

	// 3. fib-calc container.
	fibCalcConfig := tc.BrickConfig{
		BrickType:    "fib-calc",
		InstanceName: "fib-calc",
		EnvironmentVariables: map[string]string{
			"DB_HOST":       "postgres-db",
			"DB_PORT":       "5432",
			"DB_USER":       "postgres",
			"DB_PASSWORD":   "postgres",
			"DB_NAME":       "demo_db",
			"FIB_CALC_URL":  "http://fib-calc-service:8082",
			"DAPR_BASE_URL": "http://dapr-fib:3501/v1.0/invoke",
		},
		ExposedPort: "8082/tcp",
		HostPort:    "8082",
		Networks:    []string{"platform-tests-bridge"},
	}
	fibCalcConfig.HostConfigModifier = mergeWithExtraBinding(nil)

	// 4. dapr-gin container.
	daprGinConfig := tc.BrickConfig{
		BrickType:    "dapr",
		InstanceName: "dapr-gin",
		CommandArguments: []string{
			"/daprd",
			"--app-id", "gin-go",
			"--app-channel-address", "gin-go",
			"--app-port", "8081",
			"--dapr-http-port", "3500",
			"--resources-path", "/app/.dapr/components",
		},
		ExposedPort: "3500/tcp",
		HostPort:    "3500",
		Networks:    []string{"platform-tests-bridge"},
		HostConfigModifier: func(hc *container.HostConfig) {
			if hc.Binds == nil {
				hc.Binds = []string{}
			}
			mount := fmt.Sprintf("%s:/app/.dapr/components", componentsPath)
			hc.Binds = append(hc.Binds, mount)
		},
	}
	// No extra merge for dapr-gin.

	// 5. dapr-fib container.
	daprFibConfig := tc.BrickConfig{
		BrickType:    "dapr",
		InstanceName: "dapr-fib",
		CommandArguments: []string{
			"/daprd",
			"--app-id", "fib-calc",
			"--app-channel-address", "fib-calc",
			"--app-port", "8082",
			"--dapr-http-port", "3502",
			"--dapr-grpc-port", "50001",
			"--resources-path", "/app/.dapr/components",
		},
		ExposedPort: "3502/tcp",
		HostPort:    "3502",
		Networks:    []string{"platform-tests-bridge"},
		HostConfigModifier: func(hc *container.HostConfig) {
			if hc.Binds == nil {
				hc.Binds = []string{}
			}
			mount := fmt.Sprintf("%s:/app/.dapr/components", componentsPath)
			hc.Binds = append(hc.Binds, mount)

			if hc.PortBindings == nil {
				hc.PortBindings = make(map[nat.Port][]nat.PortBinding)
			}
			portGrpc := nat.Port("50001/tcp")
			bindingGrpc := nat.PortBinding{HostIP: "0.0.0.0", HostPort: "50001"}
			hc.PortBindings[portGrpc] = append(hc.PortBindings[portGrpc], bindingGrpc)
		},
	}
	// No extra merge for dapr-fib.

	// 6. k6-gin container.
	k6GinConfig := tc.BrickConfig{
		BrickType:        "k6-gin",
		InstanceName:     "k6-gin",
		CommandArguments: []string{"run", "/src/load-test.js"},
		ExposedPort:      "8080/tcp",
		HostPort:         "",
		EnvironmentVariables: map[string]string{
			"BASE_URL": "http://dapr-gin:3500/v1.0/invoke/gin-go-demo/method",
		},
		Networks: []string{"platform-tests-bridge"},
		HostConfigModifier: func(hc *container.HostConfig) {
			if hc.Binds == nil {
				hc.Binds = []string{}
			}
			scriptMount := fmt.Sprintf("%s:/src/load-test.js", loadTestScript)
			reportsMount := fmt.Sprintf("%s:/src/reports", reportsPath)
			hc.Binds = append(hc.Binds, scriptMount, reportsMount)
		},
		HealthCheck: &tc.HealthCheckConfig{
			Test:        []string{"CMD-SHELL", "exit 0"},
			Interval:    5 * time.Second,
			Retries:     5,
			StartPeriod: 2 * time.Second,
			Timeout:     5 * time.Second,
		},
	}

	// Ordered container configurations.
	orderedConfigs := []tc.BrickConfig{
		dbConfig,
		ginGoConfig,
		fibCalcConfig,
		daprGinConfig,
		daprFibConfig,
		k6GinConfig,
	}

	k6ContainerName := orderedConfigs[len(orderedConfigs)-1].InstanceName

	var bricks []tc.ContainerBrick

	// Start each container sequentially.
	for _, config := range orderedConfigs {
		t.Logf("Starting container: %s", config.InstanceName)
		brick := tc.NewContainerBrick(config)
		if err := brick.Start(ctx); err != nil {
			t.Fatalf("Failed to start container %s: %v", config.InstanceName, err)
		}
		bricks = append(bricks, brick)
		time.Sleep(2 * time.Second)
	}
	t.Log("All containers started successfully.")

	// Create a Docker client.
	cli, err := dockerClient.NewClientWithOpts(dockerClient.FromEnv, dockerClient.WithAPIVersionNegotiation())
	if err != nil {
		t.Fatalf("Failed to create Docker client: %v", err)
	}

	// Poll every 20 seconds to check if the k6 container is still running.
	t.Logf("Polling status for container %s every 20 seconds...", k6ContainerName)
	for {
		inspect, err := cli.ContainerInspect(ctx, k6ContainerName)
		if err != nil {
			t.Fatalf("Error inspecting container %s: %v", k6ContainerName, err)
		}
		if !inspect.State.Running {
			t.Log("k6 container has stopped running.")
			break
		}
		t.Log("k6 container is still running; waiting 20 seconds...")
		time.Sleep(20 * time.Second)
	}

	// Once k6 stops, stop all containers.
	t.Log("Stopping all containers...")
	for _, brick := range bricks {
		if err := brick.Stop(ctx); err != nil {
			t.Logf("Error stopping container: %v", err)
		}
	}
}
