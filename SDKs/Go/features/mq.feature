@mq
Feature: Messaging Queue Handling

  Background:
    Given the messaging system is available
    And the "orders" topic/queue is configured
    And the "dead-letter" queue is configured for failed messages

  Scenario: Dispatch and receive a message successfully
    When a message with payload:
        """
        {
            "orderId": "12345",
            "status": "PENDING"
        }
        """ 
    And the message is sent to "orders"
    Then a message should be received from "orders"
    And the message payload should include "orderId" = "12345"
    And the message header "Content-Type" should be "application/json"

  Scenario: Message deduplication and ordering
    Given messages with IDs "1", "2", "2", "3" are sent to "orders"
    Then messages received from "orders" should be in order: "1", "2", "3"
    And no duplicate messages should be received

  Scenario: Retry and idempotency behavior
    Given a message with ID "retry-001" is sent to "orders"
    And the consumer fails to process it initially
    Then the message should be retried up to 3 times
    And the consumer should handle it idempotently

  Scenario: Message expiration handling
    Given a message with TTL of 5 seconds is sent to "orders"
    And it is not consumed within 5 seconds
    Then it should be expired and not delivered from "orders"

  Scenario: Topic subscription filtering
    Given a subscriber with filter "status = 'SHIPPED'" is listening on "orders"
    When a message with status "SHIPPED" is sent to "orders"
    Then the message should be delivered to the subscriber
    When a message with status "CANCELLED" is sent to "orders"
    Then the message should not be delivered to the subscriber

  Scenario: Message security validation
    When a signed and encrypted message is sent to "orders"
    Then the message should be decrypted and verified successfully

