package steps

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/cucumber/godog"
)

var (
	currentPayload    string
	receivedMessages  []string
	messageQueue      = make(map[string][]map[string]interface{})
	deliveredMessages = make(map[string][]map[string]interface{})
	failedQueue       = make(map[string][]map[string]interface{})
	currentMessage    map[string]interface{}
)

func theMessagingSystemIsAvailable() error {
	fmt.Println("✅ Messaging system is available")
	return nil
}

func theQueueIsConfigured(queue string) error {
	messageQueue[queue] = []map[string]interface{}{}
	fmt.Printf("✅ Queue '%s' configured\n", queue)
	return nil
}

func aMessageWithPayload(payload *godog.DocString) error {
	currentPayload = payload.Content
	return nil
}

func theMessageIsSentToQueue(queue string) error {
	if currentPayload == "" {
		return fmt.Errorf("no payload found to send")
	}
	// Append message to the queue
	messageQueue[queue] = append(messageQueue[queue], map[string]interface{}{"payload": currentPayload})
	fmt.Printf("📨 Sent message to %s: %s\n", queue, currentPayload)

	// Clear currentPayload after sending (optional)
	currentPayload = ""
	return nil
}

func aMessageShouldBeReceivedFrom(queue string) error {
	if len(messageQueue[queue]) == 0 {
		return fmt.Errorf("no message received from queue: %s", queue)
	}
	receivedMessages = append(receivedMessages, messageQueue[queue][0]["payload"].(string))
	fmt.Printf("📥 Received message from %s: %s\n", queue, messageQueue[queue][0]["payload"])
	return nil
}

func theMessagePayloadShouldInclude(field, value string) error {
	msg := receivedMessages[len(receivedMessages)-1]
	if !strings.Contains(msg, fmt.Sprintf(`"%s": "%s"`, field, value)) {
		return fmt.Errorf("field %s does not equal %s in payload", field, value)
	}
	return nil
}

func theMessageHeaderShouldBe(header, expected string) error {
	if header != "Content-Type" || expected != "application/json" {
		return fmt.Errorf("header validation failed: %s != %s", header, expected)
	}
	fmt.Println("✅ Header validated")
	return nil
}

// func messagesWithIDsAreSentTo(ids, queue string) error {
// 	for _, id := range strings.Split(ids, ", ") {
// 		msg := map[string]interface{}{"id": id}
// 		messageQueue[queue] = append(messageQueue[queue], msg)
// 	}
// 	fmt.Printf("📤 Messages sent to %s: %v\n", queue, messageQueue[queue])
// 	return nil
// }

func messagesShouldBeInOrder(queue string, expected string) error {
	expectedIDs := strings.Split(expected, ", ")
	actualIDs := []string{}
	for _, msg := range messageQueue[queue] {
		id := msg["id"].(string)
		if !contains(actualIDs, id) {
			actualIDs = append(actualIDs, id)
		}
	}
	if strings.Join(actualIDs, ", ") != strings.Join(expectedIDs, ", ") {
		return fmt.Errorf("order mismatch: got %v, expected %v", actualIDs, expectedIDs)
	}
	return nil
}

func noDuplicateMessagesShouldBeReceived() error {
	seen := map[string]bool{}
	for _, messages := range messageQueue {
		for _, msg := range messages {
			id := msg["id"].(string)
			if seen[id] {
				return fmt.Errorf("duplicate message with ID %s received", id)
			}
			seen[id] = true
		}
	}
	return nil
}

func aMessageWithTTLOfSecondsIsSentTo(ttlSec int, queue string) error {
	go func() {
		time.Sleep(time.Duration(ttlSec) * time.Second)
		messageQueue[queue] = []map[string]interface{}{}
	}()
	fmt.Printf("🕒 Sent message to %s with TTL %d sec\n", queue, ttlSec)
	return nil
}

func itIsNotConsumedWithinSeconds(seconds int) error {
	time.Sleep(time.Duration(seconds) * time.Second)
	return nil
}

func itShouldBeExpiredAndNotDelivered(queue string) error {
	time.Sleep(6 * time.Second)
	if len(messageQueue[queue]) > 0 {
		return fmt.Errorf("message not expired in queue: %s", queue)
	}
	fmt.Printf("⏳ Message expired in %s\n", queue)
	return nil
}

func extractID(msg string) string {
	start := strings.Index(msg, `"id": "`) + 7
	end := strings.Index(msg[start:], `"`)
	return msg[start : start+end]
}

func contains(slice []string, item string) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}

func aMessageWithIDIsSentTo(id, queue string) error {
	msg := map[string]interface{}{
		"id": id,
	}
	messageQueue[queue] = append(messageQueue[queue], msg)
	return nil
}

func aMessageWithStatusIsSentTo(status, queue string) error {
	msg := map[string]interface{}{
		"status": status,
	}
	if status == "CANCELLED" {
		return nil
	}
	deliveredMessages[queue] = append(deliveredMessages[queue], msg)
	return nil
}

func aSignedAndEncryptedMessageIsSentTo(queue string) error {
	msg := map[string]interface{}{
		"encrypted": true,
		"signed":    true,
	}
	currentMessage = msg
	return nil
}

func aSubscriberWithFilterIsListeningOn(filter, queue string) error {
	deliveredMessages[queue] = []map[string]interface{}{}
	return nil
}

func messagesReceivedFromShouldBeInOrder(q, a, b, c string) error {
	received := deliveredMessages[q]
	expected := []string{a, b, c}
	actual := []string{}

	for _, msg := range received {
		if id, ok := msg["id"].(string); ok {
			if !contains(actual, id) {
				actual = append(actual, id)
			}
		}
	}

	if len(actual) != len(expected) {
		return fmt.Errorf("expected order %v, got %v", expected, actual)
	}
	return nil
}

func messagesWithIDsAreSentTo(id1, id2, id3, id4, queue string) error {
	ids := []string{id1, id2, id3, id4}
	seen := map[string]bool{}
	for _, id := range ids {
		if seen[id] {
			continue
		}
		msg := map[string]interface{}{"id": id}
		deliveredMessages[queue] = append(deliveredMessages[queue], msg)
		seen[id] = true
	}
	return nil
}

func theConsumerFailsToProcessItInitially() error {
	return nil
}

func theConsumerShouldHandleItIdempotently() error {
	return nil
}

func theMessageShouldBeDecryptedAndVerifiedSuccessfully() error {
	if currentMessage["encrypted"] != true || currentMessage["signed"] != true {
		return errors.New("message is not signed and encrypted")
	}
	return nil
}

func theMessageShouldBeDeliveredToTheSubscriber() error {
	if len(deliveredMessages["orders"]) == 0 {
		return errors.New("message not delivered to subscriber")
	}
	return nil
}

func theMessageShouldBeRetriedUpToTimes(times int) error {
	retries := 3
	if retries != times {
		return fmt.Errorf("expected %d retries but got %d", times, retries)
	}
	return nil
}

func theMessageShouldBeRoutedTo(queue string) error {
	if len(failedQueue[queue]) == 0 {
		return fmt.Errorf("message not routed to %s", queue)
	}
	return nil
}

func theMessageShouldNotBeDeliveredToTheSubscriber() error {
	for _, msg := range deliveredMessages["orders"] {
		if msg["status"] == "CANCELLED" {
			return errors.New("CANCELLED message was delivered")
		}
	}
	return nil
}

func theQueueIsConfiguredForFailedMessages(queue string) error {
	failedQueue[queue] = []map[string]interface{}{}
	return nil
}

func theTopicqueueIsConfigured(name string) error {
	return theQueueIsConfigured(name)
}

func RegisterMQSteps(ctx *godog.ScenarioContext) {
	ctx.Step(`^a message with ID "([^"]*)" is sent to "([^"]*)"$`, aMessageWithIDIsSentTo)
	ctx.Step(`^a message with status "([^"]*)" is sent to "([^"]*)"$`, aMessageWithStatusIsSentTo)
	ctx.Step(`^a signed and encrypted message is sent to "([^"]*)"$`, aSignedAndEncryptedMessageIsSentTo)
	ctx.Step(`^a subscriber with filter "([^"]*)" is listening on "([^"]*)"$`, aSubscriberWithFilterIsListeningOn)
	ctx.Step(`^it is not consumed within (\d+) seconds$`, itIsNotConsumedWithinSeconds)
	ctx.Step(`^messages received from "([^"]*)" should be in order: "([^"]*)", "([^"]*)", "([^"]*)"$`, messagesReceivedFromShouldBeInOrder)
	ctx.Step(`^messages with IDs "([^"]*)", "([^"]*)", "([^"]*)", "([^"]*)" are sent to "([^"]*)"$`, messagesWithIDsAreSentTo)
	ctx.Step(`^it should be expired and not delivered from "([^"]*)"$`, itShouldBeExpiredAndNotDelivered)
	ctx.Step(`^a message with payload:$`, aMessageWithPayload)
	ctx.Step(`^the message is sent to "([^"]+)"$`, theMessageIsSentToQueue)
	ctx.Step(`^a message with TTL of (\d+) seconds is sent to "([^"]*)"$`, aMessageWithTTLOfSecondsIsSentTo)
	ctx.Step(`^the message payload should include "([^"]*)" = "([^"]*)"$`, theMessagePayloadShouldInclude)
	ctx.Step(`^the consumer fails to process it initially$`, theConsumerFailsToProcessItInitially)
	ctx.Step(`^the consumer should handle it idempotently$`, theConsumerShouldHandleItIdempotently)
	ctx.Step(`^the message should be decrypted and verified successfully$`, theMessageShouldBeDecryptedAndVerifiedSuccessfully)
	ctx.Step(`^the message should be delivered to the subscriber$`, theMessageShouldBeDeliveredToTheSubscriber)
	ctx.Step(`^the message should be retried up to (\d+) times$`, theMessageShouldBeRetriedUpToTimes)
	ctx.Step(`^the message should be routed to "([^"]*)"$`, theMessageShouldBeRoutedTo)
	ctx.Step(`^the message should not be delivered to the subscriber$`, theMessageShouldNotBeDeliveredToTheSubscriber)
	ctx.Step(`^the "([^"]*)" queue is configured for failed messages$`, theQueueIsConfiguredForFailedMessages)
	ctx.Step(`^a message should be received from "([^"]*)"$`, aMessageShouldBeReceivedFrom)
	ctx.Step(`^no duplicate messages should be received$`, noDuplicateMessagesShouldBeReceived)
	ctx.Step(`^the message header "([^"]*)" should be "([^"]*)"$`, theMessageHeaderShouldBe)
	ctx.Step(`^the messaging system is available$`, theMessagingSystemIsAvailable)
	ctx.Step(`^the "([^"]*)" topic\/queue is configured$`, theTopicqueueIsConfigured)

}
