package steps

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/cucumber/godog"
)

var (
	validToken   = os.Getenv("VALID_TOKEN")
	expiredToken = os.Getenv("EXPIRED_TOKEN")
)

func addAuthHeader(req *http.Request, token string) {
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}
}

type apiFeature struct {
	baseURL    string
	lastResp   *http.Response
	lastBody   []byte
	httpClient *http.Client
}

func parseJSONBytes(data []byte) (interface{}, error) {
	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.UseNumber()
	var parsed interface{}
	err := decoder.Decode(&parsed)
	return parsed, err
}

func (a *apiFeature) theAPIBaseURLIsSetTo(url string) error {
	a.baseURL = url
	a.httpClient = &http.Client{Timeout: 5 * time.Second}
	return nil
}

func (a *apiFeature) sendRequest(method, endpoint string, body io.Reader, token string) error {

	url := a.baseURL + endpoint
	ctx := context.Background()
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	addAuthHeader(req, token)

	start := time.Now()
	resp, err := a.httpClient.Do(req)
	duration := time.Since(start)

	if err != nil {
		return err
	}

	resp.Request = resp.Request.WithContext(context.WithValue(ctx, "responseTime", duration))

	a.lastResp = resp

	defer resp.Body.Close()
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	a.lastBody = bodyBytes

	fmt.Printf("Response Status: %d\nResponse Body: %s\n", resp.StatusCode, string(bodyBytes))
	return nil
}

func (a *apiFeature) iSendRequestTo(method, endpoint string) error {
	var body io.Reader
	if method == "POST" || method == "PUT" {
		data := map[string]interface{}{
			"name":   "test resource",
			"active": true,
		}
		bodyBytes, err := json.Marshal(data)
		if err != nil {
			return err
		}
		body = bytes.NewReader(bodyBytes)
	}
	return a.sendRequest(method, endpoint, body, "")
}

func (a *apiFeature) iSendAPATCHRequestToWithBody(endpoint string, body *godog.DocString) error {
	var bodyReader io.Reader
	if body != nil {
		bodyReader = strings.NewReader(body.Content)
	}
	return a.sendRequest("PATCH", endpoint, bodyReader, "")
}

func (a *apiFeature) iSendAPOSTRequestToWithBody(endpoint string, body *godog.DocString) error {
	if body == nil {
		return fmt.Errorf("body content is required")
	}
	bodyReader := strings.NewReader(body.Content)
	return a.sendRequest("POST", endpoint, bodyReader, "")
}

func (api *apiFeature) iSendAPOSTRequestToWithEmptyBody(ctx context.Context, endpoint string) error {
	emptyBody := []byte(`{}`)
	req, err := http.NewRequestWithContext(ctx, "POST", api.baseURL+endpoint, bytes.NewReader(emptyBody))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := api.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	api.lastResp = resp

	return nil
}

func (a *apiFeature) iSendGETRequestToWithoutAuthorization(endpoint string) error {
	return a.sendRequest("GET", endpoint, nil, "")
}

func (a *apiFeature) iSendGETRequestToWithValidToken(endpoint string) error {
	if validToken == "" {
		return fmt.Errorf("VALID_TOKEN environment variable not set")
	}
	return a.sendRequest("GET", endpoint, nil, validToken)
}

func (a *apiFeature) iSendGETRequestToWithExpiredToken(endpoint string) error {
	if expiredToken == "" {
		return fmt.Errorf("EXPIRED_TOKEN environment variable not set")
	}
	return a.sendRequest("GET", endpoint, nil, expiredToken)
}

func (a *apiFeature) theResponseStatusShouldBe(status int) error {
	if a.lastResp == nil {
		return fmt.Errorf("no response received")
	}
	if a.lastResp.StatusCode != status {
		return fmt.Errorf("expected status %d but got %d", status, a.lastResp.StatusCode)
	}
	return nil
}

func (a *apiFeature) theResponseTimeShouldBeLessThan(ms int) error {
	if a.lastResp == nil {
		return fmt.Errorf("no response received")
	}
	if respTime, ok := a.lastResp.Request.Context().Value("responseTime").(time.Duration); ok {
		if respTime.Milliseconds() >= int64(ms) {
			return fmt.Errorf("response time %d ms exceeded limit of %d ms", respTime.Milliseconds(), ms)
		}
		return nil
	}
	return fmt.Errorf("response time not measured")
}

func (a *apiFeature) theResponseShouldContain(fields string) error {
	if a.lastBody == nil {
		return fmt.Errorf("no response body received")
	}
	parsed, err := parseJSONBytes(a.lastBody)
	if err != nil {
		return fmt.Errorf("response body is not valid JSON: %v", err)
	}

	jsonMap, ok := parsed.(map[string]interface{})
	if !ok {
		return fmt.Errorf("response JSON is not an object")
	}

	for _, field := range strings.Split(fields, ",") {
		field = strings.TrimSpace(field)
		if _, ok := jsonMap[field]; !ok {
			return fmt.Errorf("response does not contain expected field: %s", field)
		}
	}
	return nil
}

func (a *apiFeature) theFieldShouldBeOfType(field, typ string) error {
	if a.lastBody == nil {
		return fmt.Errorf("no response body received")
	}
	var jsonMap map[string]interface{}
	err := json.Unmarshal(a.lastBody, &jsonMap)
	if err != nil {
		return fmt.Errorf("response body is not valid JSON: %v", err)
	}
	val, ok := jsonMap[field]
	if !ok {
		return fmt.Errorf("response does not contain field: %s", field)
	}
	switch typ {
	case "integer":
		switch v := val.(type) {
		case float64:
			if v != float64(int(v)) {
				return fmt.Errorf("field %s is not an integer", field)
			}
		default:
			return fmt.Errorf("field %s is not an integer", field)
		}
	case "string":
		if _, ok := val.(string); !ok {
			return fmt.Errorf("field %s is not a string", field)
		}
	case "boolean":
		if _, ok := val.(bool); !ok {
			return fmt.Errorf("field %s is not a boolean", field)
		}
	default:
		return fmt.Errorf("unsupported type check: %s", typ)
	}
	return nil
}

func (a *apiFeature) iSendAPUTRequestToWithTheSameDataMultipleTimes(endpoint string) error {
	data := map[string]interface{}{
		"name":   "test resource",
		"active": true,
	}
	bodyBytes, err := json.Marshal(data)
	if err != nil {
		return err
	}
	for i := 0; i < 3; i++ {
		err := a.sendRequest("PUT", endpoint, bytes.NewReader(bodyBytes), "")
		if err != nil {
			return err
		}
		if a.lastResp.StatusCode != 200 {
			return fmt.Errorf("expected status 200, got %d on iteration %d", a.lastResp.StatusCode, i+1)
		}
	}
	return nil
}

func (a *apiFeature) iSendADELETERequestToMultipleTimes(endpoint string) error {
	for i := 0; i < 3; i++ {
		err := a.sendRequest("DELETE", endpoint, nil, "")
		if err != nil {
			return err
		}
		if a.lastResp.StatusCode != 204 && a.lastResp.StatusCode != 404 {
			return fmt.Errorf("expected status 204 or 404, got %d on iteration %d", a.lastResp.StatusCode, i+1)
		}
	}
	return nil
}

func (a *apiFeature) theResponseHeaderShouldBePresent(header string) error {
	if a.lastResp == nil {
		return fmt.Errorf("no response received")
	}
	if a.lastResp.Header.Get(header) == "" {
		return fmt.Errorf("response header %q not present", header)
	}
	return nil
}

func (a *apiFeature) iSendAOPTIONSRequestTo(endpoint string) error {
	return a.sendRequest("OPTIONS", endpoint, nil, "")
}

func (a *apiFeature) iSendAPUTRequestToWithBody(endpoint string, body *godog.DocString) error {
	var bodyReader io.Reader
	if body != nil {
		bodyReader = strings.NewReader(body.Content)
	}
	return a.sendRequest("PUT", endpoint, bodyReader, "")
}

func (a *apiFeature) theResponseHeaderShouldBe(header, expectedValue string) error {
	if a.lastResp == nil {
		return fmt.Errorf("no response received")
	}
	actualValue := a.lastResp.Header.Get(header)
	if actualValue != expectedValue {
		return fmt.Errorf("expected header %q to be %q but got %q", header, expectedValue, actualValue)
	}
	return nil
}

func RegisterAPISteps(ctx *godog.ScenarioContext) {
	api := &apiFeature{}

	ctx.Step(`^the API base URL is set to "([^"]*)"$`, api.theAPIBaseURLIsSetTo)
	ctx.Step(`^I send a (GET|POST|PUT|DELETE|PATCH) request to "([^"]*)"$`, api.iSendRequestTo)
	ctx.Step(`^I send a POST request to "([^"]*)" with body:$`, api.iSendAPOSTRequestToWithBody)
	ctx.Step(`^I send a PATCH request to "([^"]*)" with body:$`, api.iSendAPATCHRequestToWithBody)
	ctx.Step(`^I send a POST request to "([^"]*)" with empty body$`, api.iSendAPOSTRequestToWithEmptyBody)
	ctx.Step(`^I send a GET request to "([^"]*)" without authorization$`, api.iSendGETRequestToWithoutAuthorization)
	ctx.Step(`^I send a GET request to "([^"]*)" with valid token$`, api.iSendGETRequestToWithValidToken)
	ctx.Step(`^I send a GET request to "([^"]*)" with expired token$`, api.iSendGETRequestToWithExpiredToken)
	ctx.Step(`^the response status should be (\d+)$`, api.theResponseStatusShouldBe)
	ctx.Step(`^the response time should be less than (\d+) milliseconds$`, api.theResponseTimeShouldBeLessThan)
	ctx.Step(`^the response should contain "([^"]*)"$`, api.theResponseShouldContain)
	ctx.Step(`^the field "([^"]*)" should be of type (integer|string|boolean)$`, api.theFieldShouldBeOfType)
	ctx.Step(`^I send a PUT request to "([^"]*)" with the same data multiple times$`, api.iSendAPUTRequestToWithTheSameDataMultipleTimes)
	ctx.Step(`^I send a DELETE request to "([^"]*)" multiple times$`, api.iSendADELETERequestToMultipleTimes)
	ctx.Step(`^the response header "([^"]*)" should be present$`, api.theResponseHeaderShouldBePresent)
	ctx.Step(`^I send a OPTIONS request to "([^"]*)"$`, api.iSendAOPTIONSRequestTo)
	ctx.Step(`^I send a PUT request to "([^"]*)" with body:$`, api.iSendAPUTRequestToWithBody)
	ctx.Step(`^the response header "([^"]*)" should be "([^"]*)"$`, api.theResponseHeaderShouldBe)
}
