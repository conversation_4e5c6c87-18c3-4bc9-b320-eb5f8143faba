package steps

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"strings"

	"github.com/cucumber/godog"
	"github.com/jackc/pgx/v5"
	_ "github.com/lib/pq"
)

var db *sql.DB

type pgTest struct {
	ctx      context.Context
	conn     *pgx.Conn
	host     string
	port     string
	user     string
	password string
	dbname   string
	tx       pgx.Tx
}

func (p *pgTest) setDBHost(host string) error {
	p.host = host
	return nil
}

func (p *pgTest) setDBPort(port string) error {
	p.port = port
	return nil
}

func (p *pgTest) setDBUser(user string) error {
	p.user = user
	return nil
}

func (p *pgTest) setDBPassword(password string) error {
	p.password = password
	return nil
}

func (p *pgTest) setDBName(dbname string) error {
	p.dbname = dbname
	return nil
}

func (p *pgTest) connectToDB() error {
	var err error
	p.ctx = context.Background()

	host := getEnvOrDefault("DB_HOST", "localhost")
	port := getEnvOrDefault("DB_PORT", "5433")
	user := getEnvOrDefault("DB_USER", "postgres")
	password := getEnvOrDefault("DB_PASSWORD", "postgres")
	dbname := getEnvOrDefault("DB_NAME", "demo_db")

	connStr := fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable",
		user, password, host, port, dbname)

	p.conn, err = pgx.Connect(p.ctx, connStr)
	return err
}

func getEnvOrDefault(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}

func (p *pgTest) closeDB() error {
	if p.conn != nil {
		return p.conn.Close(p.ctx)
	}
	return nil
}

func (p *pgTest) iConnectToTheDatabase() error {
	return p.connectToDB()
}

func (p *pgTest) theConnectionShouldBeSuccessful() error {
	if p.conn == nil {
		return fmt.Errorf("no database connection")
	}
	err := p.conn.Ping(p.ctx)
	if err != nil {
		return fmt.Errorf("ping failed: %w", err)
	}
	return nil
}

func (p *pgTest) aPostgreSQLDatabaseIsRunning() error {
	host := p.host
	if host == "" {
		host = "localhost"
	}
	port := p.port
	if port == "" {
		port = "5433"
	}
	user := p.user
	if user == "" {
		user = "postgres"
	}
	password := p.password
	if password == "" {
		password = "postgres"
	}
	dbname := p.dbname
	if dbname == "" {
		dbname = "demo_db"
	}

	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		host, port, user, password, dbname)

	var err error
	db, err = sql.Open("postgres", connStr)
	if err != nil {
		return fmt.Errorf("failed to open DB connection: %w", err)
	}
	return db.Ping()
}

func iExecuteTheFollowingSQL(sql string) error {
	_, err := db.Exec(sql)
	return err
}

func recordExists(table, column, value string) (bool, error) {
	query := fmt.Sprintf("SELECT COUNT(*) FROM %s WHERE %s = $1", table, column)
	var count int
	err := db.QueryRow(query, value).Scan(&count)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func insertIfNotExists(table string, column string, value string) error {
	var count int
	query := fmt.Sprintf("SELECT COUNT(*) FROM %s WHERE %s = $1", table, column)
	err := db.QueryRow(query, value).Scan(&count)
	if err != nil {
		return fmt.Errorf("query error: %w", err)
	}

	if count == 0 {
		insertQuery := fmt.Sprintf("INSERT INTO %s(%s) VALUES($1)", table, column)
		_, err := db.Exec(insertQuery, value)
		if err != nil {
			return fmt.Errorf("insert error: %w", err)
		}
	}
	return nil
}

func iInsertARecord(table, column string, value string) error {
	return insertIfNotExists(table, column, value)
}

func insertLinkedRecord(childTable, childForeignKeyCol, parentTable, parentKeyCol, parentValCol, parentVal string) error {
	query := fmt.Sprintf(`SELECT %s FROM %s WHERE %s = $1`, parentKeyCol, parentTable, parentValCol)
	var parentID int
	err := db.QueryRow(query, parentVal).Scan(&parentID)
	if err != nil {
		return fmt.Errorf("failed to find parent record: %w", err)
	}

	query = fmt.Sprintf(`INSERT INTO %s(%s) VALUES($1)`, childTable, childForeignKeyCol)
	_, err = db.Exec(query, parentID)
	return err
}

func iInsertLinkedRecord(childTable, parentTable, foreignKey, parentCol, parentVal string) error {
	return insertLinkedRecord(childTable, foreignKey, parentTable, "id", parentCol, parentVal)
}

func theRecordShouldExist(table, column, value string) error {
	exists, err := recordExists(table, column, value)
	if err != nil {
		return err
	}
	if !exists {
		return fmt.Errorf("record not found in %s where %s = %s", table, column, value)
	}
	return nil
}

func linkedRecordExists(
	childTable string, childForeignKeyCol string,
	parentTable string, parentIDCol string,
	parentMatchCol string, parentMatchVal string,
) (bool, error) {
	query := fmt.Sprintf(`
		SELECT COUNT(*) FROM %s c
		JOIN %s p ON c.%s = p.%s
		WHERE p.%s = $1`, childTable, parentTable, childForeignKeyCol, parentIDCol, parentMatchCol)

	var count int
	err := db.QueryRow(query, parentMatchVal).Scan(&count)
	if err != nil {
		return false, fmt.Errorf("query error: %w", err)
	}
	return count > 0, nil
}

func theLinkedRecordShouldExist(childTable, childForeignKeyCol, parentTable, parentKeyCol, parentValCol string, parentVal interface{}) error {
	strVal, ok := parentVal.(string)
	if !ok {
		return fmt.Errorf("expected string for parentVal, got %T", parentVal)
	}

	exists, err := linkedRecordExists(childTable, childForeignKeyCol, parentTable, parentKeyCol, parentValCol, strVal)
	if err != nil {
		return err
	}
	if !exists {
		return fmt.Errorf("linked record not found in %s linked to %s where %s = %v", childTable, parentTable, parentValCol, parentVal)
	}
	return nil
}

func (p *pgTest) theFollowingTablesShouldExist(tableNames *godog.Table) error {
	for _, row := range tableNames.Rows[1:] { // skip header
		table := row.Cells[0].Value
		var exists bool
		err := p.conn.QueryRow(p.ctx,
			`SELECT EXISTS (
				SELECT 1 
				FROM information_schema.tables 
				WHERE table_name=$1
			)`, table).Scan(&exists)
		if err != nil {
			return err
		}
		if !exists {
			return fmt.Errorf("table %s does not exist", table)
		}
	}
	return nil
}

func (p *pgTest) theFollowingColumnsShouldExistInTable(tableName string, columns *godog.Table) error {
	for _, row := range columns.Rows[1:] {
		columnName := row.Cells[0].Value
		dataType := row.Cells[1].Value
		isNullable := row.Cells[2].Value

		var actualDataType, actualIsNullable string
		err := p.conn.QueryRow(p.ctx,
			`SELECT data_type, is_nullable 
			FROM information_schema.columns 
			WHERE table_name = $1 AND column_name = $2`, tableName, columnName).
			Scan(&actualDataType, &actualIsNullable)
		if err != nil {
			return fmt.Errorf("column %s not found in table %s", columnName, tableName)
		}

		if dataType == "text" && actualDataType == "character varying" {
			actualDataType = "text"
		}

		if actualDataType != dataType {
			return fmt.Errorf("expected data type %s for column %s but got %s", dataType, columnName, actualDataType)
		}

		if (actualIsNullable == "YES") != (isNullable == "YES") {
			return fmt.Errorf("expected nullable=%s for column %s but got %s", isNullable, columnName, actualIsNullable)
		}
	}
	return nil
}

func (p *pgTest) theFollowingIndexesShouldExistOnTable(tableName string, indexes *godog.Table) error {
	for _, row := range indexes.Rows[1:] {
		indexName := row.Cells[0].Value

		var exists bool
		err := p.conn.QueryRow(p.ctx,
			`SELECT EXISTS (
				SELECT 1 
				FROM pg_indexes 
				WHERE tablename = $1 AND indexname = $2
			)`, tableName, indexName).Scan(&exists)
		if err != nil {
			return err
		}
		if !exists {
			return fmt.Errorf("index %s does not exist on table %s", indexName, tableName)
		}
	}
	return nil
}

func (p *pgTest) theForeignKeyShouldExistOnTable(fkName, tableName string) error {
	var exists bool
	err := p.conn.QueryRow(p.ctx,
		`SELECT EXISTS (
			SELECT 1 
			FROM information_schema.table_constraints 
			WHERE constraint_type = 'FOREIGN KEY' 
			AND constraint_name = $1
			AND table_name = $2
		)`, fkName, tableName).Scan(&exists)
	if err != nil {
		return err
	}
	if !exists {
		return fmt.Errorf("foreign key %s does not exist on table %s", fkName, tableName)
	}
	return nil
}

func (p *pgTest) iInspectTheConstraint() error {
	return nil
}

func (p *pgTest) itShouldReferenceTableOnColumn(refTable, refColumn, fkName string) error {
	var foreignTable, foreignColumn string
	err := p.conn.QueryRow(p.ctx,
		`SELECT 
			ccu.table_name AS foreign_table_name,
			ccu.column_name AS foreign_column_name
		FROM information_schema.table_constraints AS tc 
		JOIN information_schema.key_column_usage AS kcu ON tc.constraint_name = kcu.constraint_name
		JOIN information_schema.constraint_column_usage AS ccu ON ccu.constraint_name = tc.constraint_name
		WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.constraint_name = $1`,
		fkName).Scan(&foreignTable, &foreignColumn)
	if err != nil {
		return err
	}
	if foreignTable != refTable {
		return fmt.Errorf("expected foreign key to reference table %s but got %s", refTable, foreignTable)
	}
	if foreignColumn != refColumn {
		return fmt.Errorf("expected foreign key to reference column %s but got %s", refColumn, foreignColumn)
	}
	return nil
}

func (p *pgTest) theTableHasUniqueConstraintOnColumn(table, column string) error {
	var exists bool
	err := p.conn.QueryRow(p.ctx,
		`SELECT EXISTS (
			SELECT 1 
			FROM information_schema.table_constraints tc
			JOIN information_schema.constraint_column_usage ccu
			ON tc.constraint_name = ccu.constraint_name
			WHERE tc.constraint_type = 'UNIQUE'
			AND tc.table_name = $1
			AND ccu.column_name = $2
		)`, table, column).Scan(&exists)

	if err == nil && exists {
		return nil
	}

	err = p.conn.QueryRow(p.ctx,
		`SELECT EXISTS (
			SELECT 1 
			FROM pg_indexes 
			WHERE tablename = $1 
			AND indexdef LIKE '%' || $2 || '%'
			AND indexdef LIKE '%UNIQUE%'
		)`, table, column).Scan(&exists)

	if err != nil {
		return err
	}

	if !exists {
		return fmt.Errorf("unique constraint on %s.%s does not exist", table, column)
	}

	return nil
}

func (p *pgTest) theColumnIsNotNull(table, column string) error {
	tableName := table
	columnName := column
	if strings.Contains(table, ".") {
		parts := strings.Split(table, ".")
		tableName = parts[0]
		columnName = parts[1]
	}

	var isNullable string
	err := p.conn.QueryRow(p.ctx,
		`SELECT is_nullable FROM information_schema.columns WHERE table_name = $1 AND column_name = $2`,
		tableName, columnName).Scan(&isNullable)
	if err != nil {
		return err
	}
	if isNullable == "YES" {
		return fmt.Errorf("column %s.%s is nullable but expected NOT NULL", tableName, columnName)
	}
	return nil
}

func (p *pgTest) theColumnHasDefaultValue(table, column, expectedDefault string) error {
	tableName := table
	columnName := column
	if strings.Contains(table, ".") {
		parts := strings.Split(table, ".")
		tableName = parts[0]
		columnName = parts[1]
	}

	fmt.Printf("Checking default for table: %s, column: %s\n", tableName, columnName)

	columnName = strings.ToLower(columnName)

	var tableExists bool
	err := p.conn.QueryRow(p.ctx,
		`SELECT EXISTS(
			SELECT 1 FROM information_schema.tables 
			WHERE table_name = $1
		)`, tableName).Scan(&tableExists)

	if err != nil {
		return fmt.Errorf("error checking table existence: %v", err)
	}

	if !tableExists {
		return fmt.Errorf("table %s does not exist", tableName)
	}

	var columnExists bool
	err = p.conn.QueryRow(p.ctx,
		`SELECT EXISTS(
			SELECT 1 FROM information_schema.columns 
			WHERE table_name = $1 
			AND column_name = $2
		)`, tableName, columnName).Scan(&columnExists)

	if err != nil {
		return fmt.Errorf("error checking column existence: %v", err)
	}

	if !columnExists {
		return fmt.Errorf("column %s.%s does not exist", tableName, columnName)
	}

	var columnDefault *string
	err = p.conn.QueryRow(p.ctx,
		`SELECT column_default 
		FROM information_schema.columns 
		WHERE table_name = $1 
		AND column_name = $2`,
		tableName, columnName).Scan(&columnDefault)
	if err != nil {
		return fmt.Errorf("error getting column default: %v", err)
	}

	if columnDefault == nil {
		return fmt.Errorf("no default value for %s.%s", tableName, columnName)
	}

	fmt.Printf("Found default value: %s\n", *columnDefault)
	if !strings.Contains(strings.ToLower(*columnDefault), strings.ToLower(expectedDefault)) {
		return fmt.Errorf("expected default value containing %s for %s.%s but got %v",
			expectedDefault, tableName, columnName, *columnDefault)
	}

	return nil
}

func (p *pgTest) theColumnIsOfType(table, column, expectedType string) error {
	tableName := table
	columnName := column
	if strings.Contains(table, ".") {
		parts := strings.Split(table, ".")
		tableName = parts[0]
		columnName = parts[1]
	}

	var dataType, udt_name string
	err := p.conn.QueryRow(p.ctx,
		`SELECT data_type, udt_name FROM information_schema.columns WHERE table_name = $1 AND column_name = $2`,
		tableName, columnName).Scan(&dataType, &udt_name)
	if err != nil {
		return err
	}

	if expectedType == "serial" && udt_name == "int4" && strings.Contains(dataType, "integer") {
		return nil
	}

	if strings.EqualFold(dataType, expectedType) || strings.EqualFold(udt_name, expectedType) {
		return nil
	}

	return fmt.Errorf("expected data type %s for %s.%s but got %s (udt: %s)",
		expectedType, tableName, columnName, dataType, udt_name)
}

func (p *pgTest) theForeignKeyHasOnDeleteCascade(fkName string) error {
	var deleteRule string
	err := p.conn.QueryRow(p.ctx,
		`SELECT rc.delete_rule
		FROM information_schema.referential_constraints rc
		WHERE constraint_name = $1`, fkName).Scan(&deleteRule)
	if err != nil {
		return err
	}
	if deleteRule != "CASCADE" {
		return fmt.Errorf("expected ON DELETE CASCADE on %s but got %s", fkName, deleteRule)
	}
	return nil
}

func iInsertARecordIntoTableWithColumnValue(table, column, value string) error {
	query := fmt.Sprintf(`INSERT INTO %s (%s) VALUES ($1)`, table, column)

	_, err := db.Exec(query, value)
	if err != nil {
		return fmt.Errorf("failed to insert record into %s where %s = %s: %w", table, column, value, err)
	}

	return nil
}

func (p *pgTest) iRollbackTheTransaction() error {
	if p.tx != nil {
		err := p.tx.Rollback(p.ctx)
		p.tx = nil
		return err
	}
	return fmt.Errorf("no transaction to rollback")
}

func iHaveAValidConnectionToTheDatabase() error {
	if db == nil {
		return fmt.Errorf("database connection is nil")
	}
	return db.Ping()
}

func theUserShouldStillNotExistInTheDatabase(db *sql.DB, email string) error {
	query := "SELECT COUNT(*) FROM users WHERE email = $1"
	var count int
	err := db.QueryRow(query, email).Scan(&count)
	if err != nil {
		return err
	}
	if count > 0 {
		return fmt.Errorf("user still exists in the database")
	}
	return nil
}

func iInsertARecordIntoLinkedToByWhereIs(childTable, parentTable, foreignKeyCol, parentValCol, parentVal string) error {
	var parentID int

	query := fmt.Sprintf(`SELECT id FROM %s WHERE %s = $1`, parentTable, parentValCol)
	err := db.QueryRow(query, parentVal).Scan(&parentID)
	if err != nil {
		return fmt.Errorf("failed to find parent record in %s where %s = %s: %w", parentTable, parentValCol, parentVal, err)
	}

	insertQuery := fmt.Sprintf(`INSERT INTO %s (%s) VALUES ($1)`, childTable, foreignKeyCol)
	_, err = db.Exec(insertQuery, parentID)
	if err != nil {
		return fmt.Errorf("failed to insert into %s with %s = %d: %w", childTable, foreignKeyCol, parentID, err)
	}

	return nil
}

func theRecordInLinkedToByWhereIsShouldExist(childTable, parentTable, foreignKeyCol, parentValCol, parentVal string) error {
	var parentID int

	query := fmt.Sprintf(`SELECT id FROM %s WHERE %s = $1`, parentTable, parentValCol)
	err := db.QueryRow(query, parentVal).Scan(&parentID)
	if err != nil {
		return fmt.Errorf("failed to find parent record in %s where %s = %s: %w", parentTable, parentValCol, parentVal, err)
	}

	existsQuery := fmt.Sprintf(`SELECT EXISTS (SELECT 1 FROM %s WHERE %s = $1)`, childTable, foreignKeyCol)
	var exists bool
	err = db.QueryRow(existsQuery, parentID).Scan(&exists)
	if err != nil {
		return fmt.Errorf("failed to check existence in %s where %s = %d: %w", childTable, foreignKeyCol, parentID, err)
	}
	if !exists {
		return fmt.Errorf("no record found in %s where %s = %d", childTable, foreignKeyCol, parentID)
	}

	return nil
}

func RegisterDBSteps(ctx *godog.ScenarioContext) {
	pg := &pgTest{}

	ctx.Before(func(ctx context.Context, sc *godog.Scenario) (context.Context, error) {
		if pg.conn != nil {
			if pg.tx != nil {
				_ = pg.tx.Rollback(pg.ctx)
				pg.tx = nil
			}
			_ = pg.conn.Close(pg.ctx)
			pg.conn = nil
		}

		err := pg.connectToDB()
		if err != nil {
			return ctx, err
		}

		_, err = pg.conn.Exec(pg.ctx, "DELETE FROM users WHERE email = '<EMAIL>'")
		if err != nil {
			return ctx, err
		}

		return ctx, nil
	})

	ctx.After(func(ctx context.Context, sc *godog.Scenario, err error) (context.Context, error) {
		if pg.conn != nil {
			_, _ = pg.conn.Exec(pg.ctx, "DELETE FROM users WHERE email = '<EMAIL>'")
		}

		if pg.tx != nil {
			rollbackErr := pg.tx.Rollback(pg.ctx)
			if rollbackErr != nil && rollbackErr != pgx.ErrTxClosed {
				return ctx, rollbackErr
			}
			pg.tx = nil
		}

		if pg.conn != nil {
			closeErr := pg.closeDB()
			pg.conn = nil
			return ctx, closeErr
		}

		return ctx, nil
	})

	ctx.Step(`^I set DB host to "([^"]*)"$`, pg.setDBHost)
	ctx.Step(`^I set DB port to "([^"]*)"$`, pg.setDBPort)
	ctx.Step(`^I set DB user to "([^"]*)"$`, pg.setDBUser)
	ctx.Step(`^I set DB password to "([^"]*)"$`, pg.setDBPassword)
	ctx.Step(`^I set DB name to "([^"]*)"$`, pg.setDBName)
	ctx.Step(`^I connect to the database$`, pg.iConnectToTheDatabase)
	ctx.Step(`^the connection should be successful$`, pg.theConnectionShouldBeSuccessful)
	ctx.Step(`^a PostgreSQL database is running$`, pg.aPostgreSQLDatabaseIsRunning)
	ctx.Step(`^I execute the following SQL:$`, iExecuteTheFollowingSQL)
	ctx.Step(`^the following tables should exist:$`, pg.theFollowingTablesShouldExist)
	ctx.Step(`^the following columns should exist in "([^"]*)":$`, pg.theFollowingColumnsShouldExistInTable)
	ctx.Step(`^the following indexes should exist on "([^"]*)":$`, pg.theFollowingIndexesShouldExistOnTable)
	ctx.Step(`^I insert a record into "([^"]*)" with "([^"]*)" = "([^"]*)"$`, iInsertARecordIntoTableWithColumnValue)
	ctx.Step(`^I insert a record into "([^"]+)" where "([^"]+)" is "([^"]+)"$`, iInsertARecord)
	ctx.Step(`^the record in "([^"]+)" where "([^"]+)" is "([^"]+)" should exist$`, theRecordShouldExist)
	ctx.Step(`^I insert a record into "([^"]+)" linked to "([^"]+)" by "([^"]+)" where "([^"]+)" "([^"]+)" is "([^"]+)"$`, iInsertLinkedRecord)
	ctx.Step(`^the record in "([^"]+)" linked to "([^"]+)" by "([^"]+)" where "([^"]+)" "([^"]+)" is "([^"]+)" should exist$`, theLinkedRecordShouldExist)
	ctx.Step(`^the foreign key "([^"]*)" should exist on "([^"]*)"$`, pg.theForeignKeyShouldExistOnTable)
	ctx.Step(`^I inspect the constraint$`, pg.iInspectTheConstraint)
	ctx.Step(`^it should reference the "([^"]*)" table on column "([^"]*)" with constraint "([^"]*)"$`, func(table, column, constraint string) error {
		return pg.itShouldReferenceTableOnColumn(table, column, constraint)
	})
	ctx.Step(`^I insert a record into "([^"]*)" linked to "([^"]*)" by "([^"]*)" where "([^"]*)" is "([^"]*)"$`, iInsertARecordIntoLinkedToByWhereIs)
	ctx.Step(`^the record in "([^"]*)" linked to "([^"]*)" by "([^"]*)" where "([^"]*)" is "([^"]*)" should exist$`, theRecordInLinkedToByWhereIsShouldExist)
	ctx.Step(`^the "([^"]*)" table has a unique constraint on "([^"]*)"$`, pg.theTableHasUniqueConstraintOnColumn)
	ctx.Step(`^the "([^"]*)\.([^"]*)" column is NOT NULL$`, pg.theColumnIsNotNull)
	ctx.Step(`^the "([^"]*)\.([^"]*)" column has a default value of "([^"]*)"$`, pg.theColumnHasDefaultValue)
	ctx.Step(`^the "([^"]*)\.([^"]*)" column is of type "([^"]*)"$`, pg.theColumnIsOfType)
	ctx.Step(`^the foreign key "([^"]*)" has ON DELETE CASCADE behavior$`, pg.theForeignKeyHasOnDeleteCascade)
	ctx.Step(`^I rollback the transaction$`, pg.iRollbackTheTransaction)
	ctx.Step(`^I have a valid connection to the database$`, iHaveAValidConnectionToTheDatabase)
	ctx.Step(`^the user should still not exist in the database$`, theUserShouldStillNotExistInTheDatabase)
}
