package main

import (
	"os"
	"testing"

	"github.com/Matrics-io/platform-tests/features/steps"
	"github.com/cucumber/godog"
)

func InitializeScenario(ctx *godog.ScenarioContext) {
	steps.RegisterAPISteps(ctx)
	steps.RegisterDBSteps(ctx)
	steps.RegisterMQSteps(ctx)
}

func TestFeatures(t *testing.T) {
	tag := os.Getenv("GODOG_TAGS")
	opts := godog.Options{
		Format: "pretty",
		Paths:  []string{"features"},
		Tags:   tag,
	}
	status := godog.TestSuite{
		Name:                "godogs",
		ScenarioInitializer: InitializeScenario,
		Options:             &opts,
	}.Run()

	if status != 0 {
		t.Fatalf("non-zero status returned: %d", status)
	}
}
