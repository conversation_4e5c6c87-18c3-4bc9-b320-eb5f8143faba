import { Given, When, Then } from '@cucumber/cucumber';
import { Client, QueryResult } from 'pg';
import { strict as assert } from 'assert';
import { DataTable } from '@cucumber/cucumber';

// Define interface for dataTable row
interface TableRow {
  table_name?: string;
  index_name?: string;
  column_name?: string;
  data_type?: string;
  is_nullable?: string;
}

// State for DB tests
interface DBState {
  client?: Client;
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
  queryResult?: QueryResult;
  connectionResult?: boolean;
}

const dbState: DBState = {
    host: '',
    port: 0,
    user: '',
    password: '',
    database: ''
  };
  

// DB Steps
Given('a PostgreSQL database is running', function () {
  // This is a precondition that the PostgreSQL server is running
  // No implementation needed as it's assumed to be true for the test to run
});

Given('I have a valid connection to the database', async function () {
  dbState.client = new Client({
    host: dbState.host,
    port: dbState.port,
    user: dbState.user,
    password: dbState.password,
    database: dbState.database
  });

  try {
    await dbState.client.connect();
    dbState.connectionResult = true;
  } catch (error) {
    dbState.connectionResult = false;
    throw new Error(`Failed to connect to database: ${error}`);
  }
});

Given('I set DB host to {string}', function (host: string) {
  dbState.host = host;
});

Given('I set DB port to {string}', function (port: string) {
  dbState.port = parseInt(port, 10);
});

Given('I set DB user to {string}', function (user: string) {
  dbState.user = user;
});

Given('I set DB password to {string}', function (password: string) {
  dbState.password = password;
});

Given('I set DB name to {string}', function (database: string) {
  dbState.database = database;
});

When('I connect to the database', async function () {
  if (dbState.client) {
    try {
      await dbState.client.end();
    } catch (error) {
      console.error('Error closing existing connection:', error);
    }
  }

  dbState.client = new Client({
    host: dbState.host,
    port: dbState.port,
    user: dbState.user,
    password: dbState.password,
    database: dbState.database
  });

  try {
    await dbState.client.connect();
    dbState.connectionResult = true;
  } catch (error) {
    dbState.connectionResult = false;
    throw new Error(`Failed to connect to database: ${error}`);
  }
});

When('I execute the following SQL:', async function (sql: string) {
  if (!dbState.client) {
    throw new Error('Database client not initialized. Call "I have a valid connection to the database" first');
  }

  try {
    dbState.queryResult = await dbState.client.query(sql);
  } catch (error) {
    throw new Error(`SQL execution failed: ${error}`);
  }
});

When('I insert a record into {string} where {string} is {string}', async function (table: string, column: string, value: string) {
  if (!dbState.client) {
    throw new Error('Database client not initialized');
  }

  try {
    const query = `INSERT INTO ${table} (${column}) VALUES ($1) ON CONFLICT DO NOTHING`;
    await dbState.client.query(query, [value]);
  } catch (error) {
    throw new Error(`Failed to insert record: ${error}`);
  }
});

When('I insert a record into {string} linked to {string} by {string} where {string} is {string}', async function (targetTable: string, sourceTable: string, foreignKey: string, sourceColumn: string, sourceValue: string) {
  if (!dbState.client) {
    throw new Error('Database client not initialized');
  }

  try {
    // First, find the primary key of the source record
    const findQuery = `SELECT id FROM ${sourceTable} WHERE ${sourceColumn} = $1`;
    const result = await dbState.client.query(findQuery, [sourceValue]);
    
    if (result.rows.length === 0) {
      throw new Error(`Source record not found in ${sourceTable} where ${sourceColumn} = ${sourceValue}`);
    }
    
    const sourceId = result.rows[0].id;
    
    // Insert the linked record
    const insertQuery = `INSERT INTO ${targetTable} (${foreignKey}) VALUES ($1)`;
    await dbState.client.query(insertQuery, [sourceId]);
  } catch (error) {
    throw new Error(`Failed to insert linked record: ${error}`);
  }
});

When('I inspect the constraint', async function () {
  // This is a placeholder - the actual implementation depends on specific needs
  // It would typically query information_schema to get constraint details
});

Then('the connection should be successful', function () {
  assert.equal(dbState.connectionResult, true, 'Database connection failed');
});

Then('the following tables should exist:', async function (dataTable: DataTable) {
  if (!dbState.client) {
    throw new Error('Database client not initialized');
  }

  const rows = dataTable.hashes() as TableRow[];
  const tableNames = rows.map(row => row.table_name).filter((name): name is string => name !== undefined);
  
  for (const tableName of tableNames) {
    const query = `
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = $1
      )`;
    
    const result: QueryResult = await dbState.client.query(query, [tableName]);
    assert.equal(result.rows[0].exists, true, `Table ${tableName} does not exist`);
  }
});

Then('the record in {string} where {string} is {string} should exist', async function (table: string, column: string, value: string) {
  if (!dbState.client) {
    throw new Error('Database client not initialized');
  }

  const query = `SELECT * FROM ${table} WHERE ${column} = $1`;
  const result = await dbState.client.query(query, [value]);
  
  assert.ok(result.rows.length > 0, `Record not found in ${table} where ${column} = ${value}`);
});

Then('the record in {string} linked to {string} by {string} where {string} is {string} should exist', async function (targetTable: string, sourceTable: string, foreignKey: string, sourceColumn: string, sourceValue: string) {
  if (!dbState.client) {
    throw new Error('Database client not initialized');
  }

  // First, find the source record ID
  const findSourceQuery = `SELECT id FROM ${sourceTable} WHERE ${sourceColumn} = $1`;
  const sourceResult = await dbState.client.query(findSourceQuery, [sourceValue]);
  
  if (sourceResult.rows.length === 0) {
    throw new Error(`Source record not found in ${sourceTable} where ${sourceColumn} = ${sourceValue}`);
  }
  
  const sourceId = sourceResult.rows[0].id;
  
  // Check if there's a linked record
  const findLinkedQuery = `SELECT * FROM ${targetTable} WHERE ${foreignKey} = $1`;
  const linkedResult = await dbState.client.query(findLinkedQuery, [sourceId]);
  
  assert.ok(linkedResult.rows.length > 0, 
    `Linked record not found in ${targetTable} where ${foreignKey} = ${sourceId}`);
});

Then('the following columns should exist in {string}:', async function (tableName: string, dataTable: DataTable) {
  if (!dbState.client) {
    throw new Error('Database client not initialized');
  }

  const columns = dataTable.hashes() as TableRow[];
  
  for (const column of columns) {
    const query = `
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = $1
      AND column_name = $2
    `;
    
    const result: QueryResult = await dbState.client.query(query, [tableName, column.column_name]);
    
    assert.ok(result.rows.length > 0, `Column ${column.column_name} does not exist in table ${tableName}`);
    
    if (column.data_type) {
      assert.ok(
        result.rows[0].data_type.includes(column.data_type.toLowerCase()),
        `Column ${column.column_name} has wrong data type: expected ${column.data_type}, got ${result.rows[0].data_type}`
      );
    }
    
    if (column.is_nullable) {
      assert.equal(
        result.rows[0].is_nullable, 
        column.is_nullable,
        `Column ${column.column_name} has wrong nullable property: expected ${column.is_nullable}, got ${result.rows[0].is_nullable}`
      );
    }
  }
});

Then('the following indexes should exist on {string}:', async function (tableName: string, dataTable: DataTable) {
  if (!dbState.client) {
    throw new Error('Database client not initialized');
  }

  const rows = dataTable.hashes() as TableRow[];
  const indexes = rows.map(row => row.index_name).filter((name): name is string => name !== undefined);
  
  for (const indexName of indexes) {
    const query = `
      SELECT indexname
      FROM pg_indexes
      WHERE schemaname = 'public'
      AND tablename = $1
      AND indexname = $2
    `;
    
    const result = await dbState.client.query(query, [tableName, indexName]);
    assert.ok(result.rows.length > 0, `Index ${indexName} does not exist on table ${tableName}`);
  }
});

Given('the foreign key {string} should exist on {string}', async function (constraintName: string, tableName: string) {
  if (!dbState.client) {
    throw new Error('Database client not initialized');
  }

  const query = `
    SELECT constraint_name
    FROM information_schema.table_constraints
    WHERE table_schema = 'public'
    AND table_name = $1
    AND constraint_name = $2
    AND constraint_type = 'FOREIGN KEY'
  `;
  
  const result = await dbState.client.query(query, [tableName, constraintName]);
  assert.ok(result.rows.length > 0, `Foreign key ${constraintName} does not exist on table ${tableName}`);
});

Then('it should reference the {string} table on column {string} with constraint {string}', async function (referencedTable: string, referencedColumn: string, constraintName: string) {
    if (!dbState.client) {
      throw new Error('Database client not initialized');
    }
  
    const query = `
      SELECT
        tc.constraint_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM
        information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
      WHERE
        tc.constraint_type = 'FOREIGN KEY'
        AND tc.constraint_name = $1
    `;
  
    const result = await dbState.client.query(query, [constraintName]);
  
    assert.ok(result.rows.length > 0, `Foreign key constraint ${constraintName} not found`);
    const row = result.rows[0];
  
    assert.equal(row.foreign_table_name, referencedTable, `Expected foreign table to be ${referencedTable} but got ${row.foreign_table_name}`);
    assert.equal(row.foreign_column_name, referencedColumn, `Expected foreign column to be ${referencedColumn} but got ${row.foreign_column_name}`);
  });
  

Given('the {string} table has a unique constraint on {string}', async function (tableName: string, columnName: string) {
  if (!dbState.client) {
    throw new Error('Database client not initialized');
  }

  const query = `
    SELECT tc.constraint_name
    FROM information_schema.table_constraints tc
    JOIN information_schema.constraint_column_usage ccu
      ON tc.constraint_name = ccu.constraint_name
    WHERE tc.table_schema = 'public'
    AND tc.table_name = $1
    AND ccu.column_name = $2
    AND tc.constraint_type IN ('UNIQUE', 'PRIMARY KEY')
  `;
  
  const result = await dbState.client.query(query, [tableName, columnName]);
  assert.ok(result.rows.length > 0, `Table ${tableName} does not have a unique constraint on column ${columnName}`);
});

Given('the {string} column is NOT NULL', async function (columnSpec: string) {
  if (!dbState.client) {
    throw new Error('Database client not initialized');
  }

  const [tableName, columnName] = columnSpec.split('.');
  
  const query = `
    SELECT is_nullable
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = $1
    AND column_name = $2
  `;
  
  const result = await dbState.client.query(query, [tableName, columnName]);
  
  assert.ok(result.rows.length > 0, `Column ${columnName} does not exist in table ${tableName}`);
  assert.equal(result.rows[0].is_nullable, 'NO', `Column ${columnName} in table ${tableName} is nullable`);
});

Given('the {string} column has a default value of {string}', async function (columnSpec: string, defaultValue: string) {
  if (!dbState.client) {
    throw new Error('Database client not initialized');
  }

  const [tableName, columnName] = columnSpec.split('.');
  
  const query = `
    SELECT column_default
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = $1
    AND column_name = $2
  `;
  
  const result = await dbState.client.query(query, [tableName, columnName]);
  
  assert.ok(result.rows.length > 0, `Column ${columnName} does not exist in table ${tableName}`);
  assert.ok(
    result.rows[0].column_default && result.rows[0].column_default.includes(defaultValue),
    `Column ${columnName} in table ${tableName} has wrong default value: expected ${defaultValue}, got ${result.rows[0].column_default}`
  );
});

Given('the {string} column is of type {string}', async function (columnSpec: string, dataType: string) {
  if (!dbState.client) {
    throw new Error('Database client not initialized');
  }

  const [tableName, columnName] = columnSpec.split('.');
  
  const query = `
    SELECT data_type, udt_name
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = $1
    AND column_name = $2
  `;
  
  const result = await dbState.client.query(query, [tableName, columnName]);
  
  assert.ok(result.rows.length > 0, `Column ${columnName} does not exist in table ${tableName}`);
  
  // For "serial" type, we check if it's an integer with a sequence
  if (dataType.toLowerCase() === 'serial') {
    assert.equal(result.rows[0].data_type, 'integer', 
      `Column ${columnName} is not an integer for serial type`);
    
    // Also check if there's a sequence for this column
    const sequenceQuery = `
      SELECT pg_get_serial_sequence($1, $2) IS NOT NULL as has_sequence
    `;
    const sequenceResult = await dbState.client.query(sequenceQuery, [tableName, columnName]);
    assert.ok(sequenceResult.rows[0].has_sequence, 
      `Column ${columnName} does not have a sequence attached (not a serial type)`);
  } else {
    assert.ok(
      result.rows[0].data_type === dataType.toLowerCase() || result.rows[0].udt_name === dataType.toLowerCase(),
      `Column ${columnName} has wrong data type: expected ${dataType}, got ${result.rows[0].data_type}`
    );
  }
});

Given('the foreign key {string} has ON DELETE CASCADE behavior', async function (constraintName: string) {
  if (!dbState.client) {
    throw new Error('Database client not initialized');
  }

  const query = `
    SELECT delete_rule
    FROM information_schema.referential_constraints
    WHERE constraint_name = $1
  `;
  
  const result = await dbState.client.query(query, [constraintName]);
  
  assert.ok(result.rows.length > 0, `Constraint ${constraintName} not found`);
  assert.equal(result.rows[0].delete_rule, 'CASCADE', 
    `Constraint ${constraintName} doesn't have CASCADE delete rule, found: ${result.rows[0].delete_rule}`);
}); 