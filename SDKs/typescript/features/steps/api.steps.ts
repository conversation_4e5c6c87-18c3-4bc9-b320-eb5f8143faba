import { Given, When, Then } from '@cucumber/cucumber';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { strict as assert } from 'assert';

// State to be shared between steps
interface WorldState {
  baseUrl: string;
  response?: AxiosResponse;
  responseTime?: number;
  authToken?: string;
}

const state: WorldState = { baseUrl: '' };

// API Steps
Given('the API base URL is set to {string}', function (url: string) {
  state.baseUrl = url;
});

When('I send a {word} request to {string}', async function (method: string, endpoint: string) {
  const startTime = Date.now();
  const config: AxiosRequestConfig = { method: method.toLowerCase() };
  
  try {
    state.response = await axios(`${state.baseUrl}${endpoint}`, config);
  } catch (error: any) {
    if (error.response) {
      state.response = error.response;
    } else {
      throw error;
    }
  }
  
  state.responseTime = Date.now() - startTime;
});

When('I send a {word} request to {string} with body:', async function (method: string, endpoint: string, body: string) {
    const startTime = Date.now();
  
    let parsedBody;
    try {
      parsedBody = JSON.parse(body);
    } catch (e) {
      console.error('Invalid JSON body:', body);
      throw new Error('The request body is not valid JSON.');
    }
  
    try {
      state.response = await axios({
        url: `${state.baseUrl}${endpoint}`,
        method: method.toLowerCase(),
        headers: {
          'Content-Type': 'application/json'
        },
        data: parsedBody
      });
    } catch (error: any) {
      if (error.response) {
        state.response = error.response;
      } else {
        throw error;
      }
    }
  
    state.responseTime = Date.now() - startTime;
  });
  
  

When('I send a {word} request to {string} with the same data multiple times', async function (method: string, endpoint: string) {
  // Implementation for idempotency test
  const data = { test: 'data' };
  const config: AxiosRequestConfig = { 
    method: method.toLowerCase(),
    headers: { 'Content-Type': 'application/json' }
  };
  
  try {
    // Send the same request multiple times
    await axios(`${state.baseUrl}${endpoint}`, { ...config, data });
    await axios(`${state.baseUrl}${endpoint}`, { ...config, data });
    state.response = await axios(`${state.baseUrl}${endpoint}`, { ...config, data });
  } catch (error: any) {
    if (error.response) {
      state.response = error.response;
    } else {
      throw error;
    }
  }
});

When('I send a {word} request to {string} multiple times', async function (method: string, endpoint: string) {
  const config: AxiosRequestConfig = { method: method.toLowerCase() };
  
  try {
    // Send the same request multiple times
    await axios(`${state.baseUrl}${endpoint}`, config);
    await axios(`${state.baseUrl}${endpoint}`, config);
    state.response = await axios(`${state.baseUrl}${endpoint}`, config);
  } catch (error: any) {
    if (error.response) {
      state.response = error.response;
    } else {
      throw error;
    }
  }
});

When('I send a {word} request to {string} without authorization', async function (method: string, endpoint: string) {
  const config: AxiosRequestConfig = { method: method.toLowerCase() };
  
  try {
    state.response = await axios(`${state.baseUrl}${endpoint}`, config);
  } catch (error: any) {
    if (error.response) {
      state.response = error.response;
    } else {
      throw error;
    }
  }
});

Given('the auth token is set to {string}', function (token: string) {
  state.authToken = token;
});

Given('the expired token is set to {string}', function (token: string) {
  state.authToken = token;
});


When('I send a {word} request to {string} with valid token', async function (method: string, endpoint: string) {
  const config: AxiosRequestConfig = { 
    method: method.toLowerCase(),
    headers: { 'Authorization': `Bearer ${state.authToken}` }
  };
  
  try {
    state.response = await axios(`${state.baseUrl}${endpoint}`, config);
  } catch (error: any) {
    if (error.response) {
      state.response = error.response;
    } else {
      throw error;
    }
  }
});

When('I send a {word} request to {string} with expired token', async function (method: string, endpoint: string) {
  const config: AxiosRequestConfig = { 
    method: method.toLowerCase(),
    headers: { 'Authorization': `Bearer ${state.authToken}` }
  };
  
  try {
    state.response = await axios(`${state.baseUrl}${endpoint}`, config);
  } catch (error: any) {
    if (error.response) {
      state.response = error.response;
    } else {
      throw error;
    }
  }
});

Then('the response status should be {int}', function (expected: number) {

  assert.equal(state.response?.status, expected);
});

Then('the response time should be less than {int} milliseconds', function (maxTime: number) {
  assert.ok(state.responseTime && state.responseTime < maxTime, 
    `Response time ${state.responseTime}ms exceeds the maximum of ${maxTime}ms`);
});

Then('the response should contain {string}', function (fields: string) {
  const fieldList = fields.split(/,\s*/);
  const responseData = state.response?.data;
  
  for (const field of fieldList) {
    assert.ok(responseData && field.trim() in responseData, 
      `Response does not contain expected field: ${field}`);
  }
});

Then('the field {string} should be of type {word}', function (field: string, type: string) {
  const responseData = state.response?.data;
  assert.ok(responseData && field in responseData, `Response does not contain field: ${field}`);
  
  const value = responseData[field];
  
  switch (type) {
    case 'string':
      assert.equal(typeof value, 'string');
      break;
    case 'integer':
    case 'number':
      assert.equal(typeof value, 'number');
      break;
    case 'boolean':
      assert.equal(typeof value, 'boolean');
      break;
    case 'array':
      assert.ok(Array.isArray(value));
      break;
    case 'object':
      assert.equal(typeof value, 'object');
      assert.ok(value !== null);
      assert.ok(!Array.isArray(value));
      break;
    default:
      throw new Error(`Unsupported type check: ${type}`);
  }
});

Then('the response header {string} should be present', function (header: string) {
  const headers = state.response?.headers || {};
  assert.ok(header.toLowerCase() in headers, `Header ${header} is not present in the response`);
});

Then('the response header {string} should be {string}', function (header: string, value: string) {
  const headers = state.response?.headers || {};
  assert.equal(headers[header.toLowerCase()], value);
}); 