#!/usr/bin/env node

import * as fs from 'fs';
import * as path from 'path';
import * as process from 'process';
import { execSync } from 'child_process';
import { program } from 'commander';

// Add the current directory to NODE_PATH to ensure imports work
process.env.NODE_PATH = path.join(process.env.NODE_PATH || '', path.dirname(__filename));

/**
 * Run the cucumber tests using Cucumber.js
 */
function runCucumberTests(tags?: string): number {
  try {
    // Get tags from argument or environment variables
    if (!tags) {
      tags = process.env.CUCUMBER_TAGS || '';
    }
    
    // Exit if no tags are specified
    if (!tags) {
      console.error("Error: No tags specified. Please provide tags using --tags argument or CUCUMBER_TAGS environment variable.");
      return 1;
    }

    const args: string[] = ['--format', 'progress'];

    console.log(`Filtering tests with tags: ${tags}`);
    args.push(`--tags=${tags}`);
    
    // Include feature files
    args.push('features/**/*.feature');

    // Build the cucumber.js command
    const command = `npx cucumber-js ${args.join(' ')}`;
    console.log("Running Cucumber with command:", command);
    
    // Execute cucumber.js and return its exit code
    execSync(command, { stdio: 'inherit' });
    return 0;
  } catch (error) {
    console.error('Error running cucumber tests:', error);
    return 1;
  }
}

// Parse command line arguments
program
  .description('Run cucumber tests with Cucumber.js')
  .option('--tags <tags>', 'Tags to filter tests (e.g. "@api and not @db")')
  .action((options: { tags?: string }) => {
    const exitCode = runCucumberTests(options.tags);
    process.exit(exitCode);
  });

program.parse(process.argv); 