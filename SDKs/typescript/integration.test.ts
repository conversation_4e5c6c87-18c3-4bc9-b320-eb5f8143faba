import { 
  createBrickConfig, 
  createNetwork, 
  GenericContainerBrick
} from "./testcontainers";
import Docker from "dockerode";
import path from "path";

// Extend the Jest timeout for this integration test
jest.setTimeout(300000); // 5 minutes

// Helper functions
const getTestFilePath = (relPath: string): string => {
  return path.resolve(__dirname, relPath);
};

const sleep = (ms: number) => new Promise(res => setTimeout(res, ms));

// Main integration test
test("Docker Compose Integration Test", async () => {
  // Compute absolute paths
  const initSql = getTestFilePath("../../services/gin-go-demo/init.sql");
  const componentsPath = getTestFilePath("../../components");
  const loadTestScript = getTestFilePath("../../tests/performance/scripts/load-test.js");
  const reportsPath = getTestFilePath("../../tests/performance/reports");

  // Create a network
  const networkName = "platform-tests-bridge";
  try {
    await createNetwork({ name: networkName });
    console.log(`Created network: ${networkName}`);
  } catch (networkError) {
    console.error("Failed to create network:", networkError);
    throw networkError;
  }

  // 1. Postgres container
  const postgres = createBrickConfig({
    brickType: "postgres",
    instanceName: "postgres-db",
    commandArguments: [],
    environmentVariables: {
      POSTGRES_USER: "postgres",
      POSTGRES_PASSWORD: "postgres",
      POSTGRES_DB: "demo_db",
    },
    exposedPort: "5432/tcp",
    hostPort: "5433",
    volumes: [{ 
      hostPath: initSql, 
      containerPath: "/docker-entrypoint-initdb.d/init.sql" 
    }],
    healthCheck: {
      test: ["pg_isready", "-U", "postgres", "-d", "demo_db"],
      interval: 5,
      retries: 10,
      startPeriod: 5,
      timeout: 5
    },
    networks: [networkName],
    networkAliases: { "platform-tests-bridge": ["db"] }
  });

  // 2. Gin-Go container
  const ginGo = createBrickConfig({
    brickType: "gin-go",
    instanceName: "gin-go",
    commandArguments: [],
    environmentVariables: {
      DB_HOST: "postgres-db", 
      DB_PORT: "5432",
      DB_USER: "postgres",
      DB_PASSWORD: "postgres",
      DB_NAME: "demo_db",
      JWT_SECRET: "supersecretkey",
      DAPR_BASE_URL: "http://dapr-gin:3500/v1.0",
    },
    exposedPort: "8081/tcp",
    hostPort: "8081",
    networks: [networkName]
  });

  // 3. Fibonacci Calculator container
  const fibCalc = createBrickConfig({
    brickType: "fib-calc",
    instanceName: "fib-calc",
    commandArguments: [],
    environmentVariables: {
      DB_HOST: "postgres-db",
      DB_PORT: "5432",
      DB_USER: "postgres",
      DB_PASSWORD: "postgres",
      DB_NAME: "demo_db",
      FIB_CALC_URL: "http://fib-calc-service:8082",
      DAPR_BASE_URL: "http://dapr-fib:3501/v1.0/invoke",
    },
    exposedPort: "8082/tcp", 
    hostPort: "8082",
    networks: [networkName]
  });

  // 4. Dapr sidecar for Gin-Go
  const daprGin = createBrickConfig({
    brickType: "dapr",
    instanceName: "dapr-gin",
    commandArguments: [
      "/daprd",
      "--app-id", "gin-go-demo",
      "--app-channel-address", "gin-go",
      "--app-port", "8081",
      "--dapr-http-port", "3500",
      "--resources-path", "/app/.dapr/components",
    ],
    environmentVariables: {},
    exposedPort: "3500/tcp",
    hostPort: "3500",
    volumes: [{ 
      hostPath: componentsPath, 
      containerPath: "/app/.dapr/components" 
    }],
    networks: [networkName]
  });

  // 5. Dapr sidecar for Fib-Calc
  const daprFib = createBrickConfig({
    brickType: "dapr",
    instanceName: "dapr-fib",
    commandArguments: [
      "/daprd",
      "--app-id", "fib-calc",
      "--app-channel-address", "fib-calc",
      "--app-port", "8082",
      "--dapr-http-port", "3502",
      "--dapr-grpc-port", "50001",
      "--resources-path", "/app/.dapr/components"
    ],
    environmentVariables: {},
    exposedPort: "3502/tcp",
    hostPort: "3502",
    volumes: [{ 
      hostPath: componentsPath, 
      containerPath: "/app/.dapr/components" 
    }],
    networks: [networkName]
  });

  // 6. k6 load testing container
  const k6Gin = createBrickConfig({
    brickType: "k6-gin",
    instanceName: "k6-gin",
    commandArguments: ["run", "/src/load-test.js"],
    environmentVariables: { 
      BASE_URL: "http://dapr-gin:3500/v1.0/invoke/gin-go-demo/method" 
    },
    exposedPort: "8080/tcp",
    volumes: [
      { hostPath: loadTestScript, containerPath: "/src/load-test.js" },
      { hostPath: reportsPath, containerPath: "/src/reports" },
    ],
    healthCheck: {
      test: ["CMD-SHELL", "exit 0"],
      interval: 5,
      retries: 5,
      startPeriod: 2,
      timeout: 5
    },
    networks: [networkName]
  });

  // Container configurations in order
  const containers = [postgres, ginGo, fibCalc, daprGin, daprFib, k6Gin];
  const bricks: GenericContainerBrick[] = [];

  try {
    // Start each container sequentially
    for (const config of containers) {
      console.log(`Starting container: ${config.instanceName}`);
      const brick = new GenericContainerBrick(config);
      await brick.start();
      bricks.push(brick);
      
      // Give a moment for container to stabilize
      if (config.instanceName === "postgres-db") {
        console.log("Waiting for Postgres to stabilize...");
        await sleep(5000);
      } else {
        await sleep(2000);
      }
    }
    console.log("All containers started successfully");

    // Monitor k6 container
    const dockerClient = new Docker();
    const k6Container = dockerClient.getContainer("k6-gin");
    console.log("Monitoring k6-gin container...");
    
    while (true) {
      const data = await k6Container.inspect();
      if (!data.State.Running) {
        console.log("k6 container has stopped.");
        break;
      }
      console.log("Waiting for k6 to finish...");
      await sleep(10000);
    }
  } catch (err) {
    console.error("Test failed:", err);
    throw err;
  } finally {
    // Cleanup - stop all containers
    console.log("Tearing down all containers...");
    for (const brick of bricks) {
      try {
        await brick.stop();
      } catch (stopErr) {
        console.error("Failed to stop container:", stopErr);
      }
    }
  }
}); 