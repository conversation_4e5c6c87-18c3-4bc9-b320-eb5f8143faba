// harness_service.ts

import { Pool } from "pg";
import { Redis as RedisClient } from "ioredis";

export interface User {
  id: string;
  name: string;
  email: string;
}

export interface Context {
  correlationId?: string;
}

export interface Logger {
  info(message: string, meta?: Record<string, any>): void;
  warn(message: string, meta?: Record<string, any>): void;
  error(message: string, meta?: Record<string, any>): void;
}

export class UserService {
  private pool: Pool;
  private redis: RedisClient;
  private smtpSender: (from: string, to: string[], subject: string, body: string) => Promise<void>;
  private logger: Logger;
  private cacheTTL = 600; // seconds

  constructor(
    pool: Pool,
    redisClient: RedisClient,
    smtpSender: (from: string, to: string[], subject: string, body: string) => Promise<void>,
    logger: Logger
  ) {
    this.pool = pool;
    this.redis    = redisClient;
    this.smtpSender = smtpSender;
    this.logger   = logger;
  }

  public async getUser(ctx: Context, userId: string): Promise<User | null> {
    const corr = ctx.correlationId ?? "";
    this.logger.info("Getting user", { userId, correlationId: corr });

    const cacheKey = `user:${userId}`;
    try {
      const cached = await this.redis.get(cacheKey);
      if (cached) {
        this.logger.info("User found in cache", { userId, correlationId: corr });
        return JSON.parse(cached) as User;
      }
    } catch (err) {
      this.logger.error("Redis get error", { error: err, correlationId: corr });
      throw err;
    }

    const res = await this.pool.query<User>(
      "SELECT id, name, email FROM users WHERE id = $1",
      [userId]
    );
    if (res.rowCount === 0) {
      this.logger.warn("User not found", { userId, correlationId: corr });
      return null;
    }
    const user = res.rows[0];

    try {
      await this.redis.setex(cacheKey, this.cacheTTL, JSON.stringify(user));
      this.logger.info("User cached", { userId, correlationId: corr });
    } catch (err) {
      this.logger.error("Failed to cache user", { error: err, correlationId: corr });
    }

    return user;
  }

  public async sendWelcomeEmail(ctx: Context, user: User): Promise<void> {
    const corr = ctx.correlationId ?? "";
    this.logger.info("Sending welcome email", { email: user.email, correlationId: corr });

    const from    = "<EMAIL>";
    const to      = [user.email];
    const subject = "Welcome to our service";
    const body    = `Hello ${user.name}, welcome to our service!`;

    try {
      await this.smtpSender(from, to, subject, body);
      this.logger.info("Welcome email sent", { email: user.email, correlationId: corr });
    } catch (err) {
      this.logger.error("Failed to send welcome email", { error: err, correlationId: corr });
      throw err;
    }
  }
}
