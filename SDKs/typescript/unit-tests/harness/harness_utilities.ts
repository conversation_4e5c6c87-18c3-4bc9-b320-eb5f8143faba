import { Pool } from 'pg';
import Redis from 'ioredis';
import Dock<PERSON> from 'dockerode';
import { BrickConfig, createBrickConfig } from '../../testcontainers/brick_config';
import { GenericContainerBrick } from '../../testcontainers/generic_container_brick';
import { PortStrategy } from '../../testcontainers/wait';
import { logger } from '../../testcontainers/utils/logger';

// Alias for readability
export type RedisClient = Redis;

/**
 * FakeLogger provides a simple logger implementation for testing.
 */
export class FakeLogger {
  logs: Array<{level: string, message: string, meta?: Record<string, any>}> = [];

  info(message: string, meta?: Record<string, any>): void {
    this.logs.push({ level: 'info', message, meta });
    console.log(`[INFO] ${message}`, meta);
  }

  warn(message: string, meta?: Record<string, any>): void {
    this.logs.push({ level: 'warn', message, meta });
    console.warn(`[WARN] ${message}`, meta);
  }

  error(message: string, meta?: Record<string, any>): void {
    this.logs.push({ level: 'error', message, meta });
    console.error(`[ERROR] ${message}`, meta);
  }

  clear(): void {
    this.logs = [];
  }
}

/**
 * FakeSmtpSender provides a mock SMTP sender for testing.
 */
export class FakeSmtpSender {
  sentEmails: Array<{from: string, to: string[], subject: string, body: string}> = [];

  async sendEmail(from: string, to: string[], subject: string, body: string): Promise<void> {
    this.sentEmails.push({ from, to, subject, body });
    return Promise.resolve();
  }

  getLastEmail() {
    return this.sentEmails[this.sentEmails.length - 1] || null;
  }

  clear(): void {
    this.sentEmails = [];
  }
}

// Helper function to check if Docker is available
async function isDockerAvailable(): Promise<boolean> {
  try {
    const docker = new Docker();
    await docker.ping();
    return true;
  } catch (err) {
    logger.error('Docker is not available:', err);
    return false;
  }
}

/**
 * FakePostgres uses a containerized PostgreSQL instance for testing.
 * Falls back to localhost connection if Docker is not available.
 */
export class FakePostgres {
  private brick?: GenericContainerBrick;
  private pool!: Pool;
  private useDockerContainer: boolean = true;
  private hostPort: string = "35432"; // Use a non-default port by default

  constructor(useDockerContainer = true, hostPort?: string) {
    this.useDockerContainer = useDockerContainer;
    if (hostPort) {
      this.hostPort = hostPort;
    }
    
    if (useDockerContainer) {
      const config = createBrickConfig({
        brickType: 'postgres',
        instanceName: 'fake-postgres-test',
        commandArguments: [],
        environmentVariables: {
          POSTGRES_USER: 'postgres',
          POSTGRES_PASSWORD: 'postgres',
          POSTGRES_DB: 'testdb',
        },
        exposedPort: '5432/tcp',
        hostPort: this.hostPort,
        healthCheck: {
          test: ["CMD", "pg_isready", "-U", "postgres"],
          interval: 1000,
          retries: 5,
          startPeriod: 2000,
          timeout: 1000
        }
      });
      
      this.brick = new GenericContainerBrick(config);
    }
  }

  /** Starts the container and initializes the pg Pool. */
  public async initialize(): Promise<void> {
    let host = 'localhost';
    let port = parseInt(this.hostPort);
    
    if (this.useDockerContainer) {
      try {
        const dockerAvailable = await isDockerAvailable();
        if (!dockerAvailable) {
          logger.warn('Docker not available, falling back to localhost Postgres');
          this.useDockerContainer = false;
        } else {
          if (!this.brick) {
            throw new Error('Brick not initialized');
          }
          
          logger.info('Starting Postgres container...');
          await this.brick.start();
          logger.info('Postgres container started');
          
          const container = this.brick.getContainer();
          if (!container) {
            throw new Error('Container failed to start');
          }
          
          const containerInfo = await container.inspect();
          
          // Get the mapped port from the container - most reliable approach
          const portMappings = containerInfo.NetworkSettings.Ports;
          const portKey = Object.keys(portMappings || {}).find(key => key.startsWith('5432'));
          if (portKey && portMappings[portKey] && portMappings[portKey][0]) {
            port = parseInt(portMappings[portKey][0].HostPort);
          }
          
          logger.info(`Postgres running on ${host}:${port}`);
        }
      } catch (err) {
        logger.error('Error starting Postgres container:', err);
        logger.warn('Falling back to localhost Postgres');
        this.useDockerContainer = false;
        host = 'localhost';
        port = 5432; // Default PostgreSQL port
      }
    }

    logger.info(`Connecting to Postgres at ${host}:${port}`);
    this.pool = new Pool({
      host,
      port,
      user: 'postgres',
      password: 'postgres',
      database: 'testdb',
    });

    // Ensure the database is ready
    let connected = false;
    for (let i = 0; i < 10; i++) {
      try {
        logger.info(`Attempt ${i+1}/10 to connect to Postgres...`);
        await this.pool.query('SELECT 1');
        logger.info('Successfully connected to Postgres');
        connected = true;
        break;
      } catch (err) {
        logger.warn(`Connection attempt ${i+1} failed:`, err);
        await new Promise(res => setTimeout(res, 1000));
      }
    }
    
    if (!connected) {
      throw new Error('Could not connect to Postgres after multiple attempts');
    }
  }

  /** Sets up database schema for testing */
  public async setupSchema(): Promise<void> {
    logger.info('Setting up database schema...');
    try {
      await this.pool.query(`
        CREATE TABLE IF NOT EXISTS users (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          email TEXT NOT NULL UNIQUE
        )
      `);
      logger.info('Schema setup complete');
    } catch (err) {
      logger.error('Error setting up schema:', err);
      throw err;
    }
  }

  /** Inserts test data */
  public async seedTestData(): Promise<void> {
    logger.info('Seeding test data...');
    try {
      await this.pool.query(`
        INSERT INTO users (id, name, email)
        VALUES 
          ('user1', 'Test User 1', '<EMAIL>'),
          ('user2', 'Test User 2', '<EMAIL>')
        ON CONFLICT (id) DO NOTHING
      `);
      logger.info('Test data seeded successfully');
    } catch (err) {
      logger.error('Error seeding test data:', err);
      throw err;
    }
  }

  /** Returns the initialized pg Pool. */
  public getPool(): Pool {
    if (!this.pool) throw new Error('FakePostgres not initialized');
    return this.pool;
  }

  /** Stops the container and closes the pool. */
  public async cleanup(): Promise<void> {
    logger.info('Cleaning up Postgres resources...');
    
    if (this.pool) {
      try {
        await this.pool.end();
        logger.info('Postgres pool closed');
      } catch (err) {
        logger.error('Error closing Postgres pool:', err);
      }
    }
    
    if (this.useDockerContainer && this.brick) {
      try {
        await this.brick.stop();
        logger.info('Postgres container stopped');
      } catch (err) {
        logger.error('Error stopping Postgres container:', err);
      }
    }
  }
}

/**
 * FakeRedis uses a containerized Redis instance for testing.
 * Falls back to localhost connection if Docker is not available.
 */
export class FakeRedis {
  private brick?: GenericContainerBrick;
  private client!: RedisClient;
  private hitCount: Record<string, number> = {};
  private useDockerContainer: boolean = true;
  private hostPort: string = "36379"; // Use a non-default port by default

  constructor(useDockerContainer = true, hostPort?: string) {
    this.useDockerContainer = useDockerContainer;
    if (hostPort) {
      this.hostPort = hostPort;
    }
    
    if (useDockerContainer) {
      const config = createBrickConfig({
        brickType: 'dapr-redis',
        instanceName: 'fake-redis-test',
        commandArguments: [],
        environmentVariables: {},
        exposedPort: '6379/tcp',
        hostPort: this.hostPort
      });
      
      this.brick = new GenericContainerBrick(config);
    }
  }

  /** Starts the container and initializes the Redis client. */
  public async initialize(): Promise<void> {
    let host = 'localhost';
    let port = parseInt(this.hostPort);
    
    if (this.useDockerContainer) {
      try {
        const dockerAvailable = await isDockerAvailable();
        if (!dockerAvailable) {
          logger.warn('Docker not available, falling back to localhost Redis');
          this.useDockerContainer = false;
        } else {
          if (!this.brick) {
            throw new Error('Brick not initialized');
          }
          
          logger.info('Starting Redis container...');
          await this.brick.start();
          logger.info('Redis container started');
          
          const container = this.brick.getContainer();
          if (!container) {
            throw new Error('Container failed to start');
          }
          
          const containerInfo = await container.inspect();
          
          // Get the mapped port from the container - most reliable approach
          const portMappings = containerInfo.NetworkSettings.Ports;
          const portKey = Object.keys(portMappings || {}).find(key => key.startsWith('6379'));
          if (portKey && portMappings[portKey] && portMappings[portKey][0]) {
            port = parseInt(portMappings[portKey][0].HostPort);
          }
          
          logger.info(`Redis running on ${host}:${port}`);
        }
      } catch (err) {
        logger.error('Error starting Redis container:', err);
        logger.warn('Falling back to localhost Redis');
        this.useDockerContainer = false;
        host = 'localhost';
        port = 6379; // Default Redis port
      }
    }

    logger.info(`Connecting to Redis at ${host}:${port}`);
    this.client = new Redis({ 
      host, 
      port,
      maxRetriesPerRequest: 3,
      retryStrategy: (times) => {
        const delay = Math.min(times * 200, 2000);
        return delay;
      }
    });
    
    // Ensure Redis is ready
    let connected = false;
    for (let i = 0; i < 10; i++) {
      try {
        logger.info(`Attempt ${i+1}/10 to connect to Redis...`);
        await this.client.ping();
        logger.info('Successfully connected to Redis');
        connected = true;
        break;
      } catch (err) {
        logger.warn(`Connection attempt ${i+1} failed:`, err);
        await new Promise(res => setTimeout(res, 1000));
      }
    }
    
    if (!connected) {
      throw new Error('Could not connect to Redis after multiple attempts');
    }
  }

  /** Returns the initialized Redis client. */
  public getClient(): RedisClient {
    if (!this.client) throw new Error('FakeRedis not initialized');
    return this.client;
  }

  /** Performs a GET while counting hits per key. */
  public async getWithCounter(key: string): Promise<string | null> {
    this.hitCount[key] = (this.hitCount[key] || 0) + 1;
    return this.client.get(key);
  }

  /** Returns how many times a key was fetched. */
  public getHitCount(key: string): number {
    return this.hitCount[key] || 0;
  }

  /** Clears all keys in Redis */
  public async flushAll(): Promise<void> {
    try {
      await this.client.flushall();
      logger.info('Redis flushed');
    } catch (err) {
      logger.error('Error flushing Redis:', err);
    }
  }

  /** Stops the container and quits the Redis client. */
  public async cleanup(): Promise<void> {
    logger.info('Cleaning up Redis resources...');
    
    if (this.client) {
      try {
        await this.client.quit();
        logger.info('Redis client closed');
      } catch (err) {
        logger.error('Error closing Redis client:', err);
      }
    }
    
    if (this.useDockerContainer && this.brick) {
      try {
        await this.brick.stop();
        logger.info('Redis container stopped');
      } catch (err) {
        logger.error('Error stopping Redis container:', err);
      }
    }
  }
}

/**
 * TestHarness combines all the test dependencies in one easy-to-use class.
 */
export class TestHarness {
  postgres: FakePostgres;
  redis: FakeRedis;
  logger: FakeLogger;
  smtpSender: FakeSmtpSender;
  useDockerContainers: boolean;

  constructor(useDockerContainers = true) {
    this.useDockerContainers = useDockerContainers;
    this.postgres = new FakePostgres(useDockerContainers, "45432"); // Use different ports for the harness
    this.redis = new FakeRedis(useDockerContainers, "46379");       // to avoid conflicts with other tests
    this.logger = new FakeLogger();
    this.smtpSender = new FakeSmtpSender();
  }

  /**
   * Initialize all test dependencies
   */
  async initialize(): Promise<void> {
    logger.info('Initializing test harness...');
    try {
      // Check if Docker is available if needed
      if (this.useDockerContainers) {
        const dockerAvailable = await isDockerAvailable();
        if (!dockerAvailable) {
          logger.warn('Docker not available, will use local connections for all services');
        }
      }
      
      logger.info('Initializing Postgres...');
      await this.postgres.initialize();
      
      logger.info('Initializing Redis...');
      await this.redis.initialize();
      
      logger.info('Setting up database schema...');
      await this.postgres.setupSchema();
      
      logger.info('Test harness initialized successfully');
    } catch (err) {
      logger.error('Error initializing test harness:', err);
      await this.cleanup();
      throw err;
    }
  }

  /**
   * Clean up all test resources
   */
  async cleanup(): Promise<void> {
    logger.info('Cleaning up test harness...');
    
    // Clean up in reverse order
    try {
      await this.redis.cleanup();
    } catch (err) {
      logger.error('Error cleaning up Redis:', err);
    }
    
    try {
      await this.postgres.cleanup();
    } catch (err) {
      logger.error('Error cleaning up Postgres:', err);
    }
    
    logger.info('Test harness cleanup complete');
  }
}