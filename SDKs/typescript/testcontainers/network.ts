import Docker, { NetworkCreateOptions } from 'dockerode';
import { logger } from './utils/logger';

/**
 * Options for creating a Docker network
 */
export interface NetworkOptions {
  /** Name of the network */
  name: string;
  /** Network driver to use (defaults to 'bridge') */
  driver?: string;
  /** Labels to apply to the network */
  labels?: Record<string, string>;
}

/**
 * Creates a Docker network if it doesn't already exist
 * 
 * @param options Network creation options
 * @throws Error if network creation fails
 */
export async function createNetwork(options: NetworkOptions): Promise<void> {
  const { name, driver = 'bridge', labels = {} } = options;
  const docker = new Docker();
  
  try {
    // Check if network already exists
    const networks = await docker.listNetworks();
    const exists = networks.find((net) => net.Name === name);

    if (exists) {
      logger.warn(`Network already exists: ${name}`);
      return;
    }

    // Create network with provided options
    const networkOptions: NetworkCreateOptions = {
      Name: name,
      Driver: driver,
      CheckDuplicate: true,
      Labels: {
        'com.docker.compose.project': 'platform-tests',
        ...labels
      },
    };

    await docker.createNetwork(networkOptions);
    logger.info(`Created network: ${name}`);
  } catch (error) {
    logger.error(`Failed to create network ${name}:`, error);
    throw error;
  }
}

/**
 * Removes a Docker network if it exists
 * 
 * @param name Name of the network to remove
 * @throws Error if network removal fails
 */
export async function removeNetwork(name: string): Promise<void> {
  const docker = new Docker();
  
  try {
    // Check if network exists
    const networks = await docker.listNetworks();
    const network = networks.find((net) => net.Name === name);

    if (!network) {
      logger.warn(`Network does not exist: ${name}`);
      return;
    }

    // Remove the network
    const networkToRemove = docker.getNetwork(network.Id);
    await networkToRemove.remove();
    
    logger.info(`Removed network: ${name}`);
  } catch (error) {
    logger.error(`Failed to remove network ${name}:`, error);
    throw error;
  }
}
