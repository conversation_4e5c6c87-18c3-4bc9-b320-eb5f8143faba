// wait.ts
import { Container } from 'dockerode';
import * as http from 'http';
import * as net from 'net';
import { logger } from './utils/logger';

/**
 * Base configuration for wait strategies
 */
export interface WaitStrategyOptions {
  /** Timeout in milliseconds before failing */
  timeout?: number;
  /** Delay between retries in milliseconds */
  retryInterval?: number;
}

/**
 * Defines a strategy for waiting until a container is ready
 */
export interface Strategy {
  /**
   * Wait until the container is ready according to this strategy
   * @param container The Docker container to wait for
   * @param signal Optional signal to abort the operation
   * @throws Error if the container fails to become ready within the timeout
   */
  waitUntilReady(container: Container, signal?: AbortSignal): Promise<void>;
}

/**
 * Configuration for HTTP wait strategy
 */
export interface HttpStrategyOptions extends WaitStrategyOptions {
  /** Path to check (defaults to '/') */
  path?: string;
  /** Expected HTTP status code (defaults to 200) */
  expectedStatus?: number;
}

/**
 * Wait for a specific HTTP endpoint to return a 2xx status code
 */
export class HttpStrategy implements Strategy {
  private readonly port: string;
  private readonly path: string;
  private readonly expectedStatus: number;
  private readonly timeout: number;
  private readonly retryInterval: number;

  /**
   * @param port Port to check
   * @param options Additional configuration options
   */
  constructor(port: string, options: HttpStrategyOptions = {}) {
    this.port = port;
    this.path = options.path ?? '/';
    this.expectedStatus = options.expectedStatus ?? 200;
    this.timeout = options.timeout ?? 60000;
    this.retryInterval = options.retryInterval ?? 1000;
  }

  async waitUntilReady(container: Container, signal?: AbortSignal): Promise<void> {
    const startTime = Date.now();
    
    // Check if operation was aborted before starting
    if (signal?.aborted) {
      throw new Error('Operation was aborted');
    }
    
    while (true) {
      try {
        const containerInfo = await container.inspect();
        if (!containerInfo.State.Running) {
          throw new Error('Container is not running');
        }
        
        const ip = containerInfo.NetworkSettings.IPAddress || 'localhost';
        logger.debug(`Checking HTTP endpoint at ${ip}:${this.port}${this.path}`);
        
        // Try to connect to the HTTP endpoint
        await this.checkEndpoint(ip);
        return;
      } catch (error) {
        // Check if we've timed out or been aborted
        if (signal?.aborted) {
          throw new Error('Operation was aborted');
        }
        
        if (Date.now() - startTime > this.timeout) {
          throw new Error(`Timeout waiting for HTTP endpoint on port ${this.port}${this.path} (${this.timeout}ms)`);
        }
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, this.retryInterval));
      }
    }
  }
  
  private checkEndpoint(ip: string): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      const req = http.request({
        host: ip,
        port: parseInt(this.port),
        path: this.path,
        method: 'GET',
        timeout: 5000,
      }, (res) => {
        if (this.expectedStatus === res.statusCode) {
          resolve();
        } else {
          reject(new Error(`Expected status ${this.expectedStatus}, got ${res.statusCode}`));
        }
      });
      
      req.on('error', reject);
      req.end();
    });
  }
}

/**
 * Configuration for log wait strategy
 */
export interface LogStrategyOptions extends WaitStrategyOptions {
  /** Maximum number of log lines to fetch */
  maxLines?: number;
}

/**
 * Wait for a log message that matches a specific pattern
 */
export class LogStrategy implements Strategy {
  private readonly pattern: RegExp;
  private readonly timeout: number;
  private readonly retryInterval: number;
  private readonly maxLines: number;

  /**
   * @param pattern Regular expression pattern to match in logs
   * @param options Additional configuration options
   */
  constructor(pattern: RegExp, options: LogStrategyOptions = {}) {
    this.pattern = pattern;
    this.timeout = options.timeout ?? 60000;
    this.retryInterval = options.retryInterval ?? 1000;
    this.maxLines = options.maxLines ?? 50;
  }

  async waitUntilReady(container: Container, signal?: AbortSignal): Promise<void> {
    const startTime = Date.now();
    
    // Check if operation was aborted before starting
    if (signal?.aborted) {
      throw new Error('Operation was aborted');
    }
    
    while (true) {
      try {
        const logs = await container.logs({
          stdout: true,
          stderr: true,
          tail: this.maxLines,
          follow: false
        });
        
        // Convert Buffer to string
        const logsString = logs.toString();
        
        if (this.pattern.test(logsString)) {
          return;
        }
        
        // Check if we've timed out or been aborted
        if (signal?.aborted) {
          throw new Error('Operation was aborted');
        }
        
        if (Date.now() - startTime > this.timeout) {
          throw new Error(`Timeout waiting for log pattern ${this.pattern} (${this.timeout}ms)`);
        }
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, this.retryInterval));
      } catch (error) {
        // If this is an abort error or timeout, rethrow it
        if (signal?.aborted || Date.now() - startTime > this.timeout) {
          throw error;
        }
        
        // For other errors, wait and retry
        await new Promise(resolve => setTimeout(resolve, this.retryInterval));
      }
    }
  }
}

/**
 * Configuration for port wait strategy
 */
export interface PortStrategyOptions extends WaitStrategyOptions {
  /** Connection timeout in milliseconds */
  connectionTimeout?: number;
}

/**
 * Wait for a specific port to be available
 */
export class PortStrategy implements Strategy {
  private readonly port: string;
  private readonly timeout: number;
  private readonly retryInterval: number;
  private readonly connectionTimeout: number;

  /**
   * @param port Port to check
   * @param options Additional configuration options
   */
  constructor(port: string, options: PortStrategyOptions = {}) {
    this.port = port.split('/')[0]; // Remove the protocol part if any (e.g., "6379/tcp" -> "6379")
    this.timeout = options.timeout ?? 60000;
    this.retryInterval = options.retryInterval ?? 1000;
    this.connectionTimeout = options.connectionTimeout ?? 5000;
  }

  async waitUntilReady(container: Container, signal?: AbortSignal): Promise<void> {
    const startTime = Date.now();
    
    // Check if operation was aborted before starting
    if (signal?.aborted) {
      throw new Error('Operation was aborted');
    }

    // Get container info first to determine the host
    const info = await container.inspect();
    if (!info.State.Running) {
      throw new Error('Container is not running');
    }

    // Try different approaches to find the appropriate IP/port
    const containerIp = info.NetworkSettings.IPAddress;
    
    // Get port mappings - this is most reliable for Docker Desktop
    const portMappings = info.NetworkSettings.Ports;
    const portKey = Object.keys(portMappings || {}).find(key => key.startsWith(this.port));
    const hostPort = portKey && portMappings[portKey] && portMappings[portKey][0] ? 
      portMappings[portKey][0].HostPort : 
      this.port;
    
    logger.debug(`Waiting for port ${this.port} on container ${info.Name}`);
    logger.debug(`Container IP: ${containerIp}, Host port: ${hostPort}`);
    
    // First try localhost with the mapped port
    let connected = false;
    while (!connected) {
      try {
        // First try host connection with mapped port (most reliable on Docker Desktop)
        try {
          logger.debug(`Checking port on localhost:${hostPort}`);
          await this.checkPort('localhost', parseInt(hostPort));
          logger.debug(`Successfully connected to localhost:${hostPort}`);
          return; // Success!
        } catch (error: any) {
          const localError = error;
          logger.debug(`Couldn't connect to localhost:${hostPort}: ${localError.message}`);
          
          // If container has an IP, try direct connection
          if (containerIp) {
            try {
              logger.debug(`Checking port on container IP ${containerIp}:${this.port}`);
              await this.checkPort(containerIp, parseInt(this.port));
              logger.debug(`Successfully connected to ${containerIp}:${this.port}`);
              return; // Success!
            } catch (error: any) {
              const containerError = error;
              logger.debug(`Couldn't connect to ${containerIp}:${this.port}: ${containerError.message}`);
              // Both approaches failed, will retry after delay
            }
          }
        }
        
        // Check if we've timed out or been aborted
        if (signal?.aborted) {
          throw new Error('Operation was aborted');
        }
        
        if (Date.now() - startTime > this.timeout) {
          throw new Error(`Timeout waiting for port ${this.port} (${this.timeout}ms)`);
        }
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, this.retryInterval));
      } catch (error) {
        // Check if we've timed out or been aborted
        if (signal?.aborted) {
          throw new Error('Operation was aborted');
        }
        
        if (Date.now() - startTime > this.timeout) {
          throw new Error(`Timeout waiting for port ${this.port} (${this.timeout}ms)`);
        }
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, this.retryInterval));
      }
    }
  }
  
  private checkPort(host: string, port: number): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      const socket = new net.Socket();
      let isConnected = false;
      
      socket.setTimeout(this.connectionTimeout);
      
      socket.on('connect', () => {
        isConnected = true;
        socket.destroy();
        resolve();
      });
      
      socket.on('timeout', () => {
        socket.destroy();
        reject(new Error(`Connection timeout to ${host}:${port}`));
      });
      
      socket.on('error', (error) => {
        if (!isConnected) {
          reject(error);
        }
      });
      
      socket.connect(port, host);
    });
  }
}
