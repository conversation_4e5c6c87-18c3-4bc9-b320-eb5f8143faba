import { BrickConfig } from "./brick_config";
import { ContainerBrick } from "./container_brick";
import { GenericContainerBrick } from "./generic_container_brick";
import { logger } from "./utils/logger";

/**
 * Manages multiple container bricks as a single test infrastructure
 */
export class InfrastructureManager {
  private readonly configs: BrickConfig[];
  private readonly containers: ContainerBrick[];

  /**
   * Creates a new infrastructure manager
   * 
   * @param configs Array of brick configurations
   */
  constructor(configs: BrickConfig[]) {
    this.configs = [...configs];
    this.containers = configs.map((config) => new GenericContainerBrick(config));
  }

  /**
   * Starts all containers in the infrastructure
   * 
   * @throws Error if any container fails to start
   */
  async startAll(): Promise<void> {
    logger.info(`Starting ${this.containers.length} containers...`);
    
    for (const container of this.containers) {
      try {
        await container.start();
      } catch (error) {
        logger.error("Failed to start container", error);
        // Attempt to stop any containers that were already started
        await this.stopAll().catch((stopError) => {
          logger.error("Failed to stop containers after start failure", stopError);
        });
        throw error;
      }
    }
    
    logger.info("All containers started successfully");
  }

  /**
   * Stops all containers in the infrastructure
   * 
   * @returns Promise that resolves when all containers are stopped
   */
  async stopAll(): Promise<void> {
    logger.info(`Stopping ${this.containers.length} containers...`);
    
    const stopPromises = this.containers.map(async (container) => {
      try {
        await container.stop();
        return true;
      } catch (error) {
        logger.error("Failed to stop container", error);
        return false;
      }
    });
    
    const results = await Promise.all(stopPromises);
    const failedCount = results.filter(result => !result).length;
    
    if (failedCount > 0) {
      logger.warn(`Failed to stop ${failedCount} containers`);
    } else {
      logger.info("All containers stopped successfully");
    }
  }

  /**
   * Adds and starts a new container to the infrastructure
   * 
   * @param config Brick configuration for the new container
   * @throws Error if the container fails to start
   */
  async addContainer(config: BrickConfig): Promise<void> {
    const container = new GenericContainerBrick(config);
    
    try {
      await container.start();
      this.containers.push(container);
      this.configs.push(config);
      logger.info(`Added and started container: ${config.instanceName}`);
    } catch (error) {
      logger.error(`Failed to add container ${config.instanceName}:`, error);
      throw error;
    }
  }
}
