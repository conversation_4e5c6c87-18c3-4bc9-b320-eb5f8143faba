/**
 * Testcontainers for TypeScript - A library for managing Docker containers for testing
 */

// Core exports
export { ContainerBrick } from './container_brick';
export { BrickConfig } from './brick_config';
export { GenericContainerBrick } from './generic_container_brick';
export { InfrastructureManager } from './infrastructure_manager';
export { createNetwork } from './network';

// Core interfaces and types
export { 
  VolumeMapping, 
  HealthCheckConfig,
  createBrickConfig 
} from './brick_config';

// Container implementations
export { createContainerBrick } from './container_brick_factory';

// Wait strategies
export { 
  Strategy,
  WaitStrategyOptions,
  HttpStrategy, 
  HttpStrategyOptions,
  LogStrategy,
  LogStrategyOptions,
  PortStrategy,
  PortStrategyOptions
} from './wait';

// Infrastructure and network management
export { 
  NetworkOptions,
  removeNetwork
} from './network';

// Utilities
export { Logger, logger } from './utils/logger'; 