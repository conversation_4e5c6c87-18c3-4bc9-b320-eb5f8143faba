import Docker, { Container, ContainerCreateOptions, HostConfig } from 'dockerode';
import { BrickConfig, VolumeMapping } from './brick_config';
import { ContainerBrick } from './container_brick';
import { PortStrategy } from './wait';
import { logger } from './utils/logger';

/**
 * Mapping of brick types to Docker image names
 */
const BRICK_TYPE_IMAGE_MAP: Record<string, string> = {
  'dapr': 'daprio/daprd:1.14.4',
  'dapr-zipkin': 'openzipkin/zipkin',
  'dapr-redis': 'redis:latest',
  'dapr-placement': 'daprio/dapr-placement:latest',
  'dapr-sentry': 'daprio/dapr-sentry:latest',
  'dapr-dashboard': 'daprio/dapr-dashboard:latest',
  'mqtt': 'eclipse-mosquitto:latest',
  'postgres': 'postgres:15-alpine',
  'gin-go': 'platform-tests-gin-go:latest',
  'fib-calc': 'platform-tests-fib-calc:latest',
  'k6-gin': 'grafana/k6:latest',
  'mongodb': 'mongo:latest',
};

/**
 * Generic implementation of a container brick
 * Manages Docker container lifecycle using dockerode
 */
export class GenericContainerBrick implements ContainerBrick {
  private readonly config: BrickConfig;
  private container?: Container;
  private readonly docker: Docker;
  private started = false;

  /**
   * Creates a new generic container brick
   * 
   * @param config The brick configuration
   */
  constructor(config: BrickConfig) {
    this.config = config;
    this.docker = new Docker();
  }

  /**
   * Maps a brick type to the corresponding Docker image
   * 
   * @param brickType The brick type to resolve
   * @returns The Docker image name
   * @throws Error if the brick type is unknown
   */
  private getImageForBrickType(brickType: string): string {
    const image = BRICK_TYPE_IMAGE_MAP[brickType];
    
    if (!image) {
      throw new Error(`Unknown brick type: ${brickType}`);
    }
    
    return image;
  }

  /**
   * Starts the container and waits until it's ready
   * 
   * @param signal Optional AbortSignal to cancel the operation
   * @throws Error if the container fails to start
   */
  async start(signal?: AbortSignal): Promise<void> {
    if (this.started) {
      logger.warn(`Container ${this.config.instanceName} is already started`);
      return;
    }
    
    try {
      const image = this.getImageForBrickType(this.config.brickType);
      logger.info(`Starting container ${this.config.instanceName} with image ${image}`);
      
      // Prepare container create options
      const createOptions = this.buildContainerCreateOptions(image);
      
      // Create and start the container
      this.container = await this.docker.createContainer(createOptions);
      await this.container.start();
      
      this.started = true;
      
      // Apply wait strategy
      await this.applyWaitStrategy(signal);
      
      logger.info(`Container ${this.config.instanceName} started successfully`);
    } catch (error) {
      logger.error(`Failed to start container ${this.config.instanceName}:`, error);
      
      // Attempt to clean up if container was created
      if (this.container) {
        try {
          await this.container.remove({ force: true });
        } catch (cleanupError) {
          logger.error(`Failed to clean up container after start failure:`, cleanupError);
        }
        
        this.container = undefined;
      }
      
      throw error;
    }
  }

  /**
   * Builds the container creation options
   * 
   * @param image Docker image to use
   * @returns Container creation options
   */
  private buildContainerCreateOptions(image: string): ContainerCreateOptions {
    return {
      Image: image,
      name: this.config.instanceName,
      Cmd: this.config.commandArguments,
      Env: this.transformEnvironmentVariables(),
      ExposedPorts: this.config.exposedPort 
        ? { [this.config.exposedPort]: {} } 
        : undefined,
      Labels: { 
        "com.docker.compose.project": "platform-tests",
        "managed-by": "testcontainers" 
      },
      HostConfig: this.createHostConfig(),
    };
  }

  /**
   * Transforms environment variables to Docker format
   * 
   * @returns Array of environment variables in KEY=VALUE format
   */
  private transformEnvironmentVariables(): string[] | undefined {
    if (!this.config.environmentVariables || Object.keys(this.config.environmentVariables).length === 0) {
      return undefined;
    }
    
    return Object.entries(this.config.environmentVariables).map(
      ([key, value]) => `${key}=${value}`
    );
  }

  /**
   * Creates the host configuration for the container
   * 
   * @returns Docker host configuration
   */
  private createHostConfig(): HostConfig {
    const hostConfig: HostConfig = {};
    
    // Set up port bindings if a hostPort is specified
    if (this.config.hostPort && this.config.exposedPort) {
      hostConfig.PortBindings = {
        [this.config.exposedPort]: [
          { HostIp: '0.0.0.0', HostPort: this.config.hostPort }
        ]
      };
    }
    
    // Set up volume bindings if volumes are specified
    if (this.config.volumes && this.config.volumes.length > 0) {
      hostConfig.Binds = this.config.volumes.map(
        (vol: VolumeMapping) => `${vol.hostPath}:${vol.containerPath}`
      );
    }
    
    // Set up networks
    if (this.config.networks && this.config.networks.length > 0) {
      hostConfig.NetworkMode = this.config.networks[0];
    }
    
    // Apply custom host config modifications if provided
    if (this.config.hostConfigModifier) {
      Object.assign(hostConfig, this.config.hostConfigModifier);
    }
    
    return hostConfig;
  }

  /**
   * Applies the appropriate wait strategy to determine when the container is ready
   * 
   * @param signal Optional AbortSignal to cancel the operation
   */
  private async applyWaitStrategy(signal?: AbortSignal): Promise<void> {
    if (!this.container) {
      throw new Error('Container is not started');
    }
    
    if (this.config.customWaitStrategy) {
      logger.debug(`Applying custom wait strategy for ${this.config.instanceName}`);
      await this.config.customWaitStrategy.waitUntilReady(this.container, signal);
    } else if (this.config.exposedPort) {
      logger.debug(`Applying default port wait strategy for ${this.config.instanceName} on port ${this.config.exposedPort}`);
      
      // Apply default port wait strategy
      const portStrategy = new PortStrategy(this.config.exposedPort);
      await portStrategy.waitUntilReady(this.container, signal);
    }
  }

  /**
   * Stops and removes the container
   * 
   * @param signal Optional AbortSignal to cancel the operation
   * @throws Error if the container fails to stop
   */
  async stop(signal?: AbortSignal): Promise<void> {
    if (!this.container || !this.started) {
      logger.warn(`Container ${this.config.instanceName} is not running`);
      return;
    }
    
    try {
      logger.info(`Stopping container ${this.config.instanceName}`);
      
      // Check if operation was aborted
      if (signal?.aborted) {
        throw new Error('Operation was aborted');
      }
      
      // Stop and remove the container
      await this.container.stop();
      await this.container.remove();
      
      this.container = undefined;
      this.started = false;
      
      logger.info(`Container ${this.config.instanceName} stopped and removed`);
    } catch (error) {
      logger.error(`Error stopping container ${this.config.instanceName}:`, error);
      
      // If this was not an abort error, attempt forceful removal
      if (!signal?.aborted && this.container) {
        try {
          await this.container.remove({ force: true });
          this.container = undefined;
          this.started = false;
        } catch (removeError) {
          logger.error(`Failed to forcefully remove container:`, removeError);
        }
      }
      
      throw error;
    }
  }

  /**
   * Returns the underlying Docker container instance
   * 
   * @returns The Docker container or undefined if not started
   */
  getContainer(): Container | undefined {
    return this.container;
  }
  
  /**
   * Checks if the container is currently running
   * 
   * @returns Promise that resolves with the running state
   */
  async isRunning(): Promise<boolean> {
    if (!this.container) {
      return false;
    }
    
    try {
      const info = await this.container.inspect();
      return info.State.Running === true;
    } catch (error) {
      return false;
    }
  }
}
