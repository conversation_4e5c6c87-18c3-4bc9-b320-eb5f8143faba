/**
 * Simple logger interface with different log levels
 */
export interface Logger {
  debug(message: string, ...args: any[]): void;
  info(message: string, ...args: any[]): void;
  warn(message: string, ...args: any[]): void;
  error(message: string, ...args: any[]): void;
}

/**
 * Default console-based logger implementation
 */
class ConsoleLogger implements Logger {
  debug(message: string, ...args: any[]): void {
    console.debug(`🔍 ${message}`, ...args);
  }

  info(message: string, ...args: any[]): void {
    console.info(`ℹ️ ${message}`, ...args);
  }

  warn(message: string, ...args: any[]): void {
    console.warn(`⚠️ ${message}`, ...args);
  }

  error(message: string, ...args: any[]): void {
    console.error(`❌ ${message}`, ...args);
  }
}

/**
 * Default logger instance
 */
export const logger: Logger = new ConsoleLogger(); 