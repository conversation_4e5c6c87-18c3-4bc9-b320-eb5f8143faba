import { Strategy } from "./wait";
import { HostConfig } from "dockerode";

/**
 * Represents a volume mapping between host and container paths
 */
export interface VolumeMapping {
  /** Path on the host machine */
  hostPath: string;
  /** Path inside the container */
  containerPath: string;
}

/**
 * Docker container health check configuration
 */
export interface HealthCheckConfig {
  /** Command to run for health check */
  test: string[];
  /** Time between health checks in milliseconds */
  interval: number;
  /** Number of retries before container is considered unhealthy */
  retries: number;
  /** Grace period before health checks start in milliseconds */
  startPeriod: number;
  /** Timeout for each health check in milliseconds */
  timeout: number;
}

/**
 * Configuration for a container-based test component
 */
export interface BrickConfig {
  /** Type of brick, used to determine Docker image */
  brickType: string;
  /** Container instance name */
  instanceName: string;
  /** Command arguments to pass to the container */
  commandArguments: string[];
  /** Environment variables to set in the container */
  environmentVariables: Record<string, string>;
  /** Container port to expose */
  exposedPort?: string;
  /** Host port to bind to */
  hostPort?: string;
  /** Volume mappings between host and container */
  volumes?: VolumeMapping[];
  /** Container health check configuration */
  healthCheck?: HealthCheckConfig;
  /** Docker networks to connect to */
  networks?: string[];
  /** Network aliases for the container in each network */
  networkAliases?: Record<string, string[]>;
  /** Additional host configuration overrides */
  hostConfigModifier?: HostConfig;
  /** Custom wait strategy to determine when container is ready */
  customWaitStrategy?: Strategy;
}

/**
 * Creates a brick configuration with defaults for optional properties
 */
export function createBrickConfig(config: BrickConfig): BrickConfig {
  return {
    volumes: [],
    networks: [],
    networkAliases: {},
    ...config
  };
}
