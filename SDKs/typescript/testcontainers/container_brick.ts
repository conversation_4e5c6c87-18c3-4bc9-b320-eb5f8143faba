/**
 * Represents a container-based test component.
 * Provides lifecycle methods for starting and stopping containers.
 */
export interface ContainerBrick {
  /**
   * Starts the container and waits until it's ready according to defined strategies.
   * @param signal Optional AbortSignal to cancel the operation
   * @throws Error if the container fails to start
   */
  start(signal?: AbortSignal): Promise<void>;
  
  /**
   * Stops and removes the container.
   * @param signal Optional AbortSignal to cancel the operation
   * @throws Error if the container fails to stop
   */
  stop(signal?: AbortSignal): Promise<void>;
}
  