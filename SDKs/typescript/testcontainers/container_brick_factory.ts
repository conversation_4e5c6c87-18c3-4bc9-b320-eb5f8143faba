import { BrickConfig } from "./brick_config";
import { GenericContainerBrick } from "./generic_container_brick";
import { ContainerBrick } from "./container_brick";

/**
 * Creates a new container brick from the provided configuration
 * 
 * @param config The brick configuration
 * @returns A new container brick instance
 */
export function createContainer<PERSON>rick(config: BrickConfig): ContainerBrick {
  return new GenericContainerBrick(config);
}
