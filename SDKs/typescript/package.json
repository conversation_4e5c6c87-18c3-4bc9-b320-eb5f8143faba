{"name": "typescript-testcontainers", "version": "1.0.0", "main": "dist/main.js", "scripts": {"build": "tsc", "test": "jest", "test:cucumber": "cucumber-js --format progress", "start": "node dist/main.js", "start:mock": "ts-node mock_server.ts"}, "devDependencies": {"@cucumber/cucumber": "8.10.0", "@types/amqplib": "^0.10.7", "@types/cucumber": "^7.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.15.3", "@types/pg": "^8.6.6", "jest": "^29.7.0", "ts-jest": "^29.3.1", "ts-node": "10.9.1", "typescript": "4.9.5"}, "dependencies": {"amqplib": "^0.10.8", "axios": "^1.8.4", "dockerode": "^4.0.5", "js-beautify": "1.14.9", "pg": "^8.11.3", "pretty": "^2.0.0", "testcontainers": "^10.24.0", "yaml": "^1.10.2"}}