version: "3.8"

services:
  db:
    image: postgres:15-alpine
    container_name: postgres-db
    restart: always
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=demo_db
    ports:
      - "5433:5432"
    volumes:
      - ./services/gin-go-demo/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d demo_db"]
      interval: 10s
      retries: 10  # Increase retries to wait longer
      start_period: 5s  # Wait a few seconds before starting healthcheck
      timeout: 5s
    networks:
      - bridge

  gin-go:
    build:
      context: .
      dockerfile: ./services/gin-go-demo/Dockerfile
    container_name: gin-go
    ports:
      - "8081:8081"
    environment:
      - DB_HOST=db
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      - DB_NAME=demo_db
      - JWT_SECRET=supersecretkey
      - DAPR_BASE_URL=http://dapr-gin:3500/v1.0
    networks:
      - bridge


  fib-calc:
    build:
      context: .
      dockerfile: ./services/fib-calc-service/Dockerfile
    container_name: fib-calc
    ports:
      - "8082:8082"
    environment:
      - DB_HOST=postgres-db
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=postgres
      - DB_NAME=demo_db
      - FIB_CALC_URL=http://fib-calc-service:8082
      - DAPR_BASE_URL=http://dapr-fib:3501/v1.0/invoke
    networks:
      - bridge
    depends_on:
      - db

  dapr-gin:
    image: "daprio/daprd:1.14.4"
    container_name: dapr-gin
    entrypoint: ["/daprd"]
    command: [
      "--app-id", "gin-go-demo",
      "--app-channel-address", "gin-go",  # Gin app address in the container network
      "--app-port", "8081",
      "--dapr-http-port", "3500",
      "--resources-path", "/app/.dapr/components"
    ]
    volumes:
      - ./components:/app/.dapr/components
    networks:
      - bridge
    ports:
      - "3500:3500"

  dapr-fib:
    image: "daprio/daprd:1.14.4"
    container_name: dapr-fib
    entrypoint: ["/daprd"]
    command: [
      "--app-id", "fib-calc",
      "--app-channel-address", "fib-calc",
      "--app-port", "8082",
      "--dapr-http-port", "3501",
      "--dapr-grpc-port", "50001",
      "--resources-path", "/app/.dapr/components"
    ]
    volumes:
      - ./components:/app/.dapr/components
    networks:
      - bridge
    ports:
      - "50001:50001"
      - "3501:3501"

  # Node exporter to collect host system metrics (CPU, RAM, etc.)
  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    restart: unless-stopped
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    networks:
      - bridge

  # Prometheus to scrape and store metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: unless-stopped
    volumes:
      - ./tests/performance/prometheus:/etc/prometheus
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    networks:
      - bridge
    depends_on:
      - node-exporter

  # InfluxDB for storing k6 metrics
  influxdb:
    image: influxdb:1.8
    container_name: k6-influxdb
    ports:
      - "8086:8086"
    environment:
      - INFLUXDB_DB=k6
      - INFLUXDB_ADMIN_USER=admin
      - INFLUXDB_ADMIN_PASSWORD=admin
    volumes:
      - influxdb-data:/var/lib/influxdb
    networks:
      - bridge

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: k6-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_AUTH_ANONYMOUS_ENABLED=true
      - GF_AUTH_ANONYMOUS_ORG_ROLE=Admin
      - GF_AUTH_DISABLE_LOGIN_FORM=true
    volumes:
      - grafana-data:/var/lib/grafana
      - ./tests/performance/grafana-dashboards:/etc/grafana/provisioning/dashboards
      - ./tests/performance/grafana-datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - influxdb
      - prometheus
    networks:
      - bridge

  k6-gin:
    build:
      context: ./tests/performance/scripts
      dockerfile: Dockerfile
    container_name: k6-gin
    volumes:
      - ./SDKs/k6/templates:/src/SDKs/k6/templates
      - ./SDKs/k6/utils:/src/SDKs/k6/utils
      - ./tests/performance/reports:/src/reports
    environment:
      - BASE_URL=http://dapr-gin:3500/v1.0/invoke/gin-go-demo/method
      - K6_OUT=influxdb=http://influxdb:8086/k6
      - K6_WEB_DASHBOARD_EXPORT=/src/reports/${K6_REPORT_FILE}
      - K6_SUMMARY_FILE=${K6_SUMMARY_FILE}
    ports:
      - "5665:5665"  # ← expose the dashboard port
    mem_limit: 3072m
    user: "${UID}:${GID}"
    cpus: 2
    networks:
      - bridge
    depends_on:
      - influxdb

  wiremock:
    build:
      context: ./wiremock
    ports:
      - "8080:8080"
    networks:
      - test-net
    healthcheck:
      test: curl --fail http://localhost:8080/__admin || exit 1
      interval: 5s
      retries: 5
      start_period: 5s

networks:
  bridge:
    driver: bridge

volumes:
  influxdb-data:
  grafana-data:
  prometheus-data:
