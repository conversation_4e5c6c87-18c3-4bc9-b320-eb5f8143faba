Feature: User Endpoint

  Scenario: Retrieve existing user successfully
    Given the PostgreSQL test container is running and the database is ready
    And the database is seeded with a user with email "<EMAIL>"
    When I send a GET request to "/user"
    Then the response status code should be 200
    And the response should contain user details with email "<EMAIL>", first name "<PERSON>", and last name "<PERSON><PERSON>"
