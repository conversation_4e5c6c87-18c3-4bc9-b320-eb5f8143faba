package features

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/cucumber/godog"
	"github.com/gin-gonic/gin"
	"github.com/onsi/gomega"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"example.com/gin-go-demo/internal/handlers"
	"example.com/gin-go-demo/internal/models"
	repository "example.com/gin-go-demo/internal/repositories"

	tc "github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/wait"
)

type userFeature struct {
	ctx       context.Context
	container tc.Container
	db        *gorm.DB
	router    *gin.Engine
	response  *httptest.ResponseRecorder
	testUser  models.User
}

// startPostgresContainer provisions a PostgreSQL container and auto-migrates the User model.
func (uf *userFeature) startPostgresContainer() error {
	uf.ctx = context.Background()
	req := tc.ContainerRequest{
		Image:        "postgres:13",
		ExposedPorts: []string{"5432/tcp"},
		Env: map[string]string{
			"POSTGRES_USER":     "postgres",
			"POSTGRES_PASSWORD": "postgres",
			"POSTGRES_DB":       "demo_db",
		},
		WaitingFor: wait.ForListeningPort("5432/tcp").WithStartupTimeout(60 * time.Second),
	}
	container, err := tc.GenericContainer(uf.ctx, tc.GenericContainerRequest{
		ContainerRequest: req,
		Started:          true,
	})
	if err != nil {
		return err
	}
	uf.container = container

	host, err := container.Host(uf.ctx)
	if err != nil {
		return err
	}
	port, err := container.MappedPort(uf.ctx, "5432")
	if err != nil {
		return err
	}
	dsn := fmt.Sprintf("host=%s port=%s user=postgres password=postgres dbname=demo_db sslmode=disable", host, port.Port())
	uf.db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		return err
	}
	// Auto-migrate the User model.
	return uf.db.AutoMigrate(&models.User{})
}

// seedDatabaseWithUser inserts a test user into the database.
func (uf *userFeature) seedDatabaseWithUser(email string) error {
	uf.testUser = models.User{
		Email:     email,
		FirstName: "John",
		LastName:  "Doe",
	}
	return uf.db.Create(&uf.testUser).Error
}

// iSendGETRequestToEndpoint creates a Gin router and sends a GET request to the specified endpoint.
func (uf *userFeature) iSendGETRequestToEndpoint(endpoint string) error {
	// Create the repository using the test DB.
	userRepo := repository.NewGormUserRepository(uf.db)
	uf.router = gin.New()
	// Register the /user endpoint (bypassing authentication for testing).
	uf.router.GET("/user", func(c *gin.Context) {
		handlers.GetUserHandler(c, userRepo)
	})
	req, err := http.NewRequest("GET", endpoint, nil)
	if err != nil {
		return err
	}
	// Set a dummy Authorization header.
	req.Header.Set("Authorization", "Bearer dummy-token")
	uf.response = httptest.NewRecorder()
	uf.router.ServeHTTP(uf.response, req)
	return nil
}

// theResponseStatusCodeShouldBe checks that the HTTP status code matches the expected value.
func (uf *userFeature) theResponseStatusCodeShouldBe(expectedCode int) error {
	if uf.response.Code != expectedCode {
		return fmt.Errorf("expected status code %d, got %d", expectedCode, uf.response.Code)
	}
	return nil
}

// theResponseShouldContainUserDetails verifies the response JSON for expected user details.
func (uf *userFeature) theResponseShouldContainUserDetails(email, firstName, lastName string) error {
	body, err := ioutil.ReadAll(uf.response.Body)
	if err != nil {
		return err
	}
	var user models.User
	if err := json.Unmarshal(body, &user); err != nil {
		return err
	}
	// Use Gomega assertions.
	gomega.Expect(user.Email).To(gomega.Equal(email))
	gomega.Expect(user.FirstName).To(gomega.Equal(firstName))
	gomega.Expect(user.LastName).To(gomega.Equal(lastName))
	return nil
}

// stopContainer terminates the PostgreSQL container.
func (uf *userFeature) stopContainer() error {
	if uf.container != nil {
		return uf.container.Terminate(uf.ctx)
	}
	return nil
}

// InitializeScenario sets up Godog steps.
func InitializeScenario(ctx *godog.ScenarioContext) {
	uf := &userFeature{}

	// Register a Before hook with the correct signature.
	ctx.Before(func(ctx context.Context, sc *godog.Scenario) (context.Context, error) {
		if err := uf.startPostgresContainer(); err != nil {
			return ctx, err
		}
		return ctx, nil
	})

	ctx.Step(`^the PostgreSQL test container is running and the database is ready$`, func() error {
		// The container is started in the Before hook.
		return nil
	})
	ctx.Step(`^the database is seeded with a user with email "([^"]*)"$`, uf.seedDatabaseWithUser)
	ctx.Step(`^I send a GET request to "([^"]*)"$`, uf.iSendGETRequestToEndpoint)
	ctx.Step(`^the response status code should be (\d+)$`, uf.theResponseStatusCodeShouldBe)
	ctx.Step(`^the response should contain user details with email "([^"]*)", first name "([^"]*)", and last name "([^"]*)"$`, uf.theResponseShouldContainUserDetails)

	// Register an After hook with the correct signature.
	ctx.After(func(ctx context.Context, sc *godog.Scenario, err error) (context.Context, error) {
		return ctx, uf.stopContainer()
	})
}

// TestUserEndpointFeatures runs the Godog test suite.
func TestUserEndpointFeatures(t *testing.T) {
	// Register Gomega's testing fail handler.
	gomega.RegisterTestingT(t)

	// Determine the absolute path to the feature file.
	wd, err := os.Getwd()
	if err != nil {
		t.Fatalf("failed to get working directory: %v", err)
	}
	featurePath := filepath.Join(wd, "user_endpoint.feature")
	if _, err := os.Stat(featurePath); os.IsNotExist(err) {
		t.Fatalf("feature file not found at: %s", featurePath)
	}

	opts := godog.Options{
		Format: "pretty",
		Paths:  []string{featurePath},
	}
	status := godog.TestSuite{
		Name:                "UserEndpoint",
		ScenarioInitializer: InitializeScenario,
		Options:             &opts,
	}.Run()
	if status != 0 {
		t.Fatalf("non-zero status returned, feature tests failed")
	}
}
