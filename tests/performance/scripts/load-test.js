import createTest from '/src/SDKs/k6/templates/load.js';
import { HttpClient } from '/src/SDKs/k6/utils/http.js';
import { BearerTokenManager } from '/src/SDKs/k6/utils/auth.js';
import { htmlReport } from 'https://raw.githubusercontent.com/benc-uk/k6-reporter/main/dist/bundle.js';
import { textSummary } from 'https://jslib.k6.io/k6-summary/0.0.1/index.js';

// Create HTTP client using SDK
const http = new HttpClient({
  baseUrl: __ENV.BASE_URL || 'http://dapr-gin:3500/v1.0/invoke/gin-go-demo/method',
  headers: { 
    'Content-Type': 'application/json',
    'Authorization': 'Bearer test-token' // Add a test token
  }
});

// Create auth manager
const auth = new BearerTokenManager({
  token: 'test-token'
});

// Define endpoints to test
const endpoints = {
  getUser: {
    method: 'GET',
    path: '/user'
  },
  getFibonacci: {
    method: 'GET',
    path: '/proxy-fib?n=10'
  }
};

// Create a stress test to find breaking points
const stressTestOptions = {
  // Progressive ramp-up to identify breaking point
  stages: [
    { duration: '1m', target: 5 },     // Start with 5 users
    { duration: '1m', target: 10 },    // Ramp up to 10 users
    { duration: '1m', target: 20 },    // Ramp up to 20 users
    { duration: '1m', target: 30 },    // Ramp up to 30 users
    { duration: '1m', target: 50 },    // Ramp up to 50 users
    { duration: '1m', target: 75 },    // Ramp up to 75 users
    { duration: '1m', target: 100 },   // Ramp up to 100 users
    { duration: '1m', target: 150 },   // Ramp up to 150 users
    { duration: '1m', target: 200 },   // Ramp up to 200 users
    { duration: '1m', target: 0 }      // Ramp down to 0 users
  ],
  // Strict thresholds to detect when system starts to fail
  thresholds: {
    'http_req_duration': ['p(95)<1000'],   // 95% of requests should be below 1000ms
    'http_req_failed': ['rate<0.05'],      // Less than 5% of requests should fail
    'http_reqs': ['rate>100'],             // Should handle at least 100 RPS
  },
  // Detailed metrics tracking
  summaryTrendStats: ['avg', 'min', 'med', 'max', 'p(90)', 'p(95)', 'p(99)'],
  summaryTimeUnit: 'ms',
  // Track critical metrics 
  systemTags: ['status', 'method', 'url', 'name', 'group', 'check'],
};

// Create load test using template with enhanced metrics tracking
const loadTest = createTest({
  http: http,
  auth: auth,
  endpoints: endpoints,
  options: stressTestOptions
});

// Export k6 lifecycle functions
export const options = loadTest.options;
export const setup = loadTest.setup;
export default loadTest.default;

// Enhanced HTML summary report
export function handleSummary(data) {
  const reportDate = new Date().toISOString().split('.')[0].replace(/[:T]/g, '-');
  const basePath = 'reports/';
  
  // Find breaking points and system limits
  let breakingPointVUs = 'Not reached';
  let breakingPointRPS = 'Not reached';
  let maxSuccessRPS = 0;
  
  // Analyze metrics to find breaking points
  if (data.metrics && data.metrics.http_reqs && data.metrics.http_req_failed) {
    const failRates = data.metrics.http_req_failed.values.rate;
    const reqRates = data.metrics.http_reqs.values.rate;
    const vuValues = data.metrics.vus.values;
    
    // Find the point where failure rate exceeds 5%
    if (failRates > 0.05) {
      breakingPointVUs = Math.max(...Object.values(vuValues));
      breakingPointRPS = reqRates;
      maxSuccessRPS = reqRates * (1 - failRates);
    } else {
      // If no failures above threshold, the system handled the max VUs
      maxSuccessRPS = reqRates;
    }
  }
  
  // Include breaking point info in summary
  const breakingPointSummary = `
# System Breaking Point Analysis
- Max VUs before failure threshold exceeded: ${breakingPointVUs}
- Request rate at breaking point: ${breakingPointRPS} RPS
- Max successful request rate: ${maxSuccessRPS.toFixed(2)} RPS
  `;
  
  return {
    // HTML Report with charts and detailed metrics
    [`${basePath}StressTestReport_${reportDate}.html`]: htmlReport(data, {
      title: 'Microservices Stress Test Results',
      logos: {
        k6: 'https://avatars.githubusercontent.com/u/11512485',
      },
    }),
    
    // Plain text summary with breaking point analysis
    [`${basePath}StressTestSummary_${reportDate}.txt`]: textSummary(data, { indent: ' ', enableColors: false }) + breakingPointSummary,
    
    // JSON data for potential further processing
    [`${basePath}StressTestData_${reportDate}.json`]: JSON.stringify(data, null, 2)
  };
}
