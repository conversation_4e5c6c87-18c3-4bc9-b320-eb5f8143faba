# Step 1: Build custom k6 with xk6-dashboard
FROM golang:1.24 as builder

# Install xk6
RUN go install go.k6.io/xk6/cmd/xk6@latest

# Build k6 with the dashboard extension
RUN xk6 build --with github.com/grafana/xk6-dashboard@latest

# Step 2: Create a lightweight final image
FROM alpine:latest

# Copy the custom-built k6 binary
COPY --from=builder /go/k6 /usr/bin/k6

# Create working directories
WORKDIR /src
RUN mkdir -p /src/SDKs/k6/templates /src/SDKs/k6/utils /src/reports

# Copy your test script
COPY load-test.js .

# Expose dashboard port
EXPOSE 5665


# Run the test with the dashboard output
CMD ["sh", "-c", "k6 run --out dashboard --summary-export=/src/reports/${K6_SUMMARY_FILE} /src/load-test.js"]