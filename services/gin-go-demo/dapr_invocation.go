package main

import (
	"context"
	"fmt"

	dapr "github.com/dapr/go-sdk/client"
)

// getFibonacciGRPC invokes the <PERSON><PERSON><PERSON><PERSON> calculation via Dapr's gRPC service invocation.
func getFibonacciGRPC(n int) (string, error) {
	// Create a new Dapr client (gRPC is used by default)
	ctx := context.Background()
	client, err := dapr.NewClient()
	if err != nil {
		return "", fmt.Errorf("failed to create Dapr client: %w", err)
	}
	defer client.Close()

	// Prepare the method name with query parameter, for example "fib?n=3"
	methodWithQuery := fmt.Sprintf("fib?n=%d", n)

	// Invoke the method on the app with app-id "fib-calc"
	// The HTTP verb (e.g., "post") must be provided.
	resp, err := client.InvokeMethodWithContent(ctx, "fib-calc", methodWithQuery, "post", &dapr.DataContent{
		ContentType: "text/plain",
		Data:        nil,
	})
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("error invoking fib-calc via gRPC: %w", err)
	}
	return string(resp), nil
}
