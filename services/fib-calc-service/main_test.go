package main

import (
	"testing"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
)

func TestFibCalcService(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "FibCalcService Suite")
}

var _ = Describe("Fibonacci", func() {
	Context("when calculating fibonacci numbers", func() {
		// Generic property-based test for the Fibonacci function
		It("should satisfy the Fibonacci recurrence relation F(n) = F(n-1) + F(n-2)", func() {
			// Test the fundamental property of Fibonacci numbers for multiple values
			for n := 2; n <= 20; n++ {
				// Arrange - calculate F(n), F(n-1), and F(n-2)
				fibN := fibonacci(n)
				fibN1 := fibonacci(n - 1)
				fibN2 := fibonacci(n - 2)

				// Assert - verify the recurrence relation holds
				Expect(fibN).To(Equal(fibN1+fibN2),
					"Fi<PERSON>acci(%d) should equal Fibonacci(%d) + <PERSON><PERSON><PERSON><PERSON>(%d)", n, n-1, n-2)
			}
		})

		// Test base cases explicitly
		It("should handle base cases correctly", func() {
			// Arrange & Act & Assert
			Expect(fibonacci(0)).To(Equal(0), "F(0) should be 0")
			Expect(fibonacci(1)).To(Equal(1), "F(1) should be 1")
		})

		// Test a few known values to ensure overall correctness
		DescribeTable("should return correct values for known Fibonacci numbers",
			func(n int, expected int) {
				Expect(fibonacci(n)).To(Equal(expected))
			},
			Entry("F(2) = 1", 2, 1),
			Entry("F(3) = 2", 3, 2),
			Entry("F(4) = 3", 4, 3),
			Entry("F(5) = 5", 5, 5),
			Entry("F(6) = 8", 6, 8),
			Entry("F(7) = 13", 7, 13),
			Entry("F(10) = 55", 10, 55),
			Entry("F(15) = 610", 15, 610),
		)

		Context("with invalid inputs", func() {
			It("should handle negative inputs by returning the input value", func() {
				// For negative inputs, the current implementation returns the input value
				// This is because of the condition (n <= 1) in the fibonacci function
				for n := -1; n >= -10; n-- {
					Expect(fibonacci(n)).To(Equal(n),
						"Fibonacci should return the input value for negative input %d", n)
				}
			})

			It("should not overflow for reasonable inputs", func() {
				// This test ensures the function can handle moderately large inputs
				// without overflowing (up to n=30 for int32)
				n := 30
				result := fibonacci(n)
				Expect(result).To(Equal(832040), "F(30) should be 832040")
			})
		})
	})
})
