package main

import (
	"fmt"
	"log"
	"net/http"
	"strconv"
)

// Fibonacci function (CPU-intensive recursive approach)
func fibonacci(n int) int {
	if n <= 1 {
		return n
	}
	return fibonacci(n-1) + <PERSON><PERSON><PERSON><PERSON>(n-2)
}

// HTTP handler to compute Fibonacci numbers
func fibHandler(w http.ResponseWriter, r *http.Request) {
	nStr := r.URL.Query().Get("n")
	if nStr == "" {
		http.Error(w, "Missing 'n' query parameter", http.StatusBadRequest)
		return
	}
	n, err := strconv.Atoi(nStr)
	if err != nil {
		http.Error(w, "Invalid number provided", http.StatusBadRequest)
		return
	}

	result := fibonacci(n)
	fmt.Fprintf(w, "Fibonacci(%d) = %d", n, result)
}

func main() {
	http.HandleFunc("/fib", fibHandler)
	log.Println("✅ FibCalcService running on port 8082...")
	log.Fatal(http.ListenAndServe("0.0.0.0:8082", nil))
}
