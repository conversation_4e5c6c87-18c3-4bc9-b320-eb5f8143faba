# 1) Build stage
ARG TARGETARCH
FROM --platform=$BUILDPLATFORM golang:1.23-bookworm AS builder

WORKDIR /app
# Copy go.mod and go.sum from the examples/fib-calc-service folder
COPY services/fib-calc-service/go.mod services/fib-calc-service/go.sum ./
RUN go mod download
# Copy main.go from the examples/fib-calc-service folder
COPY services/fib-calc-service/main.go ./
RUN CGO_ENABLED=0 GOOS=linux GOARCH=$TARGETARCH go build -o app main.go

# 2) Runtime stage
FROM debian:bookworm-slim

# Install required packages including curl, ca-certificates, netcat-openbsd, dnsutils, and inetutils-ping
RUN apt-get update && apt-get install -y \
    curl \
    ca-certificates \
    netcat-openbsd \
    dnsutils \
    inetutils-ping \
    && rm -rf /var/lib/apt/lists/*

# Create custom Dapr install directory
RUN mkdir -p /opt/dapr/bin

# Install Dapr CLI and runtime directly to custom path
RUN curl -LO https://github.com/dapr/cli/releases/download/v1.13.0/dapr_linux_amd64.tar.gz \
    && tar -xzf dapr_linux_amd64.tar.gz -C /opt/dapr/bin \
    && curl -LO https://github.com/dapr/dapr/releases/download/v1.13.0/daprd_linux_amd64.tar.gz \
    && tar -xzf daprd_linux_amd64.tar.gz -C /opt/dapr/bin \
    && chmod +x /opt/dapr/bin/dapr /opt/dapr/bin/daprd \
    && rm *.tar.gz

# Create symbolic links so that dapr and daprd are available in /usr/local/bin
RUN ln -s /opt/dapr/bin/dapr /usr/local/bin/dapr && \
    ln -s /opt/dapr/bin/daprd /usr/local/bin/daprd

# Explicitly set PATH to include our custom Dapr directory and /usr/local/bin
ENV PATH="/opt/dapr/bin:/usr/local/bin:${PATH}"
ENV DAPR_INSTALL_DIR="/opt/dapr/bin"

# (Optional) Initialize Dapr without default components
# If Dapr is already initialized externally, you can comment out the following line.
RUN dapr init --slim

# Create the app directory and copy components
RUN mkdir -p /app/.dapr/components
COPY --from=builder /app/app /app/
COPY components /app/.dapr/components

# Copy the startup script into the image
COPY entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

WORKDIR /app
EXPOSE 8082 3501

# Debug: List installed Dapr binaries
RUN dapr --version && daprd --version && ls -l /opt/dapr/bin

# Use the entrypoint script to wait for dependencies before starting the application.
ENTRYPOINT ["/app/entrypoint.sh"]
# IMPORTANT: Notice we add --app-channel-address "fib-calc-service"
CMD ["/opt/dapr/bin/dapr", "run", "--app-id", "fib-calc", "--app-channel-address", "fib-calc", "--app-port", "8082", "--dapr-http-port", "3501", "--resources-path", "/app/.dapr/components", "--", "./app"]
