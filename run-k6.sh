#!/bin/bash
# This script runs stress testing on the services to determine breaking points and monitor resource usage.

echo "Starting monitoring stack (Prometheus, InfluxDB, Grafana)..."
docker-compose up -d node-exporter prometheus influxdb grafana
echo "Waiting for monitoring services to be ready..."
sleep 5


echo "Running stress test with gradually increasing load..."
echo "This test will help identify at what point services start to fail and correlate with system resource usage."

# Generate a timestamped filename
TIMESTAMP=$(date +"%Y-%m-%d-%H-%M-%S")
REPORT_FILENAME="report-${TIMESTAMP}.html"
SUMMARY_FILENAME="summary-${TIMESTAMP}.json"

# Export them as environment variables
export K6_REPORT_FILE=$REPORT_FILENAME
export K6_SUMMARY_FILE=$SUMMARY_FILENAME
# Build and run the k6 container
echo "Building and running k6 tests..."
docker-compose build k6-gin
docker-compose up -d k6-gin

echo ""
echo "📈 Stress test started - gradually ramping up from 5 to 800 VUs"
echo ""
echo "Test will run for approximately 11 minutes. Visual report will be generated when test completes."
echo ""
echo "You can monitor the test while it runs:"
echo "- InfluxDB: http://localhost:8086 (raw data)"
echo "- Prometheus: http://localhost:9090 (metrics)"
echo "- Grafana: http://localhost:3000 (dashboards)"
echo ""

# Wait for k6 to finish
echo "Waiting for test to complete..."
docker-compose logs -f k6-gin

# Generate visual report when test completes


echo ""
echo "To shut down the monitoring stack when finished: docker-compose down"