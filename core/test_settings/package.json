{"name": "test_settings", "version": "1.2.0", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc -p tsconfig.json", "test": "ts-node index.ts"}, "dependencies": {"@playwright/test": "^1.52.0", "dotenv": "^16.5.0", "yargs": "^17.7.2", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^18.0.0", "@types/yargs": "^17.0.24", "ts-node": "^10.9.2", "typescript": "^5.0.0"}, "license": "MIT"}