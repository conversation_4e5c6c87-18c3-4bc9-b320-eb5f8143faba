import dotenv from "dotenv";
import yargs from "yargs";
import { hideBin } from "yargs/helpers";
import { z } from "zod";

// Load .env file
dotenv.config();

// CLI parsing
const argv = yargs(hideBin(process.argv))
  .options({
    environment: { type: "string", choices: ["DEV", "QA", "STAGE"] as const },
    baseURL: { type: "string" },
    browser: { type: "string", choices: ["chromium", "firefox", "webkit"] as const },
    timeout: { type: "number" },
    viewportWidth: { type: "number" },
    viewportHeight: { type: "number" },
    headless: { type: "boolean" },
    video: { type: "boolean" },
    trace: { type: "boolean" },
    parallelWorkers: { type: "number" },
  })
  .parseSync();

// Enums for strict typing
export enum Environment {
  DEV = "DEV",
  QA = "QA",
  STAGE = "STAGE"
}

export enum BrowserType {
  CHROMIUM = "chromium",
  FIREFOX = "firefox",
  WEBKIT = "webkit"
}

// Zod schema for validation
const SettingsSchema = z.object({
  environment: z.nativeEnum(Environment),
  baseURL: z.string().url(),
  browser: z.nativeEnum(BrowserType),
  timeout: z.number().int().positive(),
  viewport: z.object({
    width: z.number().int().positive(),
    height: z.number().int().positive(),
  }),
  flags: z.object({
    headless: z.boolean(),
    video: z.boolean(),
    trace: z.boolean(),
  }),
  parallelWorkerCount: z.number().int().positive(),
});

export type TestSettings = z.infer<typeof SettingsSchema>;

// Helper to parse booleans from env/CLI
function parseBool(val: unknown, fallback: boolean): boolean {
  if (typeof val === "boolean") return val;
  if (typeof val === "string") return ["1", "true", "yes", "on"].includes(val.toLowerCase());
  return fallback;
}

// Merge CLI > .env > defaults
const merged = {
  // Environment: DEV by default
  environment: (argv.environment as Environment) || (process.env.ENVIRONMENT as Environment) || Environment.DEV,
  // Base URL: localhost by default
  baseURL: (argv.baseURL as string) || process.env.BASE_URL || "http://localhost:3000",
  // Browser: chromium by default
  browser: (argv.browser as BrowserType) || (process.env.BROWSER as BrowserType) || BrowserType.CHROMIUM,
  // Timeout: 30000ms by default
  timeout: argv.timeout || (process.env.TIMEOUT ? Number(process.env.TIMEOUT) : 30000),
  // Viewport: 1280x720 by default
  viewport: {
    width: argv.viewportWidth || (process.env.VIEWPORT_WIDTH ? Number(process.env.VIEWPORT_WIDTH) : 1280),
    height: argv.viewportHeight || (process.env.VIEWPORT_HEIGHT ? Number(process.env.VIEWPORT_HEIGHT) : 720),
  },
  // Flags: headless true, video/trace false by default
  flags: {
    headless: parseBool(argv.headless ?? process.env.HEADLESS, false),
    video: parseBool(argv.video ?? process.env.VIDEO, false),
    trace: parseBool(argv.trace ?? process.env.TRACE, false),
  },
  // Parallel workers: 2 by default
  parallelWorkerCount: argv.parallelWorkers || (process.env.PARALLEL_WORKERS ? Number(process.env.PARALLEL_WORKERS) : 2),
};

// Validate and freeze settings
let settings: TestSettings;
try {
  settings = SettingsSchema.parse(merged);
  Object.freeze(settings);
} catch (err) {
  if (err instanceof z.ZodError) {
    console.error("\n❌ Invalid test settings:");
    for (const issue of err.issues) {
      console.error(`- ${issue.path.join('.')}: ${issue.message}`);
    }
    process.exit(1);
  }
  throw err;
}

export * from '@playwright/test';
export { settings }; 