{"jest-describe": {"prefix": "describe-ts", "body": ["describe('${1:feature}', () => {", "  ${2}", "});"], "description": "Jest describe block"}, "jest-it": {"prefix": "it-ts", "body": ["it('${1:should do something}', async () => {", "  // ARRANGE", "  ${2}", "", "  // ACT", "  ${3}", "", "  // ASSERT", "  ${4}", "});"], "description": "Jest it block with AAA structure"}, "jest-async-throws": {"prefix": "throw-ts", "body": ["await expect(${1:fn}(${2:args})).rejects.toThrow(${3:ErrorType});"], "description": "Assert that an async call throws in Jest"}}