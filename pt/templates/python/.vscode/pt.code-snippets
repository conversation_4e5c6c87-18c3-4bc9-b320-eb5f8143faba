{
    "pytest-basic": {
      "prefix": "pytest",
      "body": [
      "def test_${1:feature}_${2:condition}_${3:expected_result}():",
      "    # ARRANGE",
      "    ${4:# setup mocks, data, etc.}",
      "",
      "    # ACT",
      "    ${5:# execute the function}",
      "",
      "    # ASSERT",
      "    ${6:# verify outcomes}"
    ],
    "description": "Create a pytest-style function with AAA pattern"
  },
}