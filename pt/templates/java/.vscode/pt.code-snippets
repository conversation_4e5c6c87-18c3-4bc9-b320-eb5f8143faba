{"junit-test-method": {"prefix": "it-java", "body": ["@Test", "void ${1:featureName}_when${2:Condition}_should${3:ExpectedBehavior}() {", "    // ARRANGE", "    ${4:when(someDependency.method()).thenReturn(someValue);}", "", "    // ACT", "    ${5:var result = service.methodUnderTest();}", "", "    // ASSERT", "    ${6:assertThat(result).isEqualTo(expected);}", "}"], "description": "JUnit test method with AAA pattern"}, "junit-assert-throws": {"prefix": "throw-java", "body": ["@Test", "void ${1:featureName}_when${2:InvalidCondition}_shouldThrow${3:Exception}() {", "    // ARRANGE", "    ${4:when(someDependency.method()).thenReturn(null);}", "", "    // ACT & ASSERT", "    assertThatThrownBy(() -> ${5:service.methodUnderTest()})", "        .isInstanceOf(${6:ExpectedException}.class)", "        .hasMessageContaining(\"${7:message fragment}\");", "}"], "description": "JUnit assert exception with AssertJ"}, "junit-before-each": {"prefix": "before-java", "body": ["@BeforeEach", "void setUp() {", "    ${1:MockitoAnnotations.openMocks(this);}", "    ${2:service = new ServiceImpl(dependency);}", "}"], "description": "Setup method before each test"}}