package com.example;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.*;

class ExampleTest {

    @BeforeEach
    void setUp() {
        // Arrange: set up test data and environment
    }

    @Test
    void testSomethingHappens() {
        // Arrange

        // Act

        // Assert
        assertThat(true).isTrue(); // Dummy assert
    }

    @Test
    void testSomethingFails() {
        // Arrange

        // Act & Assert
        assertThatThrownBy(() -> {
            throw new RuntimeException("Something broke");
        }).isInstanceOf(RuntimeException.class)
          .hasMessageContaining("broke");
    }
}
