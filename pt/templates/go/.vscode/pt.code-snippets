{"ginkgo-describe": {"prefix": "describe-go", "body": ["Describe(\"${1:Component}\", func() {", "\t${2}", "})"], "description": "Ginkgo Describe block"}, "ginkgo-context": {"prefix": "context-go", "body": ["Context(\"${1:Condition}\", func() {", "\t${2}", "})"], "description": "Ginkgo Context block"}, "ginkgo-it": {"prefix": "it-go", "body": ["It(\"${1:should do something}\", func() {", "\t// <PERSON><PERSON>nge", "\t", "\t// Act", "\t", "\t// Assert", "})"], "description": "Ginkgo It block"}}