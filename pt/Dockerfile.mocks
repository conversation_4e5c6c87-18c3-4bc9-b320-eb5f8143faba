
# Stage 1: Node.js build
FROM node:20 AS node-build

# Set working directory
WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm install --legacy-peer-deps && npm install --save-dev @types/node --legacy-peer-deps

# Copy the mock generation script
COPY mocks/generate-mocks.ts .

RUN npm install ts-morph@latest --legacy-peer-deps

COPY . .

RUN npm run build

RUN npx tsc


# Stage 2: Python build
FROM python:3.11-slim AS python-build

WORKDIR /app
COPY mocks/generate-mocks.py .

RUN pip install pytest pytest-mock


# Stage 3: Go build
FROM golang:1.24 AS go-build

# Set working directory
WORKDIR /app

# Install mockgen and ensure it's in PATH
RUN go install go.uber.org/mock/mockgen@latest
ENV PATH="/go/bin:${PATH}"

# Copy go.mod and tidy dependencies
COPY go.mod ./
RUN go mod tidy

# Copy the mock generation script
COPY mocks/generate-mocks.go .

# Build Go app
RUN go build -o main.go


# Stage 4: Java build
FROM openjdk:17-jdk-slim AS java-build

WORKDIR /app
COPY mocks /app

RUN apt-get update && apt-get install -y maven
COPY . /app

# Compile the Java project
RUN mvn clean compile

# Debugging: Check if the target/classes directory exists
RUN ls -l /app/mocks/target/classes || echo "Directory does not exist"


# Final image combining all stages
# FROM debian:bullseye-slim
FROM node:20-slim

# Install necessary packages for Node.js, Java, and other dependencies
RUN apt-get update && apt-get install -y \
    openjdk-17-jdk \
    python3 \
    golang \
    && apt-get clean

# Copy files from all builds
COPY --from=node-build /app /app
COPY --from=python-build /app /app
COPY --from=go-build /app /app
COPY --from=java-build /app /app
COPY --from=java-build /app/mocks/target/classes /app/classes
COPY --from=go-build /go/bin/mockgen /usr/local/bin/mockgen


# Set working directory
WORKDIR /workspace

# Add custom entrypoint script
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]