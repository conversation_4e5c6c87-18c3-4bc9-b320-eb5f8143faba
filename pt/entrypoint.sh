#!/bin/bash
set -e

COMMAND=$1
shift

case "$COMMAND" in
  python)
    exec python3 /app/generate-mocks.py "$@"
    ;;
  ts)
    exec node /app/dist/generate-mocks.js "$1" "$2"
    ;;
   go)
    echo "➡ Running Go mock generator"
    exec go run /app/generate-mocks.go "$1" "$2"
    ;;
  java)
exec java -jar /app/mocks/target/mock-generator-1.0-SNAPSHOT.jar "$1" "$2"
    ;;
  *)
    echo "Unknown command: $COMMAND"
    echo "Usage: docker run ... [python|ts|go|java] args..."
    exit 1
    ;;
esac
