package com.example;

import com.github.javaparser.JavaParser;
import com.github.javaparser.ParseResult;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.body.ClassOrInterfaceDeclaration;
import com.github.javaparser.ast.body.MethodDeclaration;
import com.github.javaparser.ast.body.TypeDeclaration;
import com.github.javaparser.utils.SourceRoot;

import java.io.IOException;
import java.nio.file.*;
import java.util.List;

public class MockGenerator {

    public static void main(String[] args) throws IOException {
        if (args.length < 1) {
            System.out.println("Usage: java MockGenerator <source_directory> [output_directory]");
            return;
        }

        Path srcPath = Paths.get(args[0]);
        Path outputPath = args.length > 1 ? Paths.get(args[1]) : Paths.get("tests/mocks");

        System.out.println("Source directory: " + srcPath);
        System.out.println("Output directory: " + outputPath);

        Files.createDirectories(outputPath);

        SourceRoot sourceRoot = new SourceRoot(srcPath);
        System.out.println("Attempting to parse source files...");

        List<ParseResult<CompilationUnit>> parseResults = sourceRoot.tryToParse("com/example");

        System.out.println("Parsing completed. Processing results...");

        for (ParseResult<CompilationUnit> result : parseResults) {
            result.ifSuccessful(cu -> {
                String packageName = cu.getPackageDeclaration()
                        .map(pd -> pd.getName().toString())
                        .orElse("");
                System.out.println("Package name: " + packageName);

                for (TypeDeclaration<?> type : cu.getTypes()) {
                    if (type instanceof ClassOrInterfaceDeclaration) {
                        ClassOrInterfaceDeclaration decl = (ClassOrInterfaceDeclaration) type;
                        if (decl.isInterface()) {
                            System.out.println("Found interface: " + decl.getNameAsString());
                            generateMockFile(decl, packageName, outputPath);
                        }
                    }
                }
            });
        }
    }

    private static void generateMockFile(ClassOrInterfaceDeclaration iface, String packageName, Path outputPath) {
        String ifaceName = iface.getNameAsString();
        String className = "Mock" + ifaceName;
        StringBuilder sb = new StringBuilder();

        System.out.println("Generating mock for interface: " + ifaceName);
        
        // Package and imports
        sb.append("package com.generated;\n\n");
        sb.append("import static org.mockito.Mockito.*;\n");
        sb.append("import ").append(packageName).append(".").append(ifaceName).append(";\n");
        sb.append("import org.mockito.Mockito;\n\n");

        // Class declaration
        sb.append("public class ").append(className).append(" {\n\n");
        sb.append("    public static ").append(ifaceName).append(" create() {\n");
        sb.append("        ").append(ifaceName).append(" mock = Mockito.mock(").append(ifaceName).append(".class);\n\n");

        for (MethodDeclaration method : iface.getMethods()) {
            String methodName = method.getNameAsString();
            String returnType = method.getType().toString();
            boolean isVoid = returnType.equals("void");

            StringBuilder params = new StringBuilder();
            for (int i = 0; i < method.getParameters().size(); i++) {
                params.append("any()");
                if (i < method.getParameters().size() - 1) {
                    params.append(", ");
                }
            }

            System.out.println("Processing method: " + methodName);

            if (isVoid) {
                sb.append("        // Stubbing void method: ").append(methodName).append("\n");
                sb.append("        doNothing().when(mock).").append(methodName).append("(").append(params).append(");\n\n");
            } else {
                sb.append("        // Stubbing method: ").append(methodName).append(" with return type ").append(returnType).append("\n");
                sb.append("        when(mock.").append(methodName).append("(").append(params).append("))\n");
                sb.append("            .thenReturn(/* TODO: Provide a ").append(returnType).append(" value */ null);\n\n");
            }
        }

        sb.append("        return mock;\n");
        sb.append("    }\n");
        sb.append("}\n");

        // Write file
        Path filePath = outputPath.resolve(className + ".java");
        try {
            Files.write(filePath, sb.toString().getBytes());
            System.out.println("✅ Generated mock class: " + filePath.toAbsolutePath());
        } catch (IOException e) {
            System.err.println("❌ Failed to write " + filePath + ": " + e.getMessage());
        }
    }
}
