package main

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
)

func hasInterface(filename string) bool {
	fs := token.NewFileSet()
	node, err := parser.ParseFile(fs, filename, nil, 0)
	if err != nil {
		log.Printf("❌ Could not parse file %s: %v", filename, err)
		return false
	}

	for _, decl := range node.Decls {
		genDecl, ok := decl.(*ast.GenDecl)
		if !ok || genDecl.Tok != token.TYPE {
			continue
		}
		for _, spec := range genDecl.Specs {
			typeSpec := spec.(*ast.TypeSpec)
			if _, ok := typeSpec.Type.(*ast.InterfaceType); ok {
				return true
			}
		}
	}
	return false
}

func generateMock(sourceFile, outputDir string) error {
	// Check if file has at least one interface
	if !hasInterface(sourceFile) {
		log.Printf("🚫 Skipping mock generation for %s (no interfaces found)", sourceFile)
		return nil
	}

	base := filepath.Base(sourceFile)
	mockFile := strings.TrimSuffix(base, ".go") + "_mock.go"
	mockPath := filepath.Join(outputDir, mockFile)

	// Log before running mockgen
	fmt.Printf("➡️  Generating mock for %s at %s...\n", sourceFile, mockPath)

	cmd := exec.Command("mockgen", "-source="+sourceFile, "-destination="+mockPath, "-package=mocks")
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	// Log the command to ensure it's correct
	log.Printf("Executing command: %v", cmd.Args)

	err := cmd.Run()
	if err != nil {
		log.Printf("❌ Error generating mock for %s: %v", sourceFile, err)
		return err
	}
	return nil
}

func main() {
	// Check if the correct number of arguments were passed
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run generate_mocks.go <source_dir> [output_dir]")
		os.Exit(1)
	}

	sourceDir := os.Args[1]
	outputDir := "mocks"
	if len(os.Args) > 2 {
		outputDir = os.Args[2]
	}

	// Log source and output directories
	log.Printf("📂 Source Directory: %s", sourceDir)
	log.Printf("📂 Output Directory: %s", outputDir)

	// Ensure output directory exists
	if _, err := os.Stat(outputDir); os.IsNotExist(err) {
		log.Printf("📂 Creating output directory: %s", outputDir)
		if err := os.MkdirAll(outputDir, 0755); err != nil {
			log.Fatalf("❌ Error creating output directory: %v", err)
		}
	}

	// Walk through the source directory
	err := filepath.Walk(sourceDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			log.Printf("❌ Error walking through file: %v", err)
			return err
		}
		if info.IsDir() {
			log.Printf("📂 Skipping directory: %s", path)
			return nil
		}

		// Process only .go files, skip test files
		if strings.HasSuffix(info.Name(), ".go") && !strings.HasSuffix(info.Name(), "_test.go") {
			log.Printf("📄 Processing Go file: %s", path)
			if err := generateMock(path, outputDir); err != nil {
				return err
			}
		}
		return nil
	})

	// If error occurred during walking through files
	if err != nil {
		log.Fatalf("❌ Error generating mocks: %v", err)
	}

	// Success message
	fmt.Println("✅ Done generating mocks.")
}
