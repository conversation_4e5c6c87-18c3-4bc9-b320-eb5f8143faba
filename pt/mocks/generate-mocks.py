import os
import inspect
from pathlib import Path
import importlib
import sys

def get_package_name(module_path: str) -> str:
    """Get the package name from the module path (last folder before filename)."""
    parts = module_path.split('.')
    return parts[-2] if len(parts) >= 2 else parts[0]

def get_mock_value(field_name, field_type):
    """Return mock value for a given field type."""
    type_name = getattr(field_type, '__name__', str(field_type))
    if type_name in {'int'}:
        return "1"
    elif type_name in {'str'}:
        return f"'mock_{field_name}'"
    elif type_name in {'float'}:
        return "1.0"
    elif type_name in {'bool'}:
        return "True"
    else:
        return f"mocker.MagicMock(name='{type_name}')"

def generate_mock_file(module, iface, output_dir='mocks'):
    """Generate a mock class for the given interface using pytest-mock."""
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    pkg = get_package_name(iface.__module__)
    iface_name = iface.__name__
    mock_filename = f"{pkg}_{iface_name}_mock.py"
    mock_file_path = output_dir / mock_filename

    content_lines = [
        f"# Auto-generated mock for {iface.__module__}.{iface_name}",
        "import pytest",
        f"from {iface.__module__} import {iface_name}",
        ""
    ]

    # Try to import User for typing if needed
    extra_imports = set()
    for _, method in inspect.getmembers(iface, predicate=inspect.isfunction):
        sig = inspect.signature(method)
        for param in sig.parameters.values():
            if hasattr(param.annotation, '__name__') and param.annotation.__module__ != 'builtins':
                extra_imports.add(param.annotation.__module__)
        if hasattr(sig.return_annotation, '__name__') and sig.return_annotation.__module__ != 'builtins':
            extra_imports.add(sig.return_annotation.__module__)

    for mod in extra_imports:
        content_lines.append(f"from {mod} import *")

    content_lines += [
        "",
        "@pytest.fixture",
        f"def mock_{iface_name}(mocker):",
        f"    # Create a mock object for {iface_name}",
        f"    mock = mocker.MagicMock(spec={iface_name})",
        ""
    ]

    for name, method in inspect.getmembers(iface, predicate=inspect.isfunction):
        if not name.startswith('_'):
            if inspect.iscoroutinefunction(method):
                # async mock via side_effect
                content_lines.extend([
                    f"    # Mock the async '{name}' method",
                    f"    async def mocked_{name}(*args, **kwargs):",
                    f"        return mocker.MagicMock(name='mocked_{name}_result')",
                    f"    mock.{name}.side_effect = mocked_{name}",
                    ""
                ])
            else:
                content_lines.extend([
                    f"    # Mock the '{name}' method",
                    f"    mock.{name} = mocker.MagicMock(name='{name}')",
                    f"    mock.{name}.return_value = 'mocked_{name}_result'",
                    ""
                ])

    content_lines.append(f"    mock.reset_mock()")
    content_lines.append(f"    return mock")

    with open(mock_file_path, 'w') as f:
        f.write("\n".join(content_lines))

    print(f"✅ Created: {mock_file_path}")




def generate_mocks_for_directory(src_dir, output_dir='mocks'):
    """Generate mocks for all classes in Python files within the directory using pytest-mock."""
    src_path = Path(src_dir).resolve()
    sys.path.insert(0, str(src_path))  # <-- Add this line

    # base_path = src_path.parent
    # sys.path.insert(0, str(base_path))

    for file in src_path.rglob("*.py"):
        if file.name.startswith("_") or file.name.endswith("_mock.py"):
            continue

        # Compute module path relative to base_path (where src_dir's parent is in sys.path)
        try:
            module_path = str(file.relative_to(src_path)).replace("/", ".").replace("\\", ".")[:-3]
            module = importlib.import_module(module_path)
            for name, obj in inspect.getmembers(module):
                if inspect.isclass(obj) and obj.__module__ == module_path:
                    generate_mock_file(module, obj, output_dir)
        except Exception as e:
            print(f"Error processing {file}: {e}")


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python mock_generator.py <source_directory> [output_directory]")
        sys.exit(1)

    src_dir = sys.argv[1]
    output_dir = sys.argv[2] if len(sys.argv) > 2 else 'mocks'
    generate_mocks_for_directory(src_dir, output_dir)
