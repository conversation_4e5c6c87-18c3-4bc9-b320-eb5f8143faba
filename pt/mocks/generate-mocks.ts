import { Project } from 'ts-morph';
import * as fs from 'fs';
import * as path from 'path';

const directoryArg = process.argv[2];
const outputArg = process.argv[3];
const INPUT_DIR = path.resolve(directoryArg);
const OUTPUT_DIR = path.resolve(process.cwd(), outputArg);

console.log('Starting mock generation process...');
console.log(`Input Directory: ${INPUT_DIR}`);
console.log(`Output Directory: ${OUTPUT_DIR}`);
fs.mkdirSync(OUTPUT_DIR, { recursive: true });

const project = new Project();
project.addSourceFilesAtPaths(`${INPUT_DIR}/**/*.ts`);
const files = project.getSourceFiles();

// Helper function to get mock values for types, including resolving imported types
const getMockValue = (type: string, project: Project, imports: Set<string>, sourceFile: string): string => {
  console.log(`Generating mock value for type: ${type}`);

  // Handle Promise types by getting the inner type
  if (type.startsWith('Promise<')) {
    const innerType = type.slice(8, -1);
    console.log(`Detected Promise type, generating mock value for inner type: ${innerType}`);
    return `Promise.resolve(${getMockValue(innerType, project, imports, sourceFile)})`;
  }

  // Handle import paths (e.g., import("/src/ts/user.model").User)
  if (type.startsWith('import')) {
    const match = type.match(/import\((.*)\)\.(.*)/);
    if (match) {
      const [_, importPath, typeName] = match;
      console.log(`Detected import type, resolving mock for: ${typeName} from path: ${importPath}`);

      // Add the import path to the set of imports if it's not already there
      imports.add(importPath);
      const sourceFilePath = importPath.replace(/^["']|["']$/g, '');
      const resolvedPath = path.resolve(INPUT_DIR, `${sourceFilePath}.ts`);
      const importedFile = project.getSourceFile(resolvedPath);

      if (importedFile) {
        const typeDecl = importedFile.getInterface(typeName) || importedFile.getTypeAlias(typeName);
        if (typeDecl) {
          return generateMockForCustomType(typeDecl, project, imports, sourceFile);
        }
      }

      console.warn(`⚠️ Could not resolve imported type: ${typeName}`);
      return '{}';
    }
  }

  // Check for custom type aliases, like User, Job, etc.
  const typeAlias = project.getSourceFiles().map((sourceFile) =>
    sourceFile.getTypeAliases().find((alias) => alias.getName() === type)
  ).find((alias) => alias !== undefined);

  if (typeAlias) {
    console.log(`Generating mock value for custom type: ${type}`);
    return generateMockForCustomType(typeAlias, project, imports, sourceFile);
  }

  // Default mock values for primitive types
  switch (type) {
    case 'string':
      return `'mocked_string'`;
    case 'number':
      return '1';
    case 'boolean':
      return 'true';
    case 'any':
      return `'mocked_any'`;
    default:
      console.log(`Returning 'undefined' for unknown type: ${type}`);
      return 'undefined';
  }
};

// Helper function to generate mock data for custom types
const generateMockForCustomType = (customType: any, project: Project, imports: Set<string>, sourceFilePath: string): string => {
  console.log(`Generating mock data for custom type: ${customType.getName()}`);
  const properties = customType.getKindName() === 'TypeAliasDeclaration'
    ? customType.getType().getProperties()
    : customType.getProperties();
  const mockLines = properties.map((prop: any) => {
    const propName = prop.getName();
    const propType = prop.getType().getText();
    console.log(`Generating mock for property: ${propName}, type: ${propType}`);
    
    // If the property type is a custom type (like Job, User, etc.), add import
    if (propType === 'Job') {
      imports.add('../ts/job'); // Add Job import dynamically
    }
    return `"${propName}": ${getMockValue(propType, project, imports, sourceFilePath)},`;
  });

  return `{
    ${mockLines.join('\n')}
  }`;
};


// Iterate over all source files and generate mocks
for (const sourceFile of files) {
  console.log(`Processing source file: ${sourceFile.getFilePath()}`);

  const interfaces = sourceFile.getInterfaces();

  if (interfaces.length === 0) {
    console.log(`❌ No interfaces found in file: ${sourceFile.getFilePath()}`);
    continue;
  }

  const sourceFilePath = sourceFile.getFilePath(); // Make sure this is a string
  const dirName = path.basename(path.dirname(sourceFilePath));
  const relativeImportPath = path
    .relative(OUTPUT_DIR, sourceFilePath) // This will now work correctly because it's a string
    .replace(/\\/g, '/')
    .replace(/\.ts$/, '');

  console.log(`Found ${interfaces.length} interfaces in file: ${sourceFile.getFilePath()}`);

  // Process each interface
  for (const iface of interfaces) {
    console.log(`Processing interface: ${iface.getName()}`);
    
    const ifaceName = iface.getName();
    const mockFileName = `${dirName}_${ifaceName}_mock.ts`;
    const mockFilePath = path.join(OUTPUT_DIR, mockFileName);
    const properties = iface.getProperties();
    const methods = iface.getMethods();

    console.log(`Generating mock data for interface: ${ifaceName}`);
    
    // Set to hold unique import paths
    const imports = new Set<string>();
    const mockLines = properties.map((prop) => {
      const name = prop.getName();
      const type = prop.getType().getText();
      console.log(`Mocking property: ${name}, type: ${type}`);
    
      // Add any import dependencies found in the property type
      if (type.startsWith('import')) {
        const match = type.match(/import\((.*)\)\.(.*)/);
        if (match) {
          const [_, importPath, typeName] = match;
          if (!imports.has(importPath)) {
            imports.add(importPath);
            console.log("Imports -> ", imports);
          }
        }
      }
    
      const returned =  ` ${name}: ${getMockValue(type, project, imports, sourceFile.getFilePath())},`;  // Fix applied here
      console.log("returned -> ", returned );
      return returned;
    });
    
    // Inside the code where you process methods
    methods.forEach((method) => {
      const methodName = method.getName();
      const returnType = method.getReturnType().getText();
      console.log(`Mocking method: ${methodName}, return type: ${returnType}`);
      mockLines.push(`${methodName}: jest.fn(() => ${getMockValue(returnType, project, imports, sourceFile.getFilePath())}),`); // Fix applied here
    });


    // Prepare the mock object
    const mockObject = `{\n${mockLines.join('\n')}\n}`;
    
    // Dynamically add the imports
    const importStatements = [...imports].map((importPath) => {
      const fileName = path.basename(importPath, '.ts');
      const relativeImport = path.relative(path.dirname(mockFilePath), importPath).replace(/\\/g, '/');
      return `import { ${fileName} } from '${relativeImport}';`;
    }).join('\n');

    const content = `// AUTO-GENERATED FILE - DO NOT EDIT MANUALLY
import { mock } from 'jest-mock-extended';
import { jest } from '@jest/globals';
${[...imports].map((importPath) => `import { ${path.basename(importPath, '.ts')} } from '${importPath}';`).join('\n')}

export const ${ifaceName}Mock = mock<${ifaceName}>(${mockObject});
`;

// Write the mock file to disk
console.log(`Writing mock data to file: ${mockFilePath}`);
fs.writeFileSync(mockFilePath, content);
console.log(`✅ Created mock file: ${mockFilePath}`);
  }
}

console.log('✅ Mock generation completed!');



// const generateMockForCustomType = (
//   customType: any,
//   project: Project,
//   imports: Set<string>, 
//   sourceFilePath: string
// ): string => {
//   console.log(`Generating mock for custom type: ${customType.getName()}`);
  
//   const properties = customType.getKindName() === 'TypeAliasDeclaration'
//     ? customType.getType().getProperties()
//     : customType.getProperties();

//   const mockLines = properties.map((prop: any) => {
//     const propName = prop.getName();
//     const propType = prop.getType().getText();
//     console.log(`Mocking property: ${propName}, Type: ${propType}`);

//     // Add the necessary imports dynamically based on the type
//     if (propType === 'Job') {
//       imports.add('../ts/job');  // Add the Job import path
//     }
//     if (propType === 'User') {
//       imports.add('../ts/user');  // Add the User import path
//     }

//     return `"${propName}": ${getMockValue(propType, project, imports, sourceFilePath)},`;
//   });

//   return `{
//     ${mockLines.join('\n')}
//   }`;
// };

// // Function to generate the mock file with dynamic imports and mock data
// const generateMockFile = (
//   ifaceName: string, 
//   mockLines: string[], 
//   imports: Set<string>, 
//   mockFilePath: string
// ): void => {
//   // Generate the import statements dynamically
//   const importStatements = [...imports].map((importPath) => {
//     const importName = path.basename(importPath, '.ts');
//     return `import { ${importName} } from '${importPath}';`;
//   }).join('\n');

//   // Construct the content of the mock file
//   const content = `// AUTO-GENERATED FILE - DO NOT EDIT MANUALLY
// import { mock } from 'jest-mock-extended';
// import { jest } from '@jest/globals';
// ${importStatements}

// export const ${ifaceName}Mock = mock<${ifaceName}>({
//   ${mockLines.join('\n')}
// });
// `;

//   // Write the generated content to the mock file
//   console.log(`Writing mock data to file: ${mockFilePath}`);
//   fs.writeFileSync(mockFilePath, content);
//   console.log(`✅ Created mock file: ${mockFilePath}`);
// };

// // Function to generate mocks for all the types in a project
// const generateMocks = (project: Project, sourceFilePath: string, outputDir: string) => {
//   const sourceFile = project.addSourceFileAtPath(sourceFilePath);
//   const imports = new Set<string>();  // Track the necessary imports

//   sourceFile.getInterfaces().forEach((iface) => {
//     const mockLines: string[] = [];

//     // Generate mock for each property of the interface
//     iface.getProperties().forEach((prop) => {
//       const propName = prop.getName();
//       const propType = prop.getType().getText();
//       console.log(`Generating mock for property: ${propName}, Type: ${propType}`);
      
//       // Add import for custom types like Job, User, etc.
//       if (propType === 'Job') {
//         imports.add('../ts/job');
//       }
//       if (propType === 'User') {
//         imports.add('../ts/user');
//       }

//       // Add mock value for the property
//       mockLines.push(`"${propName}": ${getMockValue(propType, project, imports, sourceFilePath)},`);
//     });

//     // Now generate the mock file
//     const mockFilePath = path.join(outputDir, `${iface.getName()}Mock.ts`);
//     generateMockFile(iface.getName(), mockLines, imports, mockFilePath);
//   });
// };

// // Example usage
// const project = new Project();
// const sourceFilePath = './src/ts/someFile.ts';  // Path to your source file
// const outputDir = './mocks';  // Output directory for the generated mocks
// generateMocks(project, sourceFilePath, outputDir);