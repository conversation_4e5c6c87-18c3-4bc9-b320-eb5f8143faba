# WireMock

## WireMock Architecture & Workflow

<!-- WireMock Architecture PlantUML diagram -->
```plantuml
@startuml
top to bottom direction

actor "Platform QA" as PlatformQA
actor <PERSON><PERSON><PERSON> as DevOps
actor "Project QA/Automation" as ProjectQA

' Platform QA manages mappings
folder "wiremock-config/" as Mappings {
  folder "mappings/" as MappingsFolder
  folder "__files__/" as FilesFolder
}

package "Platform Tests" as PlatformTests {
  rectangle "WireMock Mappings & Dockerfile" as PlatformArtifacts
}

cloud "CI/CD Pipeline" as CICD {
  rectangle "Build WireMock Docker Image\n(COPY wiremock-config/)" as BuildImage
  rectangle "Push Image to Registry" as PushRegistry
  rectangle "Deploy WireMock Container" as DeployContainer
  rectangle "Expose WireMock Endpoint" as ExposeEndpoint
}

node "WireMock Container" as WireMock {
  database "Loaded Mappings" as LoadedMappings
  rectangle "WireMock Server" as Server
}

' Example consuming projects (vertical stack)
package "Project A" as ProjectA {
  rectangle "Test Code (Python/Go/TS/Java)" as TestCodeA
}
package "Project B" as ProjectB {
  rectangle "Test Code (Python/Go/TS/Java)" as TestCodeB
}

' Platform QA flow (top)
PlatformQA --> Mappings : Write & maintain stubs
Mappings --> PlatformArtifacts : Versioned in Platform Tests
PlatformArtifacts --> BuildImage : Included in image

' DevOps CI/CD flow (middle)
DevOps --> BuildImage : Triggers build
BuildImage --> PushRegistry : docker build & push
PushRegistry --> DeployContainer : CI/CD deploys
DeployContainer --> ExposeEndpoint : Make available to projects
ExposeEndpoint --> WireMock : Start container

' WireMock loads mappings at startup (middle)
WireMock --> LoadedMappings : Load mappings & files

' DevOps exposes WireMock endpoint to projects (middle)
ExposeEndpoint --> TestCodeA : Provide WireMock URL
ExposeEndpoint --> TestCodeB : Provide WireMock URL

' Project QA/Automation writes tests in their own repo (bottom)
ProjectQA --> TestCodeA : Write & maintain tests
ProjectQA --> TestCodeB : Write & maintain tests
TestCodeA --> WireMock : HTTP (Stub/Verify/Reset)
TestCodeB --> WireMock : HTTP (Stub/Verify/Reset)
WireMock --> TestCodeA : Mocked responses
WireMock --> TestCodeB : Mocked responses

' Test isolation (bottom)
TestCodeA --> WireMock : Reset scenarios (Admin API)
TestCodeB --> WireMock : Reset scenarios (Admin API)

' Feedback loop for mapping updates (bottom)
ProjectQA ..> PlatformQA : Request new/updated stubs

@enduml
```
---

## Overview

The **Platform Tests** repository provides a centralized, reusable WireMock container and a curated set of mappings for use by all projects in the organization. Individual projects write and own their own tests, but point to the shared WireMock instance for consistent, isolated, and repeatable API mocking. **DevOps is responsible for handling the CI/CD pipeline work**—building, pushing, deploying, and exposing the WireMock container/server for use by all projects (e.g., via docker-compose, Kubernetes, or CI/CD).

---

## Platform QA and DevOps Responsibilities

- **Platform QA**: Authors, maintains, and versions all WireMock stubs/mappings in `wiremock-config/` within the Platform Tests repo. Responds to requests from project teams for new or updated stubs.
- **DevOps**: Owns and maintains the CI/CD pipeline for building, pushing, deploying, and exposing the WireMock Docker image. Ensures the WireMock service is available and accessible to all projects (local, CI, staging, etc.). Integrates the container into the organization's infrastructure.
- **Project QA/Automation**: Writes and maintains all test code in their own project repositories. Requests new or updated stubs from Platform QA as needed.

---

## Key Components

- **Platform Tests (SDKs/wiremock/)**: Contains the Dockerfile and all mappings, maintained by Platform QA.
- **WireMock Container**: Runs the WireMock server with all mappings and files included. Exposes HTTP endpoints for stubbing and verification.
- **DevOps**: Integrates and exposes the WireMock container/server for all projects to use, and owns the CI/CD pipeline.
- **Project Test Suites**: Each project writes and maintains its own tests, which point to the shared WireMock instance.

---

## Workflow

1. **Mappings & Files (Platform QA)**
    - All stub mappings are written and maintained by Platform QA in `wiremock-config/mappings/` and version-controlled in the Platform Tests repo.
    - Static response files (if any) are placed in `wiremock-config/__files/`.

2. **Build & Deploy (DevOps)**
    - DevOps (or CI) builds the Docker image:
      ```sh
      docker build -t myorg/wiremock:latest .
      docker push myorg/wiremock:latest
      ```
    - The image is deployed via Docker Compose, Kubernetes, or other orchestrators, and the WireMock endpoint is exposed for all projects to use.

3. **WireMock Startup**
    - On container start, WireMock loads all mappings and files from `/home/<USER>
    - The server exposes HTTP endpoints for stubbing, verification, and scenario management.

4. **Test Execution (Project QA/Automation)**
    - Each project writes and maintains its own tests (in Python, Go, TypeScript, Java, etc.).
    - Tests point to the shared WireMock instance (e.g., `http://wiremock:8080`).
    - Tests can reset scenarios for isolation:
      ```python
      requests.post("http://wiremock:8080/__admin/scenarios/reset")
      ```
    - Tests verify mocked responses for all mapped endpoints.

5. **CI/CD Integration**
    - The WireMock container is started as a service in CI pipelines for all projects.
    - All test jobs (regardless of language) use the same WireMock instance for stubbing.

6. **Feedback Loop**
    - If a test in a project requires a new or updated stub, Project QA/Automation requests it from Platform QA.
    - Platform QA updates the mappings, and DevOps rebuilds and redeploys the image as part of the CI/CD process.

---

## Best Practices

- **Mappings are single-source-of-truth, owned by Platform QA**: Write once, use everywhere.
- **Projects write and own their own tests, but use the shared WireMock instance.**
- **Always reset scenarios before stateful tests** for isolation.
- **Rebuild the Docker image after changing mappings** to ensure the latest stubs are included.
- **Expose the WireMock port in all environments** (local, CI, staging) for test access.
- **Version mappings with your codebase** for traceability and reproducibility.
- **Project QA/Automation should communicate stub needs to Platform QA.**

---

## Example Directory Structure (Platform Tests)

```
SDKs/wiremock/
├── Dockerfile
├── wiremock-config/
│   ├── mappings/      # All stub mappings (JSON)
│   └── __files/       # Any static response files
```
