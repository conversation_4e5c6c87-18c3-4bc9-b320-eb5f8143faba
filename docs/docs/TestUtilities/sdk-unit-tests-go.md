---
id: sdk-unit-tests-go
title: Go SDK Unit Test Utilities
sidebar_label: Go SDK Unit Tests
---

# Go SDK Unit Test Utilities

The Go SDK provides comprehensive unit testing utilities built around the Ginkgo testing framework, Gomega assertions, and GoMock for mocking. This SDK includes test harness utilities, scaffolding tools, and mock generation capabilities.

## Overview

The Go SDK unit test utilities are located in `SDKs/Go/unitTests/` and provide:

- **Test Harness**: Unified test dependencies management (PostgreSQL, Redis, SMTP, Logging)
- **Assertions**: Gomega assertion library with expressive matchers
- **Mocking**: GoMock-based mocking utilities and generators
- **Scaffolding**: Test template generation and project setup

## Architecture

```
SDKs/Go/unitTests/
├── harness/                 # Test harness utilities
│   ├── harness_service.go   # Example service with dependencies
│   └── harness_utilities.go # Test harness management
├── assertions/              # Gomega assertion utilities
├── mocks/                   # GoMock utilities and generators
├── scaffolder/              # Test scaffolding tools
└── harness_test.go         # Example test implementation
```

## Test Harness

### HarnessService

The `HarnessService` provides a complete example of a service with common dependencies:

<augment_code_snippet path="SDKs/Go/unitTests/harness/harness_service.go" mode="EXCERPT">
````go
// UserService provides user management functionality
type UserService struct {
	db           *sql.DB
	redisClient  *redis.Client
	smtpSender   func(string, []string, string, string) error
	logger       *zap.Logger
	cacheTimeout time.Duration
}
````
</augment_code_snippet>

**Key Features:**
- **Database Integration**: PostgreSQL with connection pooling
- **Caching**: Redis integration with configurable timeout
- **Email Service**: SMTP sender with error handling
- **Logging**: Structured logging with correlation IDs
- **Context Support**: Request tracing and correlation

### Test Harness Utilities

The harness utilities provide mock implementations of all dependencies:

```go
// Example usage in tests
func setupTestHarness() *TestHarness {
    harness := &TestHarness{
        DB:          setupMockDB(),
        RedisClient: setupMockRedis(),
        SMTPSender:  setupMockSMTP(),
        Logger:      setupMockLogger(),
    }
    return harness
}
```

## Assertions with Gomega

### Core Matchers

Gomega provides expressive, readable assertions:

```go
import . "github.com/onsi/gomega"

// Basic assertions
Expect(user.Name).To(Equal("John Doe"))
Expect(user.Email).To(ContainSubstring("@example.com"))
Expect(users).To(HaveLen(3))
Expect(err).To(BeNil())

// Collection assertions
Expect(userIDs).To(ContainElement("user-123"))
Expect(response.Users).To(ConsistOf(user1, user2, user3))

// Asynchronous assertions
Eventually(func() error {
    return service.HealthCheck()
}).Should(Succeed())

Consistently(func() bool {
    return service.IsRunning()
}).Should(BeTrue())
```

### Custom Matchers

Create domain-specific matchers for better test readability:

```go
func BeValidUser() types.GomegaMatcher {
    return &userMatcher{}
}

type userMatcher struct{}

func (m *userMatcher) Match(actual interface{}) (bool, error) {
    user, ok := actual.(*User)
    if !ok {
        return false, fmt.Errorf("expected a User, got %T", actual)
    }
    
    return user.ID != "" && user.Email != "" && user.Name != "", nil
}
```

## Mocking with GoMock

### Mock Generation

Generate mocks for interfaces using GoMock:

```bash
# Generate mocks for interfaces
go generate ./...

# Or manually generate
mockgen -source=user_service.go -destination=mocks/mock_user_service.go
```

### Mock Usage in Tests

```go
//go:generate mockgen -source=user_service.go -destination=mocks/mock_user_service.go

func TestUserService_GetUser(t *testing.T) {
    ctrl := gomock.NewController(t)
    defer ctrl.Finish()
    
    mockDB := mocks.NewMockDatabase(ctrl)
    mockRedis := mocks.NewMockRedisClient(ctrl)
    
    // Set expectations
    mockRedis.EXPECT().
        Get("user:123").
        Return("", redis.Nil) // Cache miss
        
    mockDB.EXPECT().
        QueryRow(gomock.Any(), "123").
        Return(&User{ID: "123", Name: "John"})
    
    service := NewUserService(mockDB, mockRedis, nil, nil)
    user, err := service.GetUser(context.Background(), "123")
    
    Expect(err).To(BeNil())
    Expect(user.Name).To(Equal("John"))
}
```

## Test Scaffolding

### Ginkgo Test Structure

The SDK provides templates following Ginkgo's BDD-style structure:

<augment_code_snippet path="pt/templates/go/example_unit_test.go" mode="EXCERPT">
````go
var _ = Describe("Example Component", func() {
    var (
    // Declare shared variables here
    )

    BeforeEach(func() {
        // Arrange: set up any common state before each test
    })

    Describe("Behavior A", func() {
        Context("when scenario X happens", func() {
            It("should do something expected", func() {
                // Arrange
                // Act
                // Assert
            })
        })
    })
})
````
</augment_code_snippet>

### AAA Pattern Implementation

Follow the Arrange-Act-Assert pattern in your tests:

```go
It("should create user successfully", func() {
    // Arrange
    userData := &User{
        Name:  "John Doe",
        Email: "<EMAIL>",
    }
    
    mockDB.EXPECT().
        Insert(gomock.Any()).
        Return(nil)
    
    // Act
    err := service.CreateUser(context.Background(), userData)
    
    // Assert
    Expect(err).To(BeNil())
    Expect(userData.ID).ToNot(BeEmpty())
})
```

## Best Practices

### 1. Test Organization
- Use descriptive `Describe` and `Context` blocks
- Group related tests logically
- Use `BeforeEach` and `AfterEach` for setup/teardown

### 2. Mock Management
- Generate mocks using `go generate`
- Use `gomock.NewController` for each test
- Set clear expectations with `EXPECT()`

### 3. Assertion Guidelines
- Use expressive Gomega matchers
- Prefer `Eventually` for asynchronous operations
- Create custom matchers for domain objects

### 4. Test Data
- Use test builders for complex objects
- Isolate test data from production data
- Use meaningful test data that reflects real scenarios

## Integration with pt CLI

Generate Go test scaffolds using the platform tests CLI:

```bash
# Generate basic Go test scaffold
pt --lang go

# Generate with custom test file name
pt --lang go --file user_service_test

# Force regenerate existing scaffold
pt --lang go --force
```

This creates a complete test structure with:
- Ginkgo test suite setup
- Example test cases
- Mock generation setup
- Go module configuration

## Dependencies

The Go SDK unit tests require:

```go
// go.mod dependencies
github.com/onsi/ginkgo/v2
github.com/onsi/gomega
github.com/golang/mock/gomock
go.uber.org/zap
github.com/go-redis/redis/v8
```

Install dependencies:

```bash
go mod tidy
go install github.com/golang/mock/mockgen@latest
```
