# Playwright Test Utilities (`test_settings`)

The `test_settings` package is a TypeScript utility for managing and validating test configuration settings, designed for use with Playwright and modern E2E testing workflows. It provides a unified way to configure test environments via environment variables and `.env` files, with strong validation and type safety using Zod.

## Features
- **Unified test configuration**: Merge settings from environment variables and `.env` files.
- **Type-safe validation**: Uses Zod schemas for strict validation of all settings.
- **Playwright integration**: Re-exports everything from `@playwright/test` for seamless E2E testing.
- **Flexible configuration**: Override any setting at runtime using environment variables.
- **Strong defaults**: Sensible defaults for local development and CI.

## Installation

```sh
npm install test_settings@latest
```

## Configuration

Settings can be provided via:
- Environment variables
- `.env` file (loaded automatically)


### Supported Settings
| Setting            | Env Variable         | Default              | Description                       |
|--------------------|----------------------|----------------------|-----------------------------------|
| environment        | ENVIRONMENT          | DEV                  | Test environment (DEV, QA, STAGE) |
| baseURL            | BASE_URL             | http://localhost:3000| Base URL for tests                |
| browser            | BROWSER              | chromium             | Browser type (chromium, firefox, webkit) |
| timeout            | TIMEOUT              | 30000                | Test timeout in ms                |
| testDir            | TEST_DIR             | ./tests              | Playwright test directory         |
| viewportWidth      | VIEWPORT_WIDTH       | 1280                 | Browser viewport width            |
| viewportHeight     | VIEWPORT_HEIGHT      | 720                  | Browser viewport height           |
| headless           | HEADLESS             | true                 | Run browser in headless mode      |
| video              | VIDEO                | false                | Enable video recording            |
| trace              | TRACE                | false                | Enable Playwright tracing         |
| screenshot         | SCREENSHOT           | false                | Enable Playwright screenshots     |
| ignoreHTTPSErrors  | IGNORE_HTTPS_ERRORS  | false                | Ignore HTTPS errors               |
| parallelWorkers    | PARALLEL_WORKERS     | 2                    | Number of parallel workers        |
| reporter           | PLAYWRIGHT_REPORTER  | html                 | Playwright reporter               |
| outputDir          | PLAYWRIGHT_OUTPUT_DIR| test-results         | Playwright output directory       |

## Usage

### Importing and Using Settings

```ts
import { test, expect } from 'test_settings';
import { settings } from 'test_settings';

test('site loads and has correct title', async ({ page }) => {
  await page.goto(settings.baseURL);
  await expect(page).toHaveTitle('Example Domain');
  await expect(page.locator('h1')).toHaveText('Example Domain');
});
```

### Example: Using the Reusable Fixture

```ts
import { expect, browser_fixture, settings } from 'test_settings';

const test = browser_fixture;

test('site loads and has correct title and settings', async ({ page, context }) => {
  // Visit the base URL from settings
  await page.goto(settings.baseURL);

  // Assert the page title and main heading
  await expect(page).toHaveTitle('Example Domain');
  await expect(page.locator('h1')).toHaveText('Example Domain');

  // Assert settings are as expected
  expect(['DEV', 'QA', 'STAGE']).toContain(settings.environment);
  expect(['chromium', 'firefox', 'webkit']).toContain(settings.browser);
  expect(typeof settings.baseURL).toBe('string');
  expect(settings.baseURL).toMatch(/^https?:\/\//);
  expect(typeof settings.flags.headless).toBe('boolean');
  expect(typeof settings.flags.video).toBe('boolean');
  expect(typeof settings.flags.trace).toBe('boolean');
  expect(typeof settings.parallelWorkerCount).toBe('number');
  expect(settings.parallelWorkerCount).toBeGreaterThan(0);
  expect(typeof settings.viewport.width).toBe('number');
  expect(typeof settings.viewport.height).toBe('number');
  expect(settings.viewport.width).toBeGreaterThan(0);
  expect(settings.viewport.height).toBeGreaterThan(0);

  // Print settings for debug/demo
  console.log('Environment:', settings.environment);
  console.log('Base URL:', settings.baseURL);
  console.log('Browser:', settings.browser);
  console.log('Headless:', settings.flags.headless);
  console.log('Video:', settings.flags.video);
  console.log('Trace:', settings.flags.trace);
  console.log('Parallel Workers:', settings.parallelWorkerCount);
  console.log('Viewport:', settings.viewport);

  // Optionally, check that the context and page are valid
  expect(context).toBeDefined();
  expect(page).toBeDefined();
});
```

### Run tests

```sh
npx playwright test <path-to-test-file>
```

### Overriding Settings at Runtime

Override any setting at runtime using environment variables:


```sh
ENVIRONMENT=QA BASE_URL=https://example.com BROWSER=firefox HEADLESS=false npx playwright test
```

Or set them in your `.env` file:

```
ENVIRONMENT=DEV
BASE_URL=http://example.com
BROWSER=chromium
TIMEOUT=30000
TEST_DIR=./tests
VIEWPORT_WIDTH=1280
VIEWPORT_HEIGHT=720
HEADLESS=true
VIDEO=false
TRACE=false
SCREENSHOT=false
IGNORE_HTTPS_ERRORS=false
PARALLEL_WORKERS=5
PLAYWRIGHT_REPORTER=html
PLAYWRIGHT_OUTPUT_DIR=test-results
```
> **Note:** Custom CLI flags (e.g. `--environment=QA`) are **not supported** when running tests with `npx playwright test`. Use environment variables or a `.env` file to override settings.
