---
id: sdk-unit-tests-java
title: Java SDK Unit Test Utilities
sidebar_label: Java SDK Unit Tests
---

# Java SDK Unit Test Utilities

The Java SDK provides comprehensive unit testing utilities built around JUnit 5, AssertJ assertions, and Mockito for mocking. This SDK includes test harness utilities, scaffolding tools, and mock generation capabilities.

## Overview

The Java SDK unit test utilities are located in `SDKs/java/java-sdk/src/main/java/com/example/harness/` and provide:

- **Test Harness**: Unified test dependencies management (PostgreSQL, Redis, SMTP, Logging)
- **Assertions**: AssertJ library with fluent assertions
- **Mocking**: <PERSON><PERSON><PERSON> for mock creation and verification
- **Scaffolding**: Test template generation and project setup

## Architecture

```
SDKs/java/java-sdk/src/main/java/com/example/harness/
├── HarnessService.java         # Example service with dependencies
├── HarnessUtilities.java       # Test harness management utilities
└── JedisClientAdapter.java     # Redis client adapter
```

## Test Harness

### HarnessService

The `HarnessService` provides a complete example of a service with common dependencies:

<augment_code_snippet path="SDKs/java/java-sdk/src/main/java/com/example/harness/HarnessService.java" mode="EXCERPT">
````java
/**
 * Gets a user by ID, using cache when available.
 *
 * @param context the context containing correlation ID
 * @param userId  the user ID
 * @return the user, or null if not found
 * @throws Exception if an error occurs
 */
public User getUser(Context context, String userId) throws Exception {
    String corrId = getCorrelationId(context);
    logger.info("Getting user: userId={}, correlationId={}", userId, corrId);
    
    // First check Redis cache
    String cacheKey = "user:" + userId;
    String cachedUser = redisClient.get(cacheKey);
    
    if (cachedUser != null) {
        // Cache hit
        logger.info("User found in cache: userId={}, correlationId={}", userId, corrId);
        return OBJECT_MAPPER.readValue(cachedUser, User.class);
    }
````
</augment_code_snippet>

**Key Features:**
- **Database Integration**: JDBC with connection pooling
- **Caching**: Redis integration with configurable timeout
- **Email Service**: SMTP sender with error handling
- **Logging**: SLF4J logging with correlation IDs
- **Context Support**: Request tracing and correlation

### Service Dependencies

```java
public class HarnessService {
    private final DataSource dataSource;
    private final JedisClientAdapter redisClient;
    private final SmtpSender smtpSender;
    private final Logger logger;
    private final Duration cacheTimeout;
    
    public HarnessService(
            DataSource dataSource,
            JedisClientAdapter redisClient,
            SmtpSender smtpSender,
            Logger logger) {
        this.dataSource = dataSource;
        this.redisClient = redisClient;
        this.smtpSender = smtpSender;
        this.logger = logger;
        this.cacheTimeout = Duration.ofMinutes(10);
    }
}
```

## Assertions with AssertJ

### Fluent Assertions

AssertJ provides fluent, readable assertions:

```java
import static org.assertj.core.api.Assertions.*;

// Basic assertions
assertThat(user.getName()).isEqualTo("John Doe");
assertThat(user.getEmail()).contains("@example.com");
assertThat(users).hasSize(3);
assertThat(error).isNull();

// Object assertions
assertThat(user)
    .extracting(User::getName, User::getEmail)
    .containsExactly("John Doe", "<EMAIL>");

// Collection assertions
assertThat(userIds).contains("user-123");
assertThat(users).extracting(User::getName)
    .containsExactly("John", "Jane", "Bob");

// Exception assertions
assertThatThrownBy(() -> service.getUser(null, "invalid"))
    .isInstanceOf(IllegalArgumentException.class)
    .hasMessage("User ID cannot be null");

// Conditional assertions
assertThat(user.getAge())
    .isGreaterThan(18)
    .isLessThan(100);
```

### Custom Assertions

Create domain-specific assertions for better test readability:

```java
public class UserAssert extends AbstractAssert<UserAssert, User> {
    
    public UserAssert(User actual) {
        super(actual, UserAssert.class);
    }
    
    public static UserAssert assertThat(User actual) {
        return new UserAssert(actual);
    }
    
    public UserAssert hasValidEmail() {
        isNotNull();
        if (!actual.getEmail().contains("@")) {
            failWithMessage("Expected user to have valid email but was <%s>", actual.getEmail());
        }
        return this;
    }
    
    public UserAssert isActive() {
        isNotNull();
        if (!actual.isActive()) {
            failWithMessage("Expected user to be active but was inactive");
        }
        return this;
    }
}

// Usage
UserAssert.assertThat(user)
    .hasValidEmail()
    .isActive();
```

## Mocking with Mockito

### Mock Creation and Usage

Mockito provides powerful mocking capabilities:

```java
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.*;

@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    
    @Mock
    private DataSource mockDataSource;
    
    @Mock
    private JedisClientAdapter mockRedis;
    
    @Mock
    private SmtpSender mockSmtp;
    
    @Mock
    private Logger mockLogger;
    
    @InjectMocks
    private HarnessService userService;
    
    @Test
    void shouldGetUserFromCache() throws Exception {
        // Arrange
        String userId = "123";
        String cachedUser = "{\"id\":\"123\",\"name\":\"John\"}";
        when(mockRedis.get("user:123")).thenReturn(cachedUser);
        
        // Act
        User user = userService.getUser(createContext(), userId);
        
        // Assert
        assertThat(user).isNotNull();
        assertThat(user.getName()).isEqualTo("John");
        verify(mockRedis).get("user:123");
        verifyNoInteractions(mockDataSource);
    }
    
    @Test
    void shouldFetchFromDatabaseOnCacheMiss() throws Exception {
        // Arrange
        String userId = "123";
        when(mockRedis.get("user:123")).thenReturn(null);
        
        Connection mockConnection = mock(Connection.class);
        PreparedStatement mockStatement = mock(PreparedStatement.class);
        ResultSet mockResultSet = mock(ResultSet.class);
        
        when(mockDataSource.getConnection()).thenReturn(mockConnection);
        when(mockConnection.prepareStatement(anyString())).thenReturn(mockStatement);
        when(mockStatement.executeQuery()).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getString("id")).thenReturn("123");
        when(mockResultSet.getString("name")).thenReturn("John");
        when(mockResultSet.getString("email")).thenReturn("<EMAIL>");
        
        // Act
        User user = userService.getUser(createContext(), userId);
        
        // Assert
        assertThat(user).isNotNull();
        assertThat(user.getName()).isEqualTo("John");
        verify(mockRedis).get("user:123");
        verify(mockRedis).set(eq("user:123"), anyString(), anyLong());
    }
}
```

### Argument Matchers and Verification

```java
// Argument matchers
verify(mockRedis).set(eq("user:123"), contains("John"), anyLong());
verify(mockLogger).info(eq("Getting user: userId={}, correlationId={}"), eq("123"), anyString());

// Verification with times
verify(mockRedis, times(1)).get("user:123");
verify(mockRedis, never()).set(anyString(), anyString(), anyLong());

// Verification order
InOrder inOrder = inOrder(mockRedis, mockDataSource);
inOrder.verify(mockRedis).get("user:123");
inOrder.verify(mockDataSource).getConnection();

// Capture arguments
ArgumentCaptor<String> keyCaptor = ArgumentCaptor.forClass(String.class);
verify(mockRedis).set(keyCaptor.capture(), anyString(), anyLong());
assertThat(keyCaptor.getValue()).isEqualTo("user:123");
```

## Test Scaffolding

### JUnit 5 Test Structure

The SDK provides templates following JUnit 5 conventions:

```java
@ExtendWith(MockitoExtension.class)
@DisplayName("User Service Tests")
class UserServiceTest {
    
    @Mock
    private DataSource mockDataSource;
    
    @Mock
    private JedisClientAdapter mockRedis;
    
    @Mock
    private SmtpSender mockSmtp;
    
    @Mock
    private Logger mockLogger;
    
    @InjectMocks
    private HarnessService userService;
    
    @BeforeEach
    void setUp() {
        // Common setup for all tests
    }
    
    @AfterEach
    void tearDown() {
        // Cleanup after each test
    }
    
    @Nested
    @DisplayName("Get User Tests")
    class GetUserTests {
        
        @Test
        @DisplayName("Should return user from cache when available")
        void shouldReturnUserFromCache() {
            // Arrange
            // Act
            // Assert
        }
        
        @Test
        @DisplayName("Should fetch from database on cache miss")
        void shouldFetchFromDatabaseOnCacheMiss() {
            // Arrange
            // Act
            // Assert
        }
    }
    
    @Nested
    @DisplayName("Create User Tests")
    class CreateUserTests {
        
        @ParameterizedTest
        @ValueSource(strings = {"", " ", "invalid-email"})
        @DisplayName("Should reject invalid email addresses")
        void shouldRejectInvalidEmails(String invalidEmail) {
            // Test implementation
        }
    }
}
```

### AAA Pattern Implementation

Follow the Arrange-Act-Assert pattern in your tests:

```java
@Test
@DisplayName("Should create user successfully")
void shouldCreateUserSuccessfully() throws Exception {
    // Arrange
    User userData = new User(null, "John Doe", "<EMAIL>");
    Connection mockConnection = mock(Connection.class);
    PreparedStatement mockStatement = mock(PreparedStatement.class);
    
    when(mockDataSource.getConnection()).thenReturn(mockConnection);
    when(mockConnection.prepareStatement(anyString())).thenReturn(mockStatement);
    when(mockStatement.executeUpdate()).thenReturn(1);
    
    // Act
    boolean result = userService.createUser(createContext(), userData);
    
    // Assert
    assertThat(result).isTrue();
    verify(mockStatement).executeUpdate();
    verify(mockConnection).close();
}
```

## Best Practices

### 1. Test Organization
- Use `@Nested` classes to group related tests
- Use `@DisplayName` for descriptive test names
- Use `@BeforeEach` and `@AfterEach` for setup/teardown

### 2. Mock Management
- Use `@Mock` and `@InjectMocks` annotations
- Verify mock interactions with `verify()`
- Use argument matchers for flexible verification

### 3. Assertion Guidelines
- Use AssertJ for fluent, readable assertions
- Create custom assertions for domain objects
- Use `extracting()` for testing object properties

### 4. Parameterized Testing
- Use `@ParameterizedTest` for testing multiple scenarios
- Use `@ValueSource`, `@CsvSource`, or `@MethodSource` for test data
- Test edge cases and boundary conditions

## Integration with pt CLI

Generate Java test scaffolds using the platform tests CLI:

```bash
# Generate basic Java test scaffold
pt --lang java

# Generate with custom test file name
pt --lang java --file UserServiceTest

# Force regenerate existing scaffold
pt --lang java --force
```

This creates a complete test structure with:
- Maven/Gradle configuration
- JUnit 5 setup
- Example test cases
- Dependency management

## Dependencies

The Java SDK unit tests require these dependencies in `pom.xml`:

```xml
<dependencies>
    <!-- JUnit 5 -->
    <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>5.10.0</version>
        <scope>test</scope>
    </dependency>
    
    <!-- AssertJ -->
    <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <version>3.24.2</version>
        <scope>test</scope>
    </dependency>
    
    <!-- Mockito -->
    <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>5.5.0</version>
        <scope>test</scope>
    </dependency>
    
    <!-- Mockito JUnit Jupiter -->
    <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-junit-jupiter</artifactId>
        <version>5.5.0</version>
        <scope>test</scope>
    </dependency>
</dependencies>
```

## Running Tests

```bash
# Run all tests with Maven
mvn test

# Run specific test class
mvn test -Dtest=UserServiceTest

# Run tests with coverage
mvn test jacoco:report

# Run tests with Gradle
./gradlew test

# Run specific test
./gradlew test --tests UserServiceTest
```
