---
id: test-utilities-overview
title: Test Utilities Overview
sidebar_label: Overview
---

# Test Utilities Overview

This section covers the comprehensive testing utilities and frameworks available in our platform testing ecosystem, including SDK unit test utilities, scaffolding tools, and testing frameworks.

## Available Test Utilities

### SDK Unit Test Utilities
- **[Go SDK Unit Tests](./sdk-unit-tests-go.md)**: Ginkgo framework with Gomega assertions, GoMock, and test harness utilities
- **[Python SDK Unit Tests](./sdk-unit-tests-python.md)**: pytest framework with assertpy, pytest-mock, and test harness utilities  
- **[TypeScript SDK Unit Tests](./sdk-unit-tests-typescript.md)**: Jest framework with built-in assertions, jest-mock-extended, and test harness utilities
- **[Java SDK Unit Tests](./sdk-unit-tests-java.md)**: JUnit 5 with AssertJ assertions, Mockito, and test harness utilities

### Platform Testing Tools
- **[Platform Tests (pt) CLI](./platform-tests-cli.md)**: Multi-language test scaffolding and mock generation tool

### Testing Frameworks
- **[Go Testing Guide](./Go.md)**: Ginkgo framework setup and usage
- **[Python Testing Guide](./Python.md)**: pytest framework setup and usage
- **[TypeScript Testing Guide](./Typescript.md)**: Jest framework setup and usage
- **[Java Testing Guide](./Java.md)**: JUnit 5 framework setup and usage

### Performance & Integration Testing
- **[k6 Performance Testing](./k6-utilities.md)**: Load testing utilities and SDK
- **[WireMock Setup](./wiremock.md)**: API mocking and stubbing

## Architecture Overview

Our testing ecosystem is built around four core components:

| Component | Description | Languages |
|-----------|-------------|-----------|
| **Test Harness** | Unified test dependencies (DB, Redis, SMTP, Logging) | Go, Python, TypeScript, Java |
| **Assertions** | Language-specific assertion libraries | Gomega, assertpy, Jest Expect, AssertJ |
| **Mocking** | Mock generation and management utilities | GoMock, pytest-mock, jest-mock-extended, Mockito |
| **Scaffolding** | Test template generation and project setup | All languages via pt CLI |

## SDK Unit Test Features

### Test Harness Utilities

Each SDK provides a comprehensive test harness that includes:

- **Database Integration**: PostgreSQL with connection pooling and transaction management
- **Caching Layer**: Redis integration with configurable timeouts and error handling
- **Email Service**: SMTP sender with mock implementations for testing
- **Logging**: Structured logging with correlation ID support
- **Context Management**: Request tracing and correlation across service calls

### Language-Specific Assertion Libraries

| Language | Library | Key Features |
|----------|---------|--------------|
| **Go** | Gomega | Expressive matchers, async assertions (`Eventually`, `Consistently`) |
| **Python** | assertpy | Fluent, chainable assertions with readable syntax |
| **TypeScript** | Jest Expect | Rich built-in matchers with TypeScript support |
| **Java** | AssertJ | Fluent assertions with extensive matcher library |

### Mocking Frameworks

| Language | Framework | Key Features |
|----------|-----------|--------------|
| **Go** | GoMock | Interface-based mocking with code generation |
| **Python** | pytest-mock | Wrapper around unittest.mock with pytest integration |
| **TypeScript** | jest-mock-extended | Type-safe mocking for interfaces and classes |
| **Java** | Mockito | Annotation-based mocking with verification |

## Platform Tests (pt) CLI

The `pt` CLI tool provides:

### Multi-Language Scaffolding
```bash
pt --lang go --file user_service_test
pt --lang python --file test_user_service  
pt --lang typescript --file user-service.test
pt --lang java --file UserServiceTest
```

### Template Features
- **AAA Pattern**: Arrange-Act-Assert structure in all templates
- **Best Practices**: Language-specific conventions and patterns
- **VSCode Integration**: Automatic snippet installation
- **Mock Setup**: Pre-configured mock generation utilities

### Generated Structure
Each scaffold includes:
- Main test file with example tests
- Configuration files (go.mod, package.json, pom.xml, requirements.txt)
- Mock generation setup
- VSCode snippets for rapid development

## Getting Started

### 1. Choose Your Language

Select from our supported languages and their respective testing stacks:

- **Go**: Ginkgo + Gomega + GoMock
- **Python**: pytest + assertpy + pytest-mock
- **TypeScript**: Jest + Jest Expect + jest-mock-extended
- **Java**: JUnit 5 + AssertJ + Mockito

### 2. Generate Test Scaffold

Use the `pt` CLI to generate a complete test structure:

```bash
# Generate scaffold for your chosen language
pt --lang <language> --file <test_name>
```

### 3. Set Up Dependencies

Each SDK includes dependency management:

```bash
# Go
go mod tidy

# Python
pip install -r requirements.txt

# TypeScript
npm install

# Java
mvn install
```

### 4. Write Tests

Follow the generated templates and use the provided utilities:

- Use the test harness for dependency management
- Follow the AAA pattern for test structure
- Leverage language-specific assertion libraries
- Use mocking frameworks for external dependencies

## Quick Start Examples

### Go Example
```go
var _ = Describe("UserService", func() {
    var (
        service *UserService
        mockDB  *mocks.MockDatabase
    )

    BeforeEach(func() {
        mockDB = mocks.NewMockDatabase(ctrl)
        service = NewUserService(mockDB)
    })

    It("should get user successfully", func() {
        // Arrange
        mockDB.EXPECT().FindUser("123").Return(&User{ID: "123"}, nil)
        
        // Act
        user, err := service.GetUser("123")
        
        // Assert
        Expect(err).To(BeNil())
        Expect(user.ID).To(Equal("123"))
    })
})
```

### Python Example
```python
class TestUserService:
    @pytest.fixture
    def user_service(self, mocker):
        mock_db = mocker.Mock()
        return UserService(mock_db)
    
    def test_get_user_success(self, user_service, mocker):
        # Arrange
        mocker.patch.object(user_service.db, 'find_user', return_value=User(id="123"))
        
        # Act
        user = user_service.get_user("123")
        
        # Assert
        assert_that(user).is_not_none()
        assert_that(user.id).is_equal_to("123")
```

### TypeScript Example
```typescript
describe('UserService', () => {
    let userService: UserService;
    let mockDb: MockProxy<Database>;

    beforeEach(() => {
        mockDb = mock<Database>();
        userService = new UserService(mockDb);
    });

    it('should get user successfully', async () => {
        // Arrange
        mockDb.findUser.mockResolvedValue({ id: '123', name: 'John' });
        
        // Act
        const user = await userService.getUser('123');
        
        // Assert
        expect(user).toBeDefined();
        expect(user.id).toBe('123');
    });
});
```

### Java Example
```java
@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    @Mock private Database mockDb;
    @InjectMocks private UserService userService;

    @Test
    @DisplayName("Should get user successfully")
    void shouldGetUserSuccessfully() {
        // Arrange
        when(mockDb.findUser("123")).thenReturn(new User("123", "John"));
        
        // Act
        User user = userService.getUser("123");
        
        // Assert
        assertThat(user).isNotNull();
        assertThat(user.getId()).isEqualTo("123");
    }
}
```

## Best Practices

### 1. Test Organization
- Use descriptive test names that explain the scenario
- Group related tests using language-specific constructs
- Follow the AAA (Arrange-Act-Assert) pattern consistently

### 2. Mock Management
- Mock external dependencies, not internal logic
- Use type-safe mocking where available
- Verify mock interactions to ensure correct behavior

### 3. Assertion Guidelines
- Use expressive assertions that clearly communicate intent
- Create custom assertions for domain-specific validations
- Test both positive and negative scenarios

### 4. Test Data Management
- Use builders or factories for complex test data
- Keep test data isolated and independent
- Use meaningful data that reflects real-world scenarios

## Integration with CI/CD

All SDK utilities are designed to work seamlessly with CI/CD pipelines:

```bash
# Run tests in CI
go test ./...           # Go
pytest --cov=src        # Python
npm test -- --coverage # TypeScript
mvn test               # Java
```

## Support and Documentation

For detailed information about each component:

- **SDK Documentation**: See individual SDK documentation pages
- **pt CLI**: Refer to the Platform Tests CLI documentation
- **Framework Guides**: Check language-specific testing guides
- **Best Practices**: Review ADRs and architecture documentation
