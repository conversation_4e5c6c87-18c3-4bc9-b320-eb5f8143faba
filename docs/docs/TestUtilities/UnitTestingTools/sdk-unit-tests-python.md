---
id: sdk-unit-tests-python
title: Python SDK Unit Test Utilities
sidebar_label: Python SDK Unit Tests
---

# Python SDK Unit Test Utilities

The Python SDK provides comprehensive unit testing utilities built around pytest, assertpy assertions, and pytest-mock for mocking. This SDK includes test harness utilities, scaffolding tools, and mock generation capabilities.

## Overview

The Python SDK unit test utilities are located in `SDKs/python/unit-tests/` and provide:

- **Test Harness**: Unified test dependencies management (PostgreSQL, Redis, SMTP, Logging)
- **Assertions**: assertpy library with fluent, chainable assertions
- **Mocking**: pytest-mock utilities and mock generators
- **Scaffolding**: Test template generation and project setup

## Architecture

```
SDKs/python/unit-tests/
├── harness/                    # Test harness utilities
│   ├── harness_service.py      # Example service with dependencies
│   └── harness_utilities.py    # Test harness management
├── assertions/                 # assertpy assertion utilities
├── mocks/                      # pytest-mock utilities and generators
├── scaffolder/                 # Test scaffolding tools
├── harness_test.py            # Example test implementation
└── requirements.txt           # Python dependencies
```

## Test Harness

### HarnessService

The `HarnessService` provides a complete example of a service with common dependencies:

<augment_code_snippet path="SDKs/python/unit-tests/harness/harness_service.py" mode="EXCERPT">
````python
class UserService:
    """Provides user management functionality"""
    
    def __init__(
        self,
        db_connection,
        redis_client: redis.Redis,
        smtp_sender: Callable[[str, List[str], str, str], None],
        logger: logging.Logger,
        cache_timeout: int = 600  # 10 minutes in seconds
    ):
        self.db = db_connection
        self.redis_client = redis_client
        self.smtp_sender = smtp_sender
        self.logger = logger
        self.cache_timeout = cache_timeout
````
</augment_code_snippet>

**Key Features:**
- **Database Integration**: PostgreSQL with psycopg2
- **Caching**: Redis integration with configurable timeout
- **Email Service**: SMTP sender with error handling
- **Logging**: Structured logging with correlation IDs
- **Context Support**: Request tracing and correlation

### Test Harness Utilities

The harness utilities provide mock implementations of all dependencies:

```python
# Example usage in tests
def setup_test_harness():
    harness = TestHarness(
        db=setup_mock_db(),
        redis_client=setup_mock_redis(),
        smtp_sender=setup_mock_smtp(),
        logger=setup_mock_logger(),
    )
    return harness
```

## Assertions with assertpy

### Fluent Assertions

assertpy provides fluent, chainable assertions for better readability:

```python
from assertpy import assert_that

# Basic assertions
assert_that(user.name).is_equal_to("John Doe")
assert_that(user.email).contains("@example.com")
assert_that(users).is_length(3)
assert_that(error).is_none()

# Collection assertions
assert_that(user_ids).contains("user-123")
assert_that(response.users).contains_only(user1, user2, user3)

# String assertions
assert_that(user.email).starts_with("john").ends_with("@example.com")

# Numeric assertions
assert_that(user.age).is_greater_than(18).is_less_than(100)

# Boolean assertions
assert_that(service.is_running()).is_true()
assert_that(user.is_active).is_false()
```

### Custom Assertions

Create domain-specific assertions for better test readability:

```python
def assert_valid_user(user):
    """Custom assertion for user validation"""
    assert_that(user).is_not_none()
    assert_that(user.id).is_not_empty()
    assert_that(user.email).matches(r'^[^@]+@[^@]+\.[^@]+$')
    assert_that(user.name).is_not_empty()
    return user

# Usage in tests
user = service.get_user("123")
assert_valid_user(user)
```

## Mocking with pytest-mock

### Mock Usage in Tests

pytest-mock provides a `mocker` fixture for easy mocking:

```python
import pytest
from unittest.mock import Mock, patch
from assertpy import assert_that

def test_get_user_cache_hit(mocker):
    # Arrange
    mock_redis = mocker.Mock()
    mock_redis.get.return_value = '{"id": "123", "name": "John"}'
    
    mock_db = mocker.Mock()
    mock_logger = mocker.Mock()
    
    service = UserService(
        db_connection=mock_db,
        redis_client=mock_redis,
        smtp_sender=None,
        logger=mock_logger
    )
    
    # Act
    user = service.get_user({}, "123")
    
    # Assert
    assert_that(user).is_not_none()
    assert_that(user.name).is_equal_to("John")
    mock_redis.get.assert_called_once_with("user:123")
    mock_db.execute.assert_not_called()  # Should not hit database
```

### Patching with pytest-mock

```python
def test_send_welcome_email(mocker):
    # Arrange
    mock_smtp = mocker.patch('smtplib.SMTP')
    mock_logger = mocker.Mock()
    
    service = UserService(
        db_connection=None,
        redis_client=None,
        smtp_sender=mock_smtp,
        logger=mock_logger
    )
    
    user = User(id="123", name="John", email="<EMAIL>")
    
    # Act
    result = service.send_welcome_email({}, user)
    
    # Assert
    assert_that(result).is_true()
    mock_smtp.assert_called_once()
```

## Test Scaffolding

### pytest Test Structure

The SDK provides templates following pytest conventions:

```python
import pytest
from assertpy import assert_that
from unittest.mock import Mock

class TestUserService:
    """Test suite for UserService"""
    
    @pytest.fixture
    def mock_dependencies(self, mocker):
        """Setup mock dependencies for tests"""
        return {
            'db': mocker.Mock(),
            'redis': mocker.Mock(),
            'smtp': mocker.Mock(),
            'logger': mocker.Mock()
        }
    
    @pytest.fixture
    def user_service(self, mock_dependencies):
        """Create UserService instance with mocked dependencies"""
        return UserService(**mock_dependencies)
    
    def test_get_user_success(self, user_service, mock_dependencies):
        # Arrange
        expected_user = {"id": "123", "name": "John"}
        mock_dependencies['redis'].get.return_value = None  # Cache miss
        mock_dependencies['db'].fetchone.return_value = expected_user
        
        # Act
        user = user_service.get_user({}, "123")
        
        # Assert
        assert_that(user).is_not_none()
        assert_that(user.name).is_equal_to("John")
```

### AAA Pattern Implementation

Follow the Arrange-Act-Assert pattern in your tests:

```python
def test_create_user_success(self, user_service, mock_dependencies):
    # Arrange
    user_data = {
        "name": "John Doe",
        "email": "<EMAIL>"
    }
    mock_dependencies['db'].execute.return_value = None
    
    # Act
    result = user_service.create_user({}, user_data)
    
    # Assert
    assert_that(result).is_true()
    mock_dependencies['db'].execute.assert_called_once()
```

## Best Practices

### 1. Test Organization
- Use classes to group related tests
- Use descriptive test method names
- Use fixtures for setup and teardown

### 2. Mock Management
- Use `mocker` fixture from pytest-mock
- Patch at the appropriate level
- Verify mock calls with `assert_called_with()`

### 3. Assertion Guidelines
- Use assertpy for fluent, readable assertions
- Chain assertions for complex validations
- Create custom assertion helpers for domain objects

### 4. Fixtures and Test Data
- Use pytest fixtures for reusable test setup
- Parametrize tests for multiple scenarios
- Use meaningful test data that reflects real scenarios

## Integration with pt CLI

Generate Python test scaffolds using the platform tests CLI:

```bash
# Generate basic Python test scaffold
pt --lang python

# Generate with custom test file name
pt --lang python --file test_user_service

# Force regenerate existing scaffold
pt --lang python --force
```

This creates a complete test structure with:
- pytest configuration
- Example test cases
- Mock setup examples
- Requirements.txt with dependencies

## Dependencies

The Python SDK unit tests require these dependencies in `requirements.txt`:

```txt
pytest>=7.0.0
pytest-mock>=3.10.0
assertpy>=1.1
redis>=4.0.0
psycopg2-binary>=2.9.0
```

Install dependencies:

```bash
pip install -r requirements.txt
```

## Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src

# Run specific test file
pytest test_user_service.py

# Run with verbose output
pytest -v

# Run tests matching pattern
pytest -k "test_user"
```
