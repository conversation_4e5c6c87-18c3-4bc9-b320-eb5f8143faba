---
id: platform-tests-cli
title: Platform Tests (pt) CLI Tool
sidebar_label: Platform Tests CLI
---

# Platform Tests (pt) CLI Tool

The Platform Tests CLI (`pt`) is a multi-language test scaffolding and mock generation tool that creates standardized test structures for Go, Python, TypeScript, and Java projects. It provides templates, mock generators, and VSCode integration for streamlined test development.

## Overview

The `pt` CLI tool is located in the `pt/` directory and provides:

- **Test Scaffolding**: Generate language-specific test templates
- **Mock Generation**: Multi-language mock generation utilities
- **VSCode Integration**: Automatic snippet installation
- **Template Management**: Embedded templates for all supported languages

## Architecture

```
pt/
├── main.go                     # CLI tool entry point
├── templates/                  # Language-specific templates
│   ├── go/                    # Go test templates
│   ├── python/                # Python test templates
│   ├── typescript/            # TypeScript test templates
│   └── java/                  # Java test templates
├── mocks/                     # Mock generation utilities
│   ├── generate-mocks.go      # Go mock generator
│   ├── generate-mocks.py      # Python mock generator
│   ├── generate-mocks.ts      # TypeScript mock generator
│   └── pom.xml               # Java mock configuration
├── Dockerfile                 # Container for pt tool
├── Dockerfile.mocks          # Container for mock generation
└── entrypoint.sh             # Container entrypoint
```

## Installation

### Binary Installation

```bash
# Build from source
cd pt/
go build -o pt main.go

# Make executable and add to PATH
chmod +x pt
sudo mv pt /usr/local/bin/
```

### Docker Usage

```bash
# Build the pt container
docker build -t pt-cli .

# Use as alias
alias pt='docker run --rm -v $(pwd):/workspace pt-cli'
```

## Usage

### Basic Commands

```bash
# Show version
pt --version

# Generate test scaffold for specific language
pt --lang go
pt --lang python
pt --lang typescript
pt --lang java

# Generate with custom test file name
pt --lang go --file user_service_test
pt --lang python --file test_user_service
pt --lang typescript --file user-service.test
pt --lang java --file UserServiceTest

# Force regenerate existing scaffold
pt --lang go --force
```

### Command Options

| Option | Description | Example |
|--------|-------------|---------|
| `--lang` | Specify target language | `--lang go` |
| `--file` | Custom test file name (without extension) | `--file my_service_test` |
| `--force` | Force regenerate existing scaffold | `--force` |
| `--version` | Show pt version | `--version` |

## Language Templates

### Go Templates

Located in `pt/templates/go/`:

<augment_code_snippet path="pt/templates/go/example_unit_test.go" mode="EXCERPT">
````go
var _ = Describe("Example Component", func() {
    var (
    // Declare shared variables here
    )

    BeforeEach(func() {
        // Arrange: set up any common state before each test
    })

    Describe("Behavior A", func() {
        Context("when scenario X happens", func() {
            It("should do something expected", func() {
                // Arrange
                // Act
                // Assert
            })
        })
    })
})
````
</augment_code_snippet>

**Generated Structure:**
```
unit-tests/
├── example_unit_test.go       # Main test file (Ginkgo/Gomega)
├── go.mod                     # Go module configuration
├── go.sum                     # Go module checksums
└── .vscode/
    └── pt.code-snippets       # VSCode snippets
```

### Python Templates

Located in `pt/templates/python/`:

**Generated Structure:**
```
unit-tests/
├── example_test.py            # Main test file (pytest)
├── requirements.txt           # Python dependencies
└── .vscode/
    └── pt.code-snippets       # VSCode snippets
```

**Example Test Template:**
```python
import pytest
from assertpy import assert_that

class TestExampleComponent:
    """Test suite for Example Component"""
    
    @pytest.fixture
    def setup_data(self):
        """Setup test data"""
        return {"key": "value"}
    
    def test_example_behavior(self, setup_data):
        # Arrange
        # Act
        # Assert
        assert_that(True).is_true()
```

### TypeScript Templates

Located in `pt/templates/typescript/`:

**Generated Structure:**
```
unit-tests/
├── example.test.ts            # Main test file (Jest)
├── package.json               # Node.js dependencies
├── tsconfig.json              # TypeScript configuration
├── jest.config.js             # Jest configuration
└── .vscode/
    └── pt.code-snippets       # VSCode snippets
```

**Example Test Template:**
```typescript
describe('Example Component', () => {
  let component: ExampleComponent;

  beforeEach(() => {
    // Arrange: setup before each test
  });

  afterEach(() => {
    // Cleanup after each test
  });

  describe('when testing behavior A', () => {
    it('should do something expected', () => {
      // Arrange
      // Act
      // Assert
      expect(true).toBe(true);
    });
  });
});
```

### Java Templates

Located in `pt/templates/java/`:

**Generated Structure:**
```
unit-tests/
├── src/
│   └── test/
│       └── java/
│           └── ExampleTest.java    # Main test file (JUnit 5)
├── pom.xml                         # Maven configuration
└── .vscode/
    └── pt.code-snippets           # VSCode snippets
```

**Example Test Template:**
```java
@ExtendWith(MockitoExtension.class)
@DisplayName("Example Component Tests")
class ExampleTest {
    
    @BeforeEach
    void setUp() {
        // Arrange: setup before each test
    }
    
    @Nested
    @DisplayName("Behavior A Tests")
    class BehaviorATests {
        
        @Test
        @DisplayName("Should do something expected")
        void shouldDoSomethingExpected() {
            // Arrange
            // Act
            // Assert
            assertThat(true).isTrue();
        }
    }
}
```

## Mock Generation

### Multi-Language Mock Generators

The `pt/mocks/` directory contains mock generation utilities for each language:

#### Go Mock Generation (`generate-mocks.go`)
```go
// Generates GoMock mocks for interfaces
//go:generate mockgen -source=service.go -destination=mocks/mock_service.go
```

#### Python Mock Generation (`generate-mocks.py`)
```python
# Generates pytest-mock utilities and fixtures
def generate_mock_fixtures():
    # Mock generation logic
    pass
```

#### TypeScript Mock Generation (`generate-mocks.ts`)
```typescript
// Generates jest-mock-extended mocks
import { mock, MockProxy } from 'jest-mock-extended';

export function createMockService(): MockProxy<Service> {
    return mock<Service>();
}
```

#### Java Mock Generation (`pom.xml`)
```xml
<!-- Maven configuration for Mockito mock generation -->
<plugin>
    <groupId>org.mockito</groupId>
    <artifactId>mockito-maven-plugin</artifactId>
</plugin>
```

## VSCode Integration

### Automatic Snippet Installation

The `pt` tool automatically installs VSCode snippets to enhance development experience:

```bash
# Snippets are automatically copied to .vscode/pt.code-snippets
# when generating scaffolds
```

### Available Snippets

Each language template includes snippets for:

- **Test Structure**: Quick test method/function generation
- **Assertions**: Common assertion patterns
- **Mocks**: Mock setup and configuration
- **Fixtures**: Test data and setup utilities

## Docker Support

### Building Containers

```bash
# Build main pt CLI container
docker build -t pt-cli .

# Build mock generation container
docker build -f Dockerfile.mocks -t pt-mocks .
```

### Container Usage

```bash
# Generate scaffolds in container
docker run --rm -v $(pwd):/workspace pt-cli --lang go

# Generate mocks in container
docker run --rm -v $(pwd):/workspace pt-mocks
```

## Configuration

### Template Customization

Templates are embedded in the binary but can be customized by:

1. Modifying files in `pt/templates/`
2. Rebuilding the `pt` binary
3. Using the updated binary for generation

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PT_OUTPUT_DIR` | Output directory for generated files | `./unit-tests` |
| `PT_FORCE_OVERWRITE` | Force overwrite existing files | `false` |

## Best Practices

### 1. Project Structure
- Run `pt` from your project root directory
- Use consistent naming conventions for test files
- Organize tests in the generated `unit-tests/` directory

### 2. Template Usage
- Start with generated templates and customize as needed
- Follow the AAA (Arrange-Act-Assert) pattern
- Use language-specific best practices

### 3. Mock Generation
- Generate mocks for all external dependencies
- Use type-safe mocking where available
- Keep mocks focused and minimal

### 4. VSCode Integration
- Install the generated snippets for faster development
- Use snippet shortcuts for common patterns
- Customize snippets for your team's conventions

## Troubleshooting

### Common Issues

**Permission Denied:**
```bash
chmod +x pt
```

**Template Not Found:**
```bash
# Ensure language is supported
pt --lang go  # ✓ Supported
pt --lang rust # ✗ Not supported
```

**Force Regeneration:**
```bash
# Use --force to overwrite existing files
pt --lang go --force
```

### Debug Mode

```bash
# Enable verbose output (if implemented)
PT_DEBUG=true pt --lang go
```

## Contributing

### Adding New Language Support

1. Create template directory: `pt/templates/newlang/`
2. Add template files with appropriate extensions
3. Update `main.go` to handle the new language
4. Add mock generation utilities
5. Create VSCode snippets

### Template Guidelines

- Follow language-specific conventions
- Include comprehensive examples
- Provide clear documentation
- Use consistent naming patterns
