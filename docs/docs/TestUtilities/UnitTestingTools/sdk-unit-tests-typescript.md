---
id: sdk-unit-tests-typescript
title: TypeScript SDK Unit Test Utilities
sidebar_label: TypeScript SDK Unit Tests
---

# TypeScript SDK Unit Test Utilities

The TypeScript SDK provides comprehensive unit testing utilities built around Jest, built-in Jest Expect API, and jest-mock-extended for type-safe mocking. This SDK includes test harness utilities, scaffolding tools, and mock generation capabilities.

## Overview

The TypeScript SDK unit test utilities are located in `SDKs/typescript/unit-tests/` and provide:

- **Test Harness**: Unified test dependencies management (PostgreSQL, Redis, SMTP, Logging)
- **Assertions**: Jest Expect API with rich matchers
- **Mocking**: jest-mock-extended for type-safe mocking
- **Scaffolding**: Test template generation and project setup

## Architecture

```
SDKs/typescript/unit-tests/
├── harness/                    # Test harness utilities
│   ├── harness_service.ts      # Example service with dependencies
│   └── harness_utilities.ts    # Test harness management
├── assertions/                 # Jest assertion utilities
├── mocks/                      # jest-mock-extended utilities
├── scaffolder/                 # Test scaffolding tools
└── harness.test.ts            # Example test implementation
```

## Test Harness

### TestHarness Class

The `TestHarness` combines all test dependencies in one easy-to-use class:

<augment_code_snippet path="SDKs/typescript/unit-tests/harness/harness_utilities.ts" mode="EXCERPT">
````typescript
/**
 * TestHarness combines all the test dependencies in one easy-to-use class.
 */
export class TestHarness {
  postgres: FakePostgres;
  redis: FakeRedis;
  logger: FakeLogger;
  smtpSender: FakeSmtpSender;
  useDockerContainers: boolean;

  constructor(useDockerContainers = true) {
    this.useDockerContainers = useDockerContainers;
    this.postgres = new FakePostgres(useDockerContainers, "45432");
    this.redis = new FakeRedis(useDockerContainers, "46379");
    this.logger = new FakeLogger();
    this.smtpSender = new FakeSmtpSender();
  }
````
</augment_code_snippet>

**Key Features:**
- **Database Integration**: PostgreSQL with connection pooling
- **Caching**: Redis integration with configurable timeout
- **Email Service**: SMTP sender with error handling
- **Logging**: Structured logging with correlation IDs
- **Docker Support**: Optional Docker container usage

### Service Example

Example service with common dependencies:

```typescript
interface User {
  id: string;
  name: string;
  email: string;
}

class UserService {
  constructor(
    private db: Database,
    private redis: Redis,
    private smtpSender: SmtpSender,
    private logger: Logger
  ) {}

  async getUser(ctx: Context, userId: string): Promise<User | null> {
    const corrId = getCorrelationId(ctx);
    this.logger.info(`Getting user: ${userId}`, { correlationId: corrId });

    // Check cache first
    const cached = await this.redis.get(`user:${userId}`);
    if (cached) {
      return JSON.parse(cached);
    }

    // Fetch from database
    const user = await this.db.findUser(userId);
    if (user) {
      await this.redis.setex(`user:${userId}`, 600, JSON.stringify(user));
    }

    return user;
  }
}
```

## Assertions with Jest Expect API

### Core Matchers

Jest provides a rich set of built-in matchers:

```typescript
// Basic assertions
expect(user.name).toBe("John Doe");
expect(user.email).toContain("@example.com");
expect(users).toHaveLength(3);
expect(error).toBeNull();

// Object assertions
expect(user).toEqual({
  id: "123",
  name: "John Doe",
  email: "<EMAIL>"
});
expect(user).toMatchObject({
  name: "John Doe",
  email: expect.stringContaining("@example.com")
});

// Array assertions
expect(userIds).toContain("user-123");
expect(users).toEqual(expect.arrayContaining([user1, user2]));

// Promise assertions
await expect(service.getUser("123")).resolves.toBeDefined();
await expect(service.getUser("invalid")).rejects.toThrow();

// Async assertions with polling
await expect(async () => {
  const status = await service.getStatus();
  return status === "ready";
}).toEventuallyBe(true);
```

### Custom Matchers

Extend Jest with custom matchers for domain-specific assertions:

```typescript
expect.extend({
  toBeValidUser(received: any) {
    const pass = received && 
                 typeof received.id === 'string' && 
                 typeof received.email === 'string' && 
                 received.email.includes('@') &&
                 typeof received.name === 'string';

    return {
      message: () => `expected ${received} to be a valid user`,
      pass,
    };
  },
});

// Usage
expect(user).toBeValidUser();
```

## Type-Safe Mocking with jest-mock-extended

### Mock Creation

jest-mock-extended provides type-safe mocks for interfaces and classes:

```typescript
import { mock, MockProxy } from 'jest-mock-extended';

interface Database {
  findUser(id: string): Promise<User | null>;
  saveUser(user: User): Promise<void>;
}

interface Redis {
  get(key: string): Promise<string | null>;
  setex(key: string, ttl: number, value: string): Promise<void>;
}

describe('UserService', () => {
  let mockDb: MockProxy<Database>;
  let mockRedis: MockProxy<Redis>;
  let mockLogger: MockProxy<Logger>;
  let userService: UserService;

  beforeEach(() => {
    mockDb = mock<Database>();
    mockRedis = mock<Redis>();
    mockLogger = mock<Logger>();
    userService = new UserService(mockDb, mockRedis, null, mockLogger);
  });

  it('should get user from cache', async () => {
    // Arrange
    const userId = "123";
    const cachedUser = JSON.stringify({ id: "123", name: "John" });
    mockRedis.get.mockResolvedValue(cachedUser);

    // Act
    const user = await userService.getUser({}, userId);

    // Assert
    expect(user).toEqual({ id: "123", name: "John" });
    expect(mockRedis.get).toHaveBeenCalledWith("user:123");
    expect(mockDb.findUser).not.toHaveBeenCalled();
  });
});
```

### Deep Mocking

Mock nested objects and complex structures:

```typescript
const mockService = mock<ComplexService>({
  config: {
    database: {
      host: "localhost",
      port: 5432
    }
  }
});

// Access nested properties safely
expect(mockService.config.database.host).toBe("localhost");
```

## Test Scaffolding

### Jest Test Structure

The SDK provides templates following Jest conventions:

```typescript
import { mock, MockProxy } from 'jest-mock-extended';

describe('UserService', () => {
  let mockDependencies: {
    db: MockProxy<Database>;
    redis: MockProxy<Redis>;
    smtp: MockProxy<SmtpSender>;
    logger: MockProxy<Logger>;
  };
  let userService: UserService;

  beforeEach(() => {
    mockDependencies = {
      db: mock<Database>(),
      redis: mock<Redis>(),
      smtp: mock<SmtpSender>(),
      logger: mock<Logger>()
    };

    userService = new UserService(
      mockDependencies.db,
      mockDependencies.redis,
      mockDependencies.smtp,
      mockDependencies.logger
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getUser', () => {
    it('should return user from cache when available', async () => {
      // Arrange
      const userId = "123";
      const cachedUser = { id: "123", name: "John" };
      mockDependencies.redis.get.mockResolvedValue(JSON.stringify(cachedUser));

      // Act
      const result = await userService.getUser({}, userId);

      // Assert
      expect(result).toEqual(cachedUser);
      expect(mockDependencies.redis.get).toHaveBeenCalledWith("user:123");
    });

    it('should fetch from database on cache miss', async () => {
      // Arrange
      const userId = "123";
      const dbUser = { id: "123", name: "John" };
      mockDependencies.redis.get.mockResolvedValue(null);
      mockDependencies.db.findUser.mockResolvedValue(dbUser);

      // Act
      const result = await userService.getUser({}, userId);

      // Assert
      expect(result).toEqual(dbUser);
      expect(mockDependencies.db.findUser).toHaveBeenCalledWith(userId);
      expect(mockDependencies.redis.setex).toHaveBeenCalled();
    });
  });
});
```

### AAA Pattern Implementation

Follow the Arrange-Act-Assert pattern in your tests:

```typescript
it('should create user successfully', async () => {
  // Arrange
  const userData = {
    name: "John Doe",
    email: "<EMAIL>"
  };
  mockDependencies.db.saveUser.mockResolvedValue(undefined);

  // Act
  const result = await userService.createUser({}, userData);

  // Assert
  expect(result).toBe(true);
  expect(mockDependencies.db.saveUser).toHaveBeenCalledWith(
    expect.objectContaining({
      name: "John Doe",
      email: "<EMAIL>",
      id: expect.any(String)
    })
  );
});
```

## Best Practices

### 1. Test Organization
- Use `describe` blocks to group related tests
- Use descriptive test names that explain the scenario
- Use `beforeEach` and `afterEach` for setup/teardown

### 2. Mock Management
- Use jest-mock-extended for type-safe mocks
- Clear mocks between tests with `jest.clearAllMocks()`
- Mock at the appropriate abstraction level

### 3. Assertion Guidelines
- Use Jest's built-in matchers for clarity
- Create custom matchers for domain-specific validations
- Use `expect.objectContaining()` for partial object matching

### 4. Async Testing
- Always use `async/await` for asynchronous tests
- Use `resolves` and `rejects` matchers for promises
- Test both success and error scenarios

## Integration with pt CLI

Generate TypeScript test scaffolds using the platform tests CLI:

```bash
# Generate basic TypeScript test scaffold
pt --lang typescript

# Generate with custom test file name
pt --lang typescript --file user-service.test

# Force regenerate existing scaffold
pt --lang typescript --force
```

This creates a complete test structure with:
- Jest configuration
- TypeScript configuration
- Example test cases
- Package.json with dependencies

## Dependencies

The TypeScript SDK unit tests require these dependencies in `package.json`:

```json
{
  "devDependencies": {
    "@types/jest": "^29.5.14",
    "@types/node": "^20.12.10",
    "jest": "^29.7.0",
    "jest-mock-extended": "^3.0.0",
    "ts-jest": "^29.3.1",
    "typescript": "^5.8.3"
  }
}
```

Install dependencies:

```bash
npm install
# or
yarn install
```

## Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm test -- --watch

# Run tests with coverage
npm test -- --coverage

# Run specific test file
npm test user-service.test.ts

# Run tests matching pattern
npm test -- --testNamePattern="should get user"
```
