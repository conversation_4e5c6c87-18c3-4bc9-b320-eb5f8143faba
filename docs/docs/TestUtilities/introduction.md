---
id: introduction
title: Introduction
sidebar_label: Introduction
---

# Introduction

This SDK provides a framework for containerizing services and managing their lifecycle using a system of “bricks”. It abstracts the complexity of setting up Docker containers with custom configurations through a set of well‐defined APIs and integration with the [testcontainers](https://testcontainers.com/) library. The goal is to enable developers to easily spin up containerized environments for integration testing, development, and performance testing.

The core functionalities include:

- **Dynamic Configuration:** Utilize the `BrickConfig` structure to define container parameters such as environment variables, port mappings, volumes, health-checks, and custom waiting strategies.
- **Lifecycle Management:** Implement the `ContainerBrick` interface with methods to `Start` and `Stop` containers. The implementation is provided by the `GenericContainerBrick`.
- **Infrastructure Orchestration:** Leverage the `InfrastructureManager` to handle multiple bricks collectively, allowing for sequential or parallel startup and graceful teardown.
- **Network Management:** Create and manage Docker networks using the provided network utilities.

This introduction outlines the high-level purpose of the SDK system and prepares you for a deeper dive into its architecture and usage.

