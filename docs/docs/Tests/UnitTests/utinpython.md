## Testing Framework Overview

### pytest

pytest is a powerful, flexible testing framework for Python that makes it easy to write small, readable tests. Key features include:

- Simple syntax for writing tests
- Detailed information on failing assertions
- Auto-discovery of test modules and functions
- Fixtures for setup and teardown
- Parameterized testing
- Extensive plugin ecosystem

### pytest-mock

pytest-mock is a thin wrapper around the mock package from the Python standard library, providing a mocker fixture that helps with mocking objects. Key features include:

- Easy creation of mock objects
- Simplified patching of functions and methods
- Tracking of mock calls and assertions
- Integration with pytest fixtures

### assertpy

assertpy is a fluent assertion library for Python that provides a more readable and expressive way to write assertions. Key features include:

- Fluent, chainable assertions
- Rich set of assertions for different types
- Custom error messages
- Support for collections, strings, numbers, and more

## Setting Up Your Testing Environment

### Installing Dependencies

```bash
# Install pytest, pytest-mock, and assertpy
pip install pytest pytest-mock assertpy

# For a project, add to requirements-dev.txt or setup.py
```

### Project Structure

A typical Python project structure for tests:

```
my_project/
├── my_package/
│   ├── __init__.py
│   ├── module1.py
│   └── module2.py
└── tests/
    ├── __init__.py
    ├── test_module1.py
    └── test_module2.py
```

### Basic Configuration

Create a `pytest.ini` or `conftest.py` file in your project root:

```ini
# pytest.ini
[pytest]
testpaths = tests
python_files = test_*.py
python_functions = test_*
```

```python
# conftest.py
import pytest

# Define fixtures that can be used across multiple test files
@pytest.fixture
def sample_data():
    return {"key": "value"}
```

## The AAA (Arrange-Act-Assert) Pattern

The AAA pattern is a common approach to structuring unit tests:

### 1. Arrange

Set up the test environment and prepare the data needed for the test:
- Create instances of the objects you need
- Set up mock objects and their expected behavior
- Prepare input data

### 2. Act

Execute the code being tested:
- Call the function or method you're testing
- Capture the result or exception

### 3. Assert

Verify that the code behaved as expected:
- Check return values
- Verify that mocks were called as expected
- Ensure exceptions were raised (or not raised) as expected

## Step-by-Step Guide to Writing Unit Tests

### 1. Create a Test File

For a module named `user_service.py`, create a corresponding `test_user_service.py`:

```python
# tests/test_user_service.py
import pytest
from pytest_mock import MockerFixture
from assertpy import assert_that

from my_package.user_service import UserService
from my_package.models import User
```

### 2. Set Up Test Fixtures

```python
# tests/test_user_service.py
import pytest
from pytest_mock import MockerFixture
from assertpy import assert_that

from my_package.user_service import UserService
from my_package.models import User
from my_package.exceptions import UserNotFoundError

@pytest.fixture
def user_repository_mock(mocker: MockerFixture):
    return mocker.Mock()

@pytest.fixture
def user_service(user_repository_mock):
    return UserService(user_repository_mock)

@pytest.fixture
def test_user():
    return User(
        id="user123",
        name="Test User",
        email="<EMAIL>"
    )
```

### 3. Write Test Functions Following AAA Pattern

```python
def test_get_user_by_id_when_user_exists(user_service, user_repository_mock, test_user):
    # ARRANGE
    user_repository_mock.get_by_id.return_value = test_user
    
    # ACT
    result = user_service.get_user_by_id("user123")
    
    # ASSERT
    assert_that(result).is_equal_to(test_user)
    user_repository_mock.get_by_id.assert_called_once_with("user123")

def test_get_user_by_id_when_user_does_not_exist(user_service, user_repository_mock):
    # ARRANGE
    user_repository_mock.get_by_id.return_value = None
    
    # ACT & ASSERT
    with pytest.raises(UserNotFoundError) as excinfo:
        user_service.get_user_by_id("nonexistent")
    
    assert_that(str(excinfo.value)).contains("nonexistent")
    user_repository_mock.get_by_id.assert_called_once_with("nonexistent")
```

### 4. Testing Error Handling

```python
def test_create_user_when_repository_succeeds(user_service, user_repository_mock, test_user):
    # ARRANGE
    user_repository_mock.create.return_value = test_user
    
    # ACT
    result = user_service.create_user(test_user)
    
    # ASSERT
    assert_that(result).is_equal_to(test_user)
    user_repository_mock.create.assert_called_once_with(test_user)

def test_create_user_when_repository_fails(user_service, user_repository_mock, test_user):
    # ARRANGE
    user_repository_mock.create.side_effect = Exception("Database error")
    
    # ACT & ASSERT
    with pytest.raises(Exception) as excinfo:
        user_service.create_user(test_user)
    
    assert_that(str(excinfo.value)).contains("Database error")
    user_repository_mock.create.assert_called_once_with(test_user)
```

### 5. Running Your Tests

```bash
# Run all tests
pytest

# Run tests with verbose output
pytest -v

# Run a specific test file
pytest tests/test_user_service.py

# Run a specific test function
pytest tests/test_user_service.py::test_get_user_by_id_when_user_exists

# Generate coverage report
pytest --cov=my_package
```

## Advanced Testing Techniques

### Parameterized Tests

```python
@pytest.mark.parametrize("name,email,expected_valid", [
    ("John", "<EMAIL>", True),
    ("", "<EMAIL>", False),
    ("John", "", False),
    ("John", "not-an-email", False),
])
def test_validate_user_with_different_inputs(user_service, name, email, expected_valid):
    # ARRANGE
    user = User(id="test", name=name, email=email)
    
    # ACT
    result = user_service.validate_user(user)
    
    # ASSERT
    assert_that(result).is_equal_to(expected_valid)
```

### Testing Asynchronous Code

```python
import asyncio

@pytest.mark.asyncio
async def test_process_user_async(user_service, user_repository_mock, test_user):
    # ARRANGE
    user_repository_mock.get_by_id.return_value = test_user
    user_repository_mock.process_async.return_value = asyncio.Future()
    user_repository_mock.process_async.return_value.set_result(test_user)
    
    # ACT
    result = await user_service.process_user_async("user123")
    
    # ASSERT
    assert_that(result).is_equal_to(test_user)
    user_repository_mock.get_by_id.assert_called_once_with("user123")
    user_repository_mock.process_async.assert_called_once_with(test_user)
```

### Testing with Context Managers

```python
def test_user_service_with_context_manager(user_repository_mock):
    # ARRANGE
    with UserService(user_repository_mock) as service:
        # ACT
        service.initialize()
        
        # ASSERT
        user_repository_mock.connect.assert_called_once()
    
    # Assert after context manager exits
    user_repository_mock.disconnect.assert_called_once()
```

### Mocking Complex Dependencies

```python
def test_send_welcome_email(user_service, user_repository_mock, mocker, test_user):
    # ARRANGE
    email_service_mock = mocker.patch('my_package.email_service.EmailService')
    email_instance = email_service_mock.return_value
    
    user_repository_mock.get_by_id.return_value = test_user
    
    # ACT
    user_service.send_welcome_email("user123")
    
    # ASSERT
    user_repository_mock.get_by_id.assert_called_once_with("user123")
    email_instance.send.assert_called_once()
    call_args = email_instance.send.call_args[0]
    assert_that(call_args[0]).is_equal_to(test_user.email)
    assert_that(call_args[1]).contains("Welcome")
```

## Example: Complete Test File

Here's a complete example of a test file for a user service:

```python
# tests/test_user_service.py
import pytest
from pytest_mock import MockerFixture
from assertpy import assert_that

from my_package.user_service import UserService
from my_package.models import User
from my_package.exceptions import UserNotFoundError

@pytest.fixture
def user_repository_mock(mocker: MockerFixture):
    return mocker.Mock()

@pytest.fixture
def user_service(user_repository_mock):
    return UserService(user_repository_mock)

@pytest.fixture
def test_user():
    return User(
        id="user123",
        name="Test User",
        email="<EMAIL>"
    )

def test_get_user_by_id_when_user_exists(user_service, user_repository_mock, test_user):
    # ARRANGE
    user_repository_mock.get_by_id.return_value = test_user
    
    # ACT
    result = user_service.get_user_by_id("user123")
    
    # ASSERT
    assert_that(result).is_equal_to(test_user)
    user_repository_mock.get_by_id.assert_called_once_with("user123")

def test_get_user_by_id_when_user_does_not_exist(user_service, user_repository_mock):
    # ARRANGE
    user_repository_mock.get_by_id.return_value = None
    
    # ACT & ASSERT
    with pytest.raises(UserNotFoundError) as excinfo:
        user_service.get_user_by_id("nonexistent")
    
    assert_that(str(excinfo.value)).contains("nonexistent")
    user_repository_mock.get_by_id.assert_called_once_with("nonexistent")

def test_create_user_when_repository_succeeds(user_service, user_repository_mock, test_user):
    # ARRANGE
    user_repository_mock.create.return_value = test_user
    
    # ACT
    result = user_service.create_user(test_user)
    
    # ASSERT
    assert_that(result).is_equal_to(test_user)
    user_repository_mock.create.assert_called_once_with(test_user)

def test_create_user_when_repository_fails(user_service, user_repository_mock, test_user):
    # ARRANGE
    user_repository_mock.create.side_effect = Exception("Database error")
    
    # ACT & ASSERT
    with pytest.raises(Exception) as excinfo:
        user_service.create_user(test_user)
    
    assert_that(str(excinfo.value)).contains("Database error")
    user_repository_mock.create.assert_called_once_with(test_user)

def test_update_user_when_user_exists(user_service, user_repository_mock, test_user):
    # ARRANGE
    existing_user = User(
        id="user123",
        name="Old Name",
        email="<EMAIL>"
    )
    
    updated_user = User(
        id="user123",
        name="New Name",
        email="<EMAIL>"
    )
    
    user_repository_mock.get_by_id.return_value = existing_user
    user_repository_mock.update.return_value = updated_user
    
    # ACT
    result = user_service.update_user("user123", updated_user)
    
    # ASSERT
    assert_that(result).is_equal_to(updated_user)
    user_repository_mock.get_by_id.assert_called_once_with("user123")
    user_repository_mock.update.assert_called_once_with(updated_user)

def test_update_user_when_user_does_not_exist(user_service, user_repository_mock, test_user):
    # ARRANGE
    user_repository_mock.get_by_id.return_value = None
    
    # ACT & ASSERT
    with pytest.raises(UserNotFoundError) as excinfo:
        user_service.update_user("nonexistent", test_user)
    
    assert_that(str(excinfo.value)).contains("nonexistent")
    user_repository_mock.get_by_id.assert_called_once_with("nonexistent")
    user_repository_mock.update.assert_not_called()

def test_delete_user_when_user_exists(user_service, user_repository_mock, test_user):
    # ARRANGE
    user_repository_mock.get_by_id.return_value = test_user
    
    # ACT
    user_service.delete_user("user123")
    
    # ASSERT
    user_repository_mock.get_by_id.assert_called_once_with("user123")
    user_repository_mock.delete.assert_called_once_with("user123")

def test_delete_user_when_user_does_not_exist(user_service, user_repository_mock):
    # ARRANGE
    user_repository_mock.get_by_id.return_value = None
    
    # ACT & ASSERT
    with pytest.raises(UserNotFoundError) as excinfo:
        user_service.delete_user("nonexistent")
    
    assert_that(str(excinfo.value)).contains("nonexistent")
    user_repository_mock.get_by_id.assert_called_once_with("nonexistent")
    user_repository_mock.delete.assert_not_called()

def test_get_all_users(user_service, user_repository_mock):
    # ARRANGE
    user1 = User(id="user1", name="User One", email="<EMAIL>")
    user2 = User(id="user2", name="User Two", email="<EMAIL>")
    users = [user1, user2]
    
    user_repository_mock.get_all.return_value = users
    
    # ACT
    result = user_service.get_all_users()
    
    # ASSERT
    assert_that(result).is_length(2)
    assert_that(result).contains(user1)
    assert_that(result).contains(user2)
    user_repository_mock.get_all.assert_called_once()

@pytest.mark.parametrize("name,email,expected_valid", [
    ("John", "<EMAIL>", True),
    ("", "<EMAIL>", False),
    ("John", "", False),
    ("John", "not-an-email", False),
])
def test_validate_user_with_different_inputs(user_service, name, email, expected_valid):
    # ARRANGE
    user = User(id="test", name=name, email=email)
    
    # ACT
    result = user_service.validate_user(user)
    
    # ASSERT
    assert_that(result).is_equal_to(expected_valid)
```

By following this guide, you'll be able to write effective, maintainable unit tests for your Python applications using pytest, pytest-mock, and assertpy while adhering to the AAA pattern.
