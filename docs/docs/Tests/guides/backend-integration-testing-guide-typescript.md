## SDK Integration Components

### 1. Test Container Bricks
Our SDK provides pre-configured container "bricks" that can be reused across projects:

```typescript
import { BrickConfig, HealthCheck } from "./testcontainers/brick_config";
import { InfrastructureManager } from "./testcontainers/infrastructure_manager";

function setupTestInfrastructure(): InfrastructureManager {
    const postgresConfig = new BrickConfig({
        brickType: "postgres",
        instanceName: "test-db",
        exposedPort: "5432/tcp",
        hostPort: "5432",
        environmentVariables: {
            POSTGRES_DB: "testdb",
            POSTGRES_USER: "test",
            POSTGRES_PASSWORD: "test"
        }
    });
    
    const redisConfig = new BrickConfig({
        brickType: "redis",
        instanceName: "test-cache",
        exposedPort: "6379/tcp",
        hostPort: "6379"
    });
    
    return new InfrastructureManager([postgresConfig, redisConfig]);
}
```

### 2. Dapr Integration Testing
Using our Dapr helper for service integration tests:

```typescript
import { Dap<PERSON><PERSON><PERSON><PERSON> } from './helpers/dapr_helper';

async function testServiceIntegration(daprClient: DaprHelper): Promise<void> {
    // Publish test message
    await daprClient.publishMessage({
        topic: "user-events",
        message: { type: "user.created", userId: "123" }
    });
    
    // Verify state
    const state = await daprClient.getState("user-123");
    expect(state.status).toBe("active");
}
```

## Writing Tests with Jest

### 1. Test Structure
```typescript
import { BrickConfig } from "./testcontainers/brick_config";
import { InfrastructureManager } from "./testcontainers/infrastructure_manager";
import { DaprHelper } from "./helpers/dapr_helper";

describe('UserService', () => {
    let infrastructure: InfrastructureManager;
    let daprClient: DaprHelper;

    beforeAll(async () => {
        infrastructure = setupTestInfrastructure();
        await infrastructure.startAll();
    });

    afterAll(async () => {
        await infrastructure.stopAll();
    });

    beforeEach(() => {
        daprClient = new DaprHelper();
    });

    it('should create user', async () => {
        const response = await daprClient.invokeService({
            appId: "user-service",
            methodName: "create_user",
            data: { name: "Test User" }
        });
        
        expect(response).toHaveProperty('userId');
    });
});
```

### 2. Common Test Scenarios

#### Database Operations
```typescript
import { BrickConfig } from "./testcontainers/brick_config";
import { Pool } from 'pg';

describe('DatabaseOperations', () => {
    let infrastructure: InfrastructureManager;
    let pool: Pool;

    beforeAll(async () => {
        const config = new BrickConfig({
            brickType: "postgres",
            instanceName: "test-db",
            exposedPort: "5432/tcp",
            environmentVariables: {
                POSTGRES_DB: "testdb",
                POSTGRES_USER: "test",
                POSTGRES_PASSWORD: "test"
            }
        });
        
        infrastructure = new InfrastructureManager([config]);
        await infrastructure.startAll();
        
        pool = new Pool({
            host: 'localhost',
            port: 5432,
            database: 'testdb',
            user: 'test',
            password: 'test'
        });
    });

    afterAll(async () => {
        await pool.end();
        await infrastructure.stopAll();
    });

    it('should handle database transaction', async () => {
        const client = await pool.connect();
        try {
            await client.query('BEGIN');
            await client.query('INSERT INTO users (name) VALUES ($1)', ['Test User']);
            const result = await client.query('SELECT * FROM users WHERE name = $1', ['Test User']);
            await client.query('COMMIT');
            
            expect(result.rows[0]).toBeTruthy();
        } catch (e) {
            await client.query('ROLLBACK');
            throw e;
        } finally {
            client.release();
        }
    });
});
```

#### Service Integration
```typescript
import { HttpStrategy } from "./testcontainers/wait";

it('should handle service communication', async () => {
    // Publish event
    await daprClient.publishMessage({
        topic: "user-events",
        message: {
            type: "user.created",
            payload: {
                userId: "123",
                role: "admin"
            }
        }
    });
    
    // Wait for processing using custom strategy
    const checkPermissions = new HttpStrategy(
        "3500",
        "/v1.0/state/user-permissions-123",
        200,
        5000
    );
    
    await checkPermissions.waitUntilReady(infrastructure.getContainer("user-service"));
    
    const state = await daprClient.getState("user-permissions-123");
    expect(state.role).toBe("admin");
});
```

## SDK Test Components

### 1. Infrastructure Manager Usage
```typescript
import { BrickConfig, HealthCheck } from "./testcontainers/brick_config";
import { InfrastructureManager } from "./testcontainers/infrastructure_manager";

function createTestInfrastructure(): InfrastructureManager {
    const configs = [
        new BrickConfig({
            brickType: "dapr",
            instanceName: "dapr-service1",
            commandArguments: [
                "daprd",
                "--app-id", "service1",
                "--app-port", "5001"
            ],
            exposedPort: "3500/tcp",
            hostPort: "3500",
            healthCheck: new HealthCheck({
                test: ["CMD", "wget", "--spider", "http://localhost:3500/v1.0/healthz"],
                interval: 1,
                retries: 30,
                startPeriod: 5,
                timeout: 1
            })
        })
    ];
    return new InfrastructureManager(configs);
}
```

### 2. Test Helpers
```typescript
import { TestDataBuilder } from './helpers/test_data';
import { ServiceAssertions } from './helpers/assertions';

describe('TestHelpers', () => {
    let testData: TestDataBuilder;
    let assertions: ServiceAssertions;

    beforeEach(() => {
        testData = new TestDataBuilder()
            .withUser()
            .withPermissions()
            .withAuditLog();
            
        assertions = new ServiceAssertions();
    });
});
```

## Project Integration

### 1. Dependencies Setup
```json
{
  "devDependencies": {
    "@types/jest": "^29.5.14",
    "@types/node": "^20.12.10",
    "jest": "^29.7.0",
    "ts-jest": "^29.3.1",
    "typescript": "^5.8.3",
    "jest-mock-extended": "^3.0.0"
  },
  "dependencies": {
    "company-platform-tests-sdk": "^2.0.0"
  }
}
```

### 2. Test Configuration
```typescript
// jest.config.ts
import type { Config } from '@jest/types';

const config: Config.InitialOptions = {
    preset: 'ts-jest',
    testEnvironment: 'node',
    testMatch: ['**/*.test.ts'],
    setupFilesAfterEnv: ['./jest.setup.ts']
};

export default config;
```

## Best Practices

### 1. SDK Usage Guidelines
- Use SDK-provided bricks for infrastructure setup
- Leverage Jest's beforeAll/afterAll for resource management
- Follow established testing patterns
- Use strong typing for all test components

### 2. Test Organization
```typescript
import { InfrastructureManager } from "./testcontainers/infrastructure_manager";
import { DaprHelper } from "./helpers/dapr_helper";

describe('UserService Integration Tests', () => {
    let infrastructure: InfrastructureManager;
    let daprClient: DaprHelper;

    beforeAll(async () => {
        infrastructure = setupTestInfrastructure();
        await infrastructure.startAll();
    });

    afterAll(async () => {
        await infrastructure.stopAll();
    });

    it('should start service correctly', async () => {
        expect(await infrastructure.isHealthy()).toBe(true);
    });

    it('should create user', async () => {
        // Test implementation
    });
});
```

### 3. CI/CD Integration
```yaml
# .github/workflows/integration-tests.yml
name: Integration Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
      
      - name: Install dependencies
        run: npm install
      
      - name: Run Tests
        run: npm test -- --ci --coverage
```
