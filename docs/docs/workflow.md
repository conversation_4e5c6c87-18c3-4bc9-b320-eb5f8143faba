# QA Workflow


<!-- QA Workflow planuml diagram -->
```plantuml
@startuml
left to right direction

' Define the Pipeline Fail block (outside all pipelines)
rectangle "Pipeline Fail" as PipelineFail

' Group Development Pipeline components
package "Development Pipeline" {
    ' Arrange components vertically within the package
    top to bottom direction
    rectangle "Dev PR" as Dev<PERSON>
    rectangle "Has Unit Tests?" as UnitTestsCheck
    rectangle "Run Unit Tests" as RunUnitTests
    rectangle "Unit Tests Pass?" as UnitTestsPass
    rectangle "Check Code Coverage" as CheckCoverage
    rectangle "Coverage >= Baseline?" as CoverageCheck
    rectangle "Check Code Duplication" as CheckDuplication
    rectangle "Duplication < 3%?" as DuplicationCheck
    rectangle "Run SonarQube Analysis" as SonarQube
    rectangle "Manual Analysis of SonarQube Results" as ManualSonarQube
    rectangle "Run API Tests" as APITests
    rectangle "Run DB Tests" as DBTests
    rectangle "Code Review and Merge to Dev env" as MergeToDev

    ' Define the flow within the Development Pipeline
    DevPR --> UnitTestsCheck
    UnitTestsCheck --> RunUnitTests : Yes
    UnitTestsCheck --> [Pipeline Fail] : No
    RunUnitTests --> UnitTestsPass
    UnitTestsPass --> CheckCoverage : Yes
    UnitTestsPass --> [Pipeline Fail] : No
    CheckCoverage --> CoverageCheck
    CoverageCheck --> CheckDuplication : Yes
    CoverageCheck --> [Pipeline Fail] : No
    CheckDuplication --> DuplicationCheck
    DuplicationCheck --> SonarQube : Yes
    DuplicationCheck --> [Pipeline Fail] : No
    SonarQube --> ManualSonarQube
    ManualSonarQube --> APITests
    APITests --> DBTests
    DBTests --> MergeToDev
    APITests --> [Pipeline Fail] : No
    DBTests --> [Pipeline Fail] : No
}

' Transition to Staging Pipeline
rectangle "Deployed to Development" as DeployedDev
MergeToDev --> DeployedDev
DeployedDev --> PRStaging

' Group Staging Pipeline components
package "Staging Pipeline" {
    ' Arrange components vertically within the package
    top to bottom direction
    rectangle "Create PR for STG env" as PRStaging2
    rectangle "PR Passed?" as PRStagingPass
    rectangle "Merge to STG" as MergeToStaging
    rectangle "Rerun All Tests" as RerunTestsStaging
    rectangle "Run E2E Tests" as RunE2ETests
    rectangle "All Tests Passed?" as AllTestsStagingPass
    rectangle "Run Performance Tests" as PerformanceTests
    rectangle "Performance Acceptable?" as PerformanceCheck
    rectangle "Publish Reports to Docusaurus" as PublishReports

    ' Define the flow within the Staging Pipeline
    PRStaging --> PRStagingPass
    PRStagingPass --> MergeToStaging : Yes
    PRStagingPass --> [Pipeline Fail] : No
    MergeToStaging --> RerunTestsStaging
    RerunTestsStaging --> RunE2ETests
    RunE2ETests --> AllTestsStagingPass
    AllTestsStagingPass --> PerformanceTests : Yes
    AllTestsStagingPass --> [Pipeline Fail] : No
    PerformanceTests --> PerformanceCheck
    PerformanceCheck --> PublishReports : Yes
    PerformanceCheck --> [Pipeline Fail] : No
}

' Transition to Production Pipeline
rectangle "Deployed to Staging" as DeployedStaging
PublishReports --> DeployedStaging
DeployedStaging --> PRProduction

' Group Production Pipeline components
package "Production Pipeline" {
    ' Arrange components vertically within the package
    top to bottom direction
    rectangle "Create PR for PROD env" as PRProduction2
    rectangle "PR Passed?" as PRProductionPass
    rectangle "Rerun All Tests" as RerunTestsProduction
    rectangle "Merge to Prod" as MergeToProd

    ' Define the flow within the Production Pipeline
    PRProduction --> PRProductionPass
    PRProductionPass --> RerunTestsProduction : Yes
    PRProductionPass --> [Pipeline Fail] : No
    RerunTestsProduction --> MergeToProd
}

' Final deployment
rectangle "Deployed to Production" as DeployedProd
MergeToProd --> DeployedProd

@enduml
