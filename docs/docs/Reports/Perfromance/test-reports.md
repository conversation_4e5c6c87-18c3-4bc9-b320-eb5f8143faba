# Test Reports

This document contains links to the test reports for various configurations and services tested within the project. Each report includes a description of the test setup, expected outcomes, and the actual results.

---

## Reports

### 1. 1 Service Without Dapr
- **Description**: This test evaluates the behavior and performance of a single service **without Dapr**. The goal is to see how the service behaves when running independently without using Dapr for service discovery and communication.
- **Link**: <a href="/Reports/summary.html">View Report Results</a>

### 2. 1 Service With Dapr
- **Description**: This test evaluates the same service **with Dapr** integrated. It aims to compare the performance and behavior of the service when Dapr is used for service discovery and communication.
- **Link**: <a href="/Reports/summaryDapr.html">View Report Results</a>

### 3. 2 Services Without Dapr
- **Description**: This test evaluates the behavior of **two services running independently without Dapr**. The goal is to compare how services communicate without Dapr in place.
- **Link**: <a href="/Reports/microServicesNoDapr.html">View Report Results</a>

### 4. 2 Services: S1 in Dapr, S2 Not – S1 Sends Request to S2 via HTTPS through Dapr
- **Description**: This test examines a scenario where Service 1 (S1) runs with Dapr and sends a request via HTTPS to Service 2 (S2) which is not using Dapr.
- **Link**: <a href="/Reports/microServicesDapr.html">View Report Results</a>

### 5. 2 Services: Both in Dapr – S1 Sends Request to S2 via HTTP through Dapr
- **Description**: This test checks the scenario where both Service 1 (S1) and Service 2 (S2) are running with Dapr, and S1 sends an HTTP request to S2 via Dapr.
- **Link**: <a href="/Reports/bothMicroServicesDapr.html">View Report Results</a>

### 6. 2 Services: Both in Dapr – S1 Sends Request to S2 via gRPC through Dapr
- **Description**: This test evaluates the behavior when both Service 1 (S1) and Service 2 (S2) are running with Dapr, and S1 sends a gRPC request to S2 via Dapr.
- **Link**: <a href="/Reports/bothMicroServicesDaprgRPC.html">View Report Results</a>

---
## Performance Reports
    ---

## Conclusion

This document provides links to the detailed reports for each of the test scenarios. The results of each test will help us understand how our services behave with and without Dapr integration and under various configurations. The links above will direct you to the full test results for each scenario.

---
