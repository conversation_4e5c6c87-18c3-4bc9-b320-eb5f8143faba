# QA Team – Automation Collaboration Workflow

## Purpose

Provide a clear, shared view of how the QA team collaborates with development and technical leadership across Heart+, Platform, and TMP projects to deliver automated tests **and** reusable testing utilities.

## Scope

* **Projects**: Heart+, Platform, TMP
* **Shared repo**: `platform-tests` (centralised automation utilities)
* **Issue tracker**: Jira (custom workflow)
* **CI/CD**: Central pipeline (GitLab/GitHub Actions)

## High‑Level Process

1. **Developer** opens a pull‑request (PR).
2. **Tech Lead** reviews & approves the PR, then moves the Jira ticket from **Code Review → Ready for QA**.
3. Ticket auto‑assigns to **QA Team**.
4. **QA Engineer** writes/updates automated tests while the branch is in code‑freeze.
5. If a reusable capability is identified, QA implements it in **`platform-tests`**, releases a new version, and consumes it in the project repo.
6. When all tests pass, QA merges to `develop`; CI runs regression and the ticket is closed.

---

## Sequence Diagrams

### 1. Standard Ticket Flow

```plantuml
@startuml
actor "<PERSON><PERSON><PERSON>" as Dev
actor "<PERSON> Lead" as TL
participant "Jira / Board" as Jira
participant "QA Team" as QA
database "Project Repo\n(Heart+, Platform, TMP)" as Repo
participant "CI/CD Pipeline" as CI

Dev -> Repo : Open Pull‑Request
TL  -> Repo : Review & approve
TL  -> Jira : Code Review → Ready for QA
Jira-> QA   : Auto‑assign ticket
QA  -> Repo : Checkout feature branch\n+ create test branch
QA  -> CI   : Implement & run automation tests
CI  -> QA   : Test results (green)
QA  -> Repo : Merge tests into PR\nApprove merge to develop
Repo-> CI   : Post‑merge build & regression
CI  --> Jira: Update status / close ticket
@enduml
```

### 2. Reusable Component Extension

```plantuml
@startuml
actor  "QA Engineer" as QE
database "Platform-Tests Repo\n(shared utilities)" as PT
database "Project Repo\n(Heart+, Platform, TMP)"   as Repo
participant "CI/CD Pipeline" as CI

QE -> PT : Create new testing utility
PT -> CI : Unit & integration tests
CI -> QE : Utility build passes
QE -> PT : Merge to main & tag release (e.g., v1.2.0)
QE -> Repo: Add dependency on new PT release
QE -> CI  : Re‑run ticket tests
CI -> QE  : Test results (green)
QE -> Repo: Merge ticket tests & dependency update
@enduml
```

---

## Roles & Responsibilities

| Role               | Responsibility                                            |
| ------------------ | --------------------------------------------------------- |
| **Developer**      | Implement feature / bug‑fix and open PR                   |
| **Tech Lead**      | Review code, transition ticket, enforce standards         |
| **QA Engineer**    | Design & execute automated tests; extend reusable toolkit |
| **CI/CD Pipeline** | Execute builds, test suites, and report status            |

---

## Alternative / Failure Paths

* **Tests fail** → QA comments on PR → Ticket returns to **In Progress**; Developer fixes defects; cycle repeats.
* **Reusable component build fails** → QA fixes tests in `platform-tests` before tagging release.

---

## Version History

| Date (YYYY‑MM‑DD) | Version | Notes         |
| ----------------- | ------- | ------------- |
| 2025‑05‑09        |  1.0    | Initial draft |
