---
id: testing-utilities-architecture
title: Architecture & Implementation
sidebar_label: Testing Utilities Architecture
---

# Testing Utilities Architecture & Implementation

This section provides a detailed breakdown of the components that form the SDK system, how they are organized, and how they interact with each other.

## Core Components

### Brick Configuration
- **File:** `brick_config.go`
- **Description:**  
  The `BrickConfig` struct defines the configurable parameters for a container brick. It includes fields for:
  - **BrickType:** The type or category of the container (e.g., `"postgres"`, `"gin-go"`, etc.).  
  - **InstanceName:** A unique name to identify the container instance.
  - **CommandArguments & EnvironmentVariables:** Define the runtime command and environment variables to customize container behavior.
  - **ExposedPort & HostPort:** Specify ports for container exposure and host mapping.
  - **Volumes & HealthCheck:** Setup volume bindings and health-check commands with customizable intervals, retries, and timeouts.
  - **Networks & NetworkAliases:** Configure network settings for container communications.
  - **CustomWaitStrategy:** Optionally override the default waiting strategy.

### Container Brick Interface and Implementation
- **Interface:**  
  - **File:** `container_brick.go`  
    The `ContainerBrick` interface declares two essential methods: `Start(context.Context) error` and `Stop(context.Context) error`.
- **Implementation:**  
  - **File:** `generic_container_brick.go`  
    This file implements `ContainerBrick` in the `GenericContainerBrick` type, which:
    - Uses the Docker image mappings determined by `getImageForBrickType`.
    - Constructs a container request using the testcontainers API.
    - Applies host configuration modifications (via the provided modifier functions) for port bindings and volume mounts.
    - Supports a dynamic waiting strategy to ensure the container is ready before proceeding.

### Brick Factory
- **File:** `container_brick_factory.go`
- **Description:**  
  Provides a factory method (`NewContainerBrick`) to instantiate a new container brick using a given `BrickConfig`. This encapsulates the creation logic and ensures that the proper implementation (in this case, `GenericContainerBrick`) is used based on the configuration.

### Infrastructure Manager
- **File:** `infrastructure_manager.go`
- **Description:**  
  The `InfrastructureManager` handles the collective management of multiple container bricks. It offers:
  - **StartAll:** Sequentially starts all bricks defined in the configuration.
  - **StopAll:** Terminates all running bricks.
  
  This central management component simplifies the orchestration of multi-container environments in integration tests or development setups.

### Network Management Utility
- **File:** `network.go`
- **Description:**  
  This utility file provides a helper function `CreateNetwork` to set up a Docker network. It ensures that all container bricks can communicate over a predefined network (for example, `"platform-tests-bridge"`), which is essential in multi-container integrations.

## Integration Testing

### Integration Test Example
- **File:** `int_test.go`
- **Description:**  
  The integration test demonstrates how to utilize the SDK:
  - **Setup:**  
    - Absolute file path computations for SQL initialization files and component directories.
    - Creation of a Docker network.
  - **Container Configuration:**  
    - Defines configurations for various services such as PostgreSQL, the Gin-based application, and ancillary services like Dapr and k6.
    - Utilizes helper functions (e.g., `mergeWithExtraBinding`) to merge additional Docker host configurations.
  - **Lifecycle Management:**  
    - Sequentially starts containers with delays.
    - Monitors container status using a Docker client.
    - Gracefully terminates all containers after the tests complete.

The integration test serves as a practical example of how each module interacts to create, manage, and terminate containerized environments.

