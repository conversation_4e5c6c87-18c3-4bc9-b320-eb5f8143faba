# ADR: Reporting Service Integration

**Date:** 21.05.2025  
**Status:** Proposed

## Context

`platform-tests` is a centralized toolkit for developing and customizing testing tools across various repositories. It provides SDKs, CLI tools, and services for building reliable test infrastructures that execute unit, integration, end-to-end and performance test suites for several micro-service repositories and collects SonarQube quality-gate metrics on every CI build.

Currently, test results are scattered across JUnit XML, Allure JSON/HTML, k6 summary files and the SonarQube web UI. Engineers developing and supporting these testing tools must consult three to four different systems to assess the effectiveness and quality of the testing infrastructure.

We therefore need a single, queryable backend that stores all quality signals and a Manhattan Project-based UI that exposes live and historical trends as KPIs, enabling testing tool developers to make data-driven improvements to the platform and compare quality metrics across projects.

## Decision Drivers

- **Single Source of Truth** for automated test results and static-analysis metrics from tools developed in this repository.  
- **Time-series & high-cardinality support** (repo, service, branch, commit, pipeline) to track testing tool effectiveness.  
- **Self-hosted, no new SaaS** to comply with security policy and cost cap (€100 / year).  
- **Push-based ingestion** from CI plus scheduled **pull** from SonarQube Web API.  
- **Low total cost of ownership**: one extra micro-service on our Kubernetes cluster.  
- **Seamless UI integration** with The Manhattan Project, enabling comparison of quality KPIs across projects.  
- **Extensible schema** so that new test types or metrics can be added with minimal effort as the testing tools evolve.

## Considered Options

- **Custom reporting-service (Go + ClickHouse)** – the "build" option  
- **ReportPortal**  
- **Allure Report hosted in S3 / MinIO**  
- **Grafana Cloud / k6 Cloud**  
- **Do nothing** (keep current fragmented state)

## Pros and Cons of Each Option

### Custom reporting-service (Go + ClickHouse)

**Pros:**
- Tailored schema joins test pass-rate, coverage %, latency P95, etc. in one query  
- Re-uses existing Go libraries and deployment tooling  
- ClickHouse provides excellent columnar compression & blazing-fast analytics queries
- Optimal for time-series and high-cardinality data (outperforms TimescaleDB)
- No licence fees; infra footprint < 200 MiB RAM, single replica  
- Full control over auth (GitHub OIDC) and data retention
- Seamless integration with The Manhattan Project for KPI visualization

**Cons:**
- Custom code to build and maintain (≈ 1 developer-month initial, 1 day/month upkeep)  
- Less SQL compatibility than PostgreSQL/TimescaleDB (learning curve for team)
- Requires writing connectors for The Manhattan Project dashboards

### ReportPortal

**Pros:**
- Rich out-of-the-box dashboards and trend analysis  
- Broad community adoption in QA space
- Can integrate with The Manhattan Project via webhooks

**Cons:**
- Heavy stack (7+ containers incl. Elasticsearch)  
- Overlaps with existing Grafana; introduces Elasticsearch operational overhead  
- Road-map is moving toward paid SaaS tier → potential vendor lock-in  
- Still lacks first-class SonarQube integration

### Allure Report + S3

**Pros:**
- Familiar HTML output, attractive test history UI  
- Lightweight to generate

**Cons:**
- Static only – no live updates, no API  
- Each repository builds its own history; merging multi-repo data is manual  
- No native SonarQube overlay; would still require a second tool
- Cannot integrate with The Manhattan Project KPI system

### Grafana Cloud / k6 Cloud

**Pros:**
- Excellent real-time graphs for k6 metrics  
- Managed service – no infra to run

**Cons:**
- Violates "no new paid SaaS" constraint  
- Company policy forbids PII in external clouds  
- Focused on performance metrics; test pass/fail and coverage would need adapters
- Cannot integrate with The Manhattan Project KPI system

### Do Nothing

**Pros:**
- Zero implementation work

**Cons:**
- Engineers keep context-switching between disparate tools  
- Cannot introduce trend-based quality gates  
- Fails roadmap objective of a unified dashboard
- No integration with The Manhattan Project KPI system

## Comparison Table

| Capability | Custom Reporting | ReportPortal | Allure + S3 | Grafana/k6 Cloud |
|------------|-----------------|--------------|-------------|------------------|
| **Cost** | €100/year infra | Medium (extra 7 containers) | Low (static hosting) | Subscription per MAU |
| **Self-hosting** | ✔ | ✔ | ✔ | ✖ |
| **SonarQube Integration** | Native pull job | Indirect (plugins) | ❌ | ❌ |
| **Multi-suite Support** | Unit, IT, E2E, Perf | Primarily functional | Functional | Perf only |
| **Real-time Queries** | ✔ (REST/GraphQL) | ✔ | ❌ | ✔ |
| **Setup Complexity** | Moderate (Go + ClickHouse) | High | Low | Low |
| **Vendor Lock-in** | None | Medium | None | High |
| **Manhattan Integration** | ✔ (via webhooks) | ✔ (via webhooks) | ❌ | ❌ |
| **Webhook Support** | ✔ | ✔ | ❌ | ✔ |

## Decision

After evaluating the options, **building a lightweight Go reporting-service backed by ClickHouse** is selected.  
It satisfies all decision drivers while keeping operational and licensing costs minimal, and provides a foundation for continuous improvement of the testing tools developed in this repository with seamless integration into The Manhattan Project for KPI tracking.

## Rationale

- Provides a **single, extensible schema** that unifies functional, performance and static-analysis data generated by our testing tools.  
- Leverages **existing cluster resources and Go expertise**, minimizing tooling sprawl.  
- ClickHouse offers **superior analytical query performance** for time-series data with high cardinality.  
- Enables **trend-based gates** and alerting directly from the same data store.  
- The Manhattan Project integration allows quality KPIs to be tracked alongside other project metrics.
- Supports **testing tool effectiveness analysis** to guide future development priorities.
- Enables **cross-project quality comparison** through The Manhattan Project's dashboards.

## Consequences

- **Positive:**  
  - One URL in The Manhattan Project where testing tool developers can view live & historical quality metrics to improve their tools.  
  - Ability to fail a release automatically when coverage or pass-rate regress beyond agreed thresholds.  
  - Unified API simplifies further integrations (e.g., Slack alerts, ML-driven flakiness detection).  
  - Enables data-driven decisions for testing tool enhancement and prioritization.
  - Quality KPIs can be compared across projects within The Manhattan Project.

- **Negative / Risks:**  
  - Additional service to maintain; will require on-call ownership.  
  - Team needs to build ClickHouse expertise (different from PostgreSQL).
  - Additional integration work with The Manhattan Project required.
