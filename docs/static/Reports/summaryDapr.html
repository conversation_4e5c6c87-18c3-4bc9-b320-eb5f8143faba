<!DOCTYPE html>
<html lang="en">
  <head> 
    <meta charset="UTF-8" />
    <link rel="stylesheet" href="https://unpkg.com/purecss@2.0.3/build/pure-min.css" crossorigin="anonymous">

    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.2/css/all.css" crossorigin="anonymous">

    <link rel="shortcut icon" href="https://raw.githubusercontent.com/benc-uk/k6-reporter/main/assets/icon.png" type="image/png">

    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>K6 Load Test: 2025-01-29 13:09</title>
    <style>
      body {
        margin: 1rem;
      }
      footer { 
        float: right;
        font-size: 0.8rem;
        color: #777;
      }
      footer a {
        text-decoration: none;
        color: #777;
      }
      .failed {
        background-color: #ff6666 !important;
      }     
      .good {
        background-color: #3abe3a !important;
      }   
      td.failed {
        font-weight: bold;
      }
      h2 {
        padding-bottom: 4px;
        border-bottom: solid 3px #cccccc;
      }
      .tabs {
        display: flex;
        flex-wrap: wrap; 
      }
      .tabs label {
        order: 1; 
        display: block;
        padding: 1rem 2rem;
        margin-right: 0.2rem;
        cursor: pointer;
        color: #666;
        background: #ddd;
        font-weight: bold;
        font-size: 1.2rem;
        flex: 1 1;
        transition: background ease 0.2s;
        border-top-left-radius: 0.3rem;
        border-top-right-radius: 0.3rem;
        border-color: #ccc;
        border-style: solid;
        border-width: 2px 2px 0px;
        box-shadow: inset 0px -3px 7px -1px rgba(0,0,0,0.33);
      }
      .tabs .tab {
        order: 99;
        flex-grow: 1;
        width: 100%;
        display: none;
        padding: 1rem;
        background: #fff;
      }
      .tabs input[type="radio"] {
        display: none;
      }
      .tabs input[type="radio"]:checked + label {
        background: #fff;
        box-shadow: none;
        color: #000;
      }
      .tabs input[type="radio"]:checked + label + .tab {
        display: block;
      }
      .box {
        flex: 1 1;
        border-radius: 0.3rem;
        background-color: #3abe3a;
        margin: 1rem;
        padding: 0.5rem;
        font-size: 2vw; 
        box-shadow: 0px 4px 7px -1px rgba(0,0,0,0.49);
        color: white;
        position: relative;
        overflow: hidden;
      }
      .box h4 {
        margin: 0;
        padding-bottom: 0.5rem;
        text-align: center;
        position: relative;
        z-index: 50;
      }
      .row {
        display: flex;
      }
      .row div {
        flex: 1 1;
        text-align: center;
        margin-bottom: 0.5rem;
      }
      .bignum {
        position: relative;
        font-size: min(6vw, 80px);
        z-index: 20;
      }
      table {
        font-size: min(2vw, 22px);
        width: 100%;
      }
      .icon { 
        position: absolute;
        top: 60%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #0000002d;
        font-size: 8vw;
        z-index: 1;
      }
      .metricbox {
        background-color: #5697e2;
        font-size: 3vw;
        height: auto;
      }
      .metricbox .row {
        position: relative;
        z-index: 20;
      }
    </style>
  </head>

  <body>
    <h1>
      <svg style="vertical-align:middle" width="50" height="45" viewBox="0 0 50 45" fill="none" class="footer-module--logo--_lkxx"><path d="M31.968 34.681a2.007 2.007 0 002.011-2.003c0-1.106-.9-2.003-2.011-2.003a2.007 2.007 0 00-2.012 2.003c0 1.106.9 2.003 2.012 2.003z" fill="#7D64FF"></path><path d="M39.575 0L27.154 16.883 16.729 9.31 0 45h50L39.575 0zM23.663 37.17l-2.97-4.072v4.072h-2.751V22.038l2.75 1.989v7.66l3.659-5.014 2.086 1.51-3.071 4.21 3.486 4.776h-3.189v.001zm8.305.17c-2.586 0-4.681-2.088-4.681-4.662 0-1.025.332-1.972.896-2.743l4.695-6.435 2.086 1.51-2.239 3.07a4.667 4.667 0 013.924 4.6c0 2.572-2.095 4.66-4.681 4.66z" fill="#7D64FF"></path></svg> 
      &nbsp; K6 Load Test: 2025-01-29 13:09
    </h1>

    <div class="row">
      <div class="box">
        <i class="fas fa-globe icon"></i>
        <h4>Total Requests</h4>
        <div class="bignum">265753</div>
        <div class="bignum"></div>
      </div>
      
        <div class="box  failed ">
          <i class="far fa-times-circle icon"></i>
          <h4>Failed Requests</h4>
          <div class="bignum">58</div>
        </div> 
           
      <div class="box ">
        <i class="fas fa-chart-bar icon"></i>
        <h4>Breached Thresholds</h4>
        <div class="bignum">0</div>
      </div>
      <div class="box  failed ">
        <i class="fas fa-eye icon"></i>
        <h4>Failed Checks</h4>
        <div class="bignum">58</div>
      </div>
    </div>

    <br>
    
    <div class="tabs">
      <input type="radio" name="tabs" id="tabone" checked="checked">
      <label for="tabone"><i class="far fa-clock"></i> &nbsp; Request Metrics</label>
      <div class="tab">
        <table class="pure-table pure-table-striped">
          <tbody>
            <thead>
              <tr>
                <th></th>
                <th>Count</th>
                <th>Rate</th>
                <th>Average</th>
                <th>Maximum</th>
                <th>Median</th> 
                <th>Minimum</th>
                <th>90th Percentile</th>
                <th>95th Percentile</th>
              </tr>
            </thead>
            
            
              <tr>
                <td><b>http_req_duration</b></td>

                
                  <td>-</td>
                

                
                  <td>-</td>
                
                
                
                  <td class="">15.87</td>
                

                
                  <td class="">864.73</td>
                  

                
                  <td class="">4.99</td>
                  
                
                
                  <td class="">0.43</td>
                   
                              
                
                  <td class="">42.62</td>
                

                
                  <td class="">75.47</td>
                
              </tr>
            
              <tr>
                <td><b>http_req_waiting</b></td>

                
                  <td>-</td>
                

                
                  <td>-</td>
                
                
                
                  <td class="">15.70</td>
                

                
                  <td class="">422.32</td>
                  

                
                  <td class="">4.93</td>
                  
                
                
                  <td class="">0.42</td>
                   
                              
                
                  <td class="">42.38</td>
                

                
                  <td class="">75.00</td>
                
              </tr>
            
              <tr>
                <td><b>http_req_connecting</b></td>

                
                  <td>-</td>
                

                
                  <td>-</td>
                
                
                
                  <td class="">0.01</td>
                

                
                  <td class="">104.67</td>
                  

                
                  <td>-</td>
                  
                
                
                  <td>-</td>
                   
                              
                
                  <td>-</td>
                

                
                  <td>-</td> 
                
              </tr>
            
              <tr>
                <td><b>http_req_tls_handshaking</b></td>

                
                  <td>-</td>
                

                
                  <td>-</td>
                
                
                
                  <td>-</td>
                

                
                  <td>-</td>
                  

                
                  <td>-</td>
                  
                
                
                  <td>-</td>
                   
                              
                
                  <td>-</td>
                

                
                  <td>-</td> 
                
              </tr>
            
              <tr>
                <td><b>http_req_sending</b></td>

                
                  <td>-</td>
                

                
                  <td>-</td>
                
                
                
                  <td class="">0.11</td>
                

                
                  <td class="">461.39</td>
                  

                
                  <td class="">0.01</td>
                  
                
                
                  <td class="">0.00</td>
                   
                              
                
                  <td class="">0.04</td>
                

                
                  <td class="">0.11</td>
                
              </tr>
            
              <tr>
                <td><b>http_req_receiving</b></td>

                
                  <td>-</td>
                

                
                  <td>-</td>
                
                
                
                  <td class="">0.07</td>
                

                
                  <td class="">781.70</td>
                  

                
                  <td class="">0.02</td>
                  
                
                
                  <td class="">0.00</td>
                   
                              
                
                  <td class="">0.06</td>
                

                
                  <td class="">0.10</td>
                
              </tr>
            
              <tr>
                <td><b>http_req_blocked</b></td>

                
                  <td>-</td>
                

                
                  <td>-</td>
                
                
                
                  <td class="">0.03</td>
                

                
                  <td class="">200.78</td>
                  

                
                  <td class="">0.00</td>
                  
                
                
                  <td class="">0.00</td>
                   
                              
                
                  <td class="">0.01</td>
                

                
                  <td class="">0.02</td>
                
              </tr>
            
              <tr>
                <td><b>iteration_duration</b></td>

                
                  <td>-</td>
                

                
                  <td>-</td>
                
                
                
                  <td class="">1019.63</td>
                

                
                  <td class="">1865.57</td>
                  

                
                  <td class="">1006.92</td>
                  
                
                
                  <td class="">1000.53</td>
                   
                              
                
                  <td class="">1052.64</td>
                

                
                  <td class="">1085.16</td>
                
              </tr>
            
          </tbody>
        </table>
        <br>

        
            </tbody>
          </table>
          <br>


        &nbsp;&nbsp; Note. All times are in milli-seconds
      </div> 
      <!-- ---- end tab ---- -->

      <input type="radio" name="tabs" id="tabtwo">
      <label for="tabtwo"><i class="fas fa-chart-line"></i> &nbsp; Other Stats</label>
      <div class="tab">
        <div class="row">
          
            <div class="box metricbox">
              <h4>Checks</h4>
              <i class="fas fa-eye icon"></i>
              <div class="row"><div>Passed</div><div>265695</div></div>
              <div class="row"><div>Failed</div><div>58</div></div>
            </div>
          

          
            <div class="box metricbox">
              <h4>Iterations</h4>
              <i class="fas fa-redo icon"></i>
              <div class="row"><div>Total</div><div>265753</div></div>
              <div class="row"><div>Rate</div><div>1103.09/s</div></div>
            </div>
          

          <div class="box metricbox">
            <h4>Virtual Users</h4>
            <i class="fas fa-user icon"></i>
            <div class="row"><div>Min</div><div>2</div></div>
            <div class="row"><div>Max</div><div>2000</div></div>
          </div>
        </div>

        <div class="row">
          <div class="box metricbox">
            <h4>Requests</h4>
            <i class="fas fa-globe icon"></i>
            <div class="row"><div>Total</div><div>265753</div></div>
            <div class="row"><div>Rate</div><div>1103.09/s</div></div>
          </div>

          <div class="box metricbox">
            <h4>Data Received</h4>
            <i class="fas fa-cloud-download-alt icon"></i>
            <div class="row"><div>Total</div><div>70.70 MB</div></div>
            <div class="row"><div>Rate</div><div>0.29 mB/s</div></div>
          </div>

          <div class="box metricbox">
            <h4>Data Sent</h4>
            <i class="fas fa-cloud-upload-alt icon"></i>
            <div class="row"><div>Total</div><div>87.70 MB</div></div>
            <div class="row"><div>Rate</div><div>0.36 mB/s</div></div>
          </div>   
        </div>
      </div>  
      <!-- ---- end tab ---- -->     

      <input type="radio" name="tabs" id="tabthree">
      <label for="tabthree"><i class="fas fa-tasks"></i> Checks & Groups</label>
      <div class="tab">

        

        <h2>&bull; Other Checks</h2>
        <table class="pure-table pure-table-horizontal" style="width: 100%">
          <thead>
            <tr>
              <th>Check Name</th>
              <th>Passes</th>
              <th>Failures</th>
            </tr>
          </thead>
          
            <tr class="checkDetails failed">
              <td width="50%">status for request 1 was 200</td><td>265695</td><td>58</td>
            </tr>
          
        </table>     
      </div> 
      <!-- ---- end tab ---- -->
    </div>
    <footer>K6 Reporter v2.3.0 - Ben Coleman 2021, <a href="https://github.com/benc-uk/k6-reporter">[GitHub]</a></footer>
  </body>
</html>
