export const goPaths = {
    tool1: () => import('!!raw-loader!../../docs/Architecture/ADRs/UnitTests/Go/ADR_UnitTesting_Tool_Go.md'),
    tool2: () => import('!!raw-loader!../../docs/Architecture/ADRs/UnitTests/Go/ADR_Mocking_Framework_Go.md'),
    tool3: () => import('!!raw-loader!../../docs/Architecture/ADRs/UnitTests/Go/ADR_AssertionLibrary_Go.md'),
    tool4: () => import('!!raw-loader!/docs/TestUtilities/Go/running-tests.md'),
    tool5: () => import('!!raw-loader!/docs/TestUtilities/Go/supported-images.md'),
  };
  
  export const pythonPaths = {
    tool1: () => import('!!raw-loader!../../docs/Architecture/ADRs/UnitTests/Python/ADR_UnitTesting_Tool_Python.md'),
    tool2: () => import('!!raw-loader!../../docs/Architecture/ADRs/UnitTests/Python/ADR_Mocking_Framework_Python.md'),
    tool3: () => import('!!raw-loader!../../docs/Architecture/ADRs/UnitTests/Python/ADR_AssertionLibrary_Python.md'),
    tool4: () => import('!!raw-loader!/docs/TestUtilities/Python/running-tests.md'),
    tool5: () => import('!!raw-loader!/docs/TestUtilities/Python/supported-images-python.md'),
  };
  
  export const typescriptPaths = {
    tool1: () => import('!!raw-loader!../../docs/Architecture/ADRs/UnitTests/Typescript/ADR_UnitTesting_Tool_Ts.md'),
    tool2: () => import('!!raw-loader!../../docs/Architecture/ADRs/UnitTests/Typescript/ADR_Mocking_Framework_Ts.md'),
    tool3: () => import('!!raw-loader!../../docs/Architecture/ADRs/UnitTests/Typescript/ADR_AssertionLibrary_Ts.md'),
    tool4: () => import('!!raw-loader!/docs/TestUtilities/Typescript/running-tests.md'),
    tool5: () => import('!!raw-loader!/docs/TestUtilities/Typescript/supported-images-ts.md')
  };

  export const javaPaths = {
    tool1: () => import('!!raw-loader!../../docs/Architecture/ADRs/UnitTests/Java/ADR_UnitTesting_Tools_Java.md'),
    tool2: () => import('!!raw-loader!../../docs/Architecture/ADRs/UnitTests/Java/ADR_Mocking_Framework_Java.md'),
    tool3: () => import('!!raw-loader!../../docs/Architecture/ADRs/UnitTests/Java/ADR_AssertionLibrary_Java.md'),
    tool4: () => import('!!raw-loader!/docs/TestUtilities/Java/running-tests.md'),
    tool5: () => import('!!raw-loader!/docs/TestUtilities/Java/supported-images-java.md'),
  };
  
  export const integrationPaths = {
    int1: () => import('!!raw-loader!/docs/Setup/IntegrationTests/Go_Integration_Tests.md'),
    int2: () => import('!!raw-loader!/docs/Setup/IntegrationTests/Python_Integration_Tests.md'),
    int3: () => import('!!raw-loader!/docs/Setup/IntegrationTests/TypeScript_Integration_Tests.md'),
    int4: () => import('!!raw-loader!/docs/Setup/IntegrationTests/Java_Integration_Tests.md'),
  };


  export const backendIntegrationPaths = {
    int1: () => import('!!raw-loader!/docs/Tests/guides/backend-integration-testing-guide.md'),
    int2: () => import('!!raw-loader!/docs/Tests/guides/backend-int-python.md'),
    int3: () => import('!!raw-loader!/docs/Tests/guides/backend-integration-testing-guide-typescript.md'),
    int4: () => import('!!raw-loader!/docs/Tests/guides/backend-integration-testing-guide-java.md'),
  };

  export const guidelinesPaths = {
    int1: () => import('!!raw-loader!/docs/Tests/UnitTests/utingo.md'),
    int2: () => import('!!raw-loader!/docs/Tests/UnitTests/utinpython.md'),
    int3: () => import('!!raw-loader!/docs/Tests/UnitTests/utints.md'),
    int4: () => import('!!raw-loader!/docs/Tests/UnitTests/utinjava.md'),

  };