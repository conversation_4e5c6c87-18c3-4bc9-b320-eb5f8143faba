import React from 'react';
import Layout from '@theme/Layout';
import Link from '@docusaurus/Link';
import styles from './index.module.css';

export default function Home() {
  return (
    <Layout
      title="Testing Documentation"
      description="Comprehensive guide to setup, architecture, services, utilities, and test practices">
      <main className={styles.main}>
        <div className={styles.header}>
          <div className={styles.headerContent}>
            <h1>Welcome to the Testing Documentation</h1>
            <p>Your one-stop reference for setup, architecture, and testing best practices.</p>
            <Link className={`button button--primary button--lg ${styles.pulseButton}`} to="/docs/intro">
              🚀 Get Started
            </Link>
          </div>
          <div className={styles.headerBackground}></div>
        </div>

        <div className={styles.stats}>
          <div className={styles.statItem}>
            <h3>4</h3>
            <p>Testing Types</p>
          </div>
          <div className={styles.statItem}>
            <h3>10+</h3>
            <p>Tools & Frameworks</p>
          </div>
          <div className={styles.statItem}>
            <h3>100%</h3>
            <p>Docker Support</p>
          </div>
          <div className={styles.statItem}>
            <h3>24/7</h3>
            <p>CI/CD Integration</p>
          </div>
        </div>

        <div className={styles.sections}>
          <div className={styles.row}>
            <Section 
              icon="🔧"
              title="Setup" 
              link="/docs/Setup/setup-guide" 
              description="Environment setup, integration, performance, and E2E testing setup guides." 
            />
            <Section 
              icon="🏛️"
              title="Architecture" 
              link="/docs/Architecture/project-architecture" 
              description="Learn how the project is structured and explore our testing architecture and ADRs." 
            />
            <Section 
              icon="📊"
              title="Reports" 
              link="/docs/Reports/Perfromance/test-reports" 
              description="Browse performance test reports and metrics." 
            />
          </div>
          <div className={styles.row}>
            <Section 
              icon="🧩"
              title="Services" 
              link="/docs/Services/api-gateway" 
              description="Explore core services like the API Gateway and Fibonacci service." 
            />
            <Section 
              icon="🛠️"
              title="Test Utilities" 
              link="/docs/TestUtilities/introduction" 
              description="Tools and language bindings for testing in Go, Python, TypeScript, and Java." 
            />
            <Section 
              icon="📘"
              title="Guidelines" 
              link="/docs/Tests/UnitTests/" 
              description="Best practices and guides for writing and running tests." 
            />
          </div>
        </div>

        <div className={styles.steps}>
          <h2>Quick Start Guide</h2>
          <div className={styles.stepGrid}>
            <div className={styles.step}>
              <div className={styles.stepNumber}>1</div>
              <h3>Setup Environment</h3>
              <p>Install Docker and required dependencies</p>
              <Link to="/docs/Setup/setup-guide">View Setup Guide →</Link>
            </div>
            <div className={styles.step}>
              <div className={styles.stepNumber}>2</div>
              <h3>Choose Framework</h3>
              <p>Select testing tools for your needs</p>
              <Link to="/docs/Architecture/project-architecture">View Frameworks →</Link>
            </div>
            <div className={styles.step}>
              <div className={styles.stepNumber}>3</div>
              <h3>Run Tests</h3>
              <p>Execute your first test suite</p>
              <Link to="/docs/TestUtilities/Go">Start Testing →</Link>
            </div>
          </div>
        </div>

        <div className={styles.faqSection}>
          <h2>🛠️ Troubleshooting FAQ</h2>
          <div className={styles.faqGrid}>
            <div className={styles.faqItem}>
              <h3>🐳 Docker and Container Issues</h3>
              <p>Resolve Docker network conflicts, socket access problems, port conflicts, and container startup failures.</p>
              <Link to="/docs/Troubleshooting/troubleshooting-guide#-docker-and-container-issues">View Solutions →</Link>
            </div>
            <div className={styles.faqItem}>
              <h3>🗃️ Database Issues</h3>
              <p>Fix database connection failures and initialization script problems.</p>
              <Link to="/docs/Troubleshooting/troubleshooting-guide#️-database-issues">View Solutions →</Link>
            </div>
            <div className={styles.faqItem}>
              <h3>🚀 Dapr Issues</h3>
              <p>Troubleshoot sidecar connection failures and component initialization issues.</p>
              <Link to="/docs/Troubleshooting/troubleshooting-guide#-dapr-issues">View Solutions →</Link>
            </div>
            <div className={styles.faqItem}>
              <h3>📈 Performance Testing Issues</h3>
              <p>Resolve K6 test failures and missing test reports problems.</p>
              <Link to="/docs/Troubleshooting/troubleshooting-guide#-performance-testing-issues">View Solutions →</Link>
            </div>
            <div className={styles.faqItem}>
              <h3>🧪 Integration Test Issues</h3>
              <p>Fix Python environment and module-related problems in integration tests.</p>
              <Link to="/docs/Troubleshooting/troubleshooting-guide#-integration-test-issues">View Solutions →</Link>
            </div>
            <div className={styles.faqItem}>
              <h3>💻 Shell Script Issues</h3>
              <p>Handle permission denied errors and script execution problems.</p>
              <Link to="/docs/Troubleshooting/troubleshooting-guide#-shell-script-issues">View Solutions →</Link>
            </div>
            <div className={styles.faqItem}>
              <h3>🌍 Environment Variables</h3>
              <p>Fix missing or misconfigured environment variables.</p>
              <Link to="/docs/Troubleshooting/troubleshooting-guide#-environment-variables">View Solutions →</Link>
            </div>
          </div>
        </div>

        <div className={styles.cta}>
          <h2>Ready to Start Testing?</h2>
          <p>Get started with our comprehensive testing suite today</p>
          <div className={styles.ctaButtons}>
            <Link className="button button--primary button--lg" to="/docs/intro">
              Read Documentation
            </Link>
            <Link className="button button--secondary button--lg" to="https://github.com/Matrics-io/platform-tests">
              View on GitHub
            </Link>
          </div>
        </div>
      </main>
    </Layout>
  );
}

function Section({ icon, title, description, link }) {
  return (
    <div className={styles.section}>
      <div className={styles.sectionContent}>
        <div className={styles.sectionIcon}>{icon}</div>
        <h2>{title}</h2>
        <p>{description}</p>
      </div>
      <div className={styles.sectionFooter}>
        <Link to={link} className={`button button--secondary button--sm ${styles.readMore}`}>
          Read More
          <span className={styles.arrow}>→</span>
        </Link>
      </div>
    </div>
  );
}
