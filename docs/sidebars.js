// @ts-check

/** @type {import('@docusaurus/plugin-content-docs').SidebarsConfig} */
const sidebars = {
  tutorialSidebar: [
        'intro',
        {
          type: 'category',
          label: 'Setup',
          items: [
            'Setup/setup-guide',
            'Setup/environment-variables',
            {
              type: 'category',
              label: 'Testing Setup',
              items: [
                'Setup/integration-testing-setup',
                'Setup/performance-testing-setup',
                'Setup/e2e-testing-setup',
              ],
            }
          ],
        },
        {
          type: 'category',
          label: 'Architecture',
          items: [
            'Architecture/project-architecture',
            'Architecture/project-structure',
            'Architecture/testing-utilities-architecture',
            'Architecture/qa-workflow',
            {
              type: 'category',
              label: 'ADRs',
              items: [
                'Architecture/ADRs/Adrs',
                'Architecture/ADRs/ADR_Reporting_Service',
                {
                  type: 'category',
                  label: 'Unit Tests',
                  items: [
                    {
                      type: 'doc',
                      id: 'Architecture/ADRs/UnitTests/Go',
                      label: 'Go',
                    },
                    {
                      type: 'doc',
                      id: 'Architecture/ADRs/UnitTests/Python',
                      label: 'Python',
                    },
                    {
                      type: 'doc',
                      id: 'Architecture/ADRs/UnitTests/Typescript',
                      label: 'TypeScript',
                    },
                    {
                      type: 'doc',
                      id: 'Architecture/ADRs/UnitTests/Java',
                      label: 'Java',
                    }
                  ],
                },
                {
                  type: 'category',
                  label: 'E2E Tests',
                  items: [
                    'Architecture/ADRs/E2ETests/ADR_E2E_Testing_Tool',
                  ],
                },
                {
                  type: 'category',
                  label: 'Performance Tests',
                  items: [
                    'Architecture/ADRs/PerformanceTests/ADR_PerformanceTool',
                  ],
                },
              ],
            },

          ],
        },
        {
          type: 'category',
          label: 'Reports',
          items: [
            {
              type: 'category',
              label: 'Performance Reports',
              items: [
                'Reports/Perfromance/test-reports',
              ],
            },
          ],
        },
    {
      type: 'category',
      label: 'Test Utilities',
      items: [
        'TestUtilities/introduction',
        'TestUtilities/brick-config',
        'TestUtilities/wiremock',
        {
          type: 'category',
          label: 'Unit Testing Tools',
          items: [
            'TestUtilities/UnitTestingTools/test-utilities-overview',
            'TestUtilities/UnitTestingTools/platform-tests-cli',
            {
              type: 'category',
              label: 'SDK Unit Test Utilities',
              items: [
                {
                  type: 'doc',
                  id: 'TestUtilities/UnitTestingTools/sdk-unit-tests-go',
                  label: 'Go SDK',
                },
                {
                  type: 'doc',
                  id: 'TestUtilities/UnitTestingTools/sdk-unit-tests-python',
                  label: 'Python SDK',
                },
                {
                  type: 'doc',
                  id: 'TestUtilities/UnitTestingTools/sdk-unit-tests-typescript',
                  label: 'TypeScript SDK',
                },
                {
                  type: 'doc',
                  id: 'TestUtilities/UnitTestingTools/sdk-unit-tests-java',
                  label: 'Java SDK',
                },
              ],
            },
          ],
        },
        {
          type: 'category',
          label: 'Language Bindings',
          items: [
            {
              type: 'doc',
              id: 'TestUtilities/Go',
              label: 'Go',
            },
            {
              type: 'doc',
              id: 'TestUtilities/Python',
              label: 'Python',
            },
            {
              type: 'doc',
              id: 'TestUtilities/Typescript',
              label: 'TypeScript',
            },
            {
              type: 'doc',
              id: 'TestUtilities/Java',
              label: 'Java',
            },
          ],
        },
        {
          type: 'category',
          label: 'Playwright',
          items: [
            'TestUtilities/Typescript/playwrite',
          ],
        },
        {
          type: 'category',
          label: 'Services',
          items: [
            'Services/api-gateway',
            'Services/fibonacci-service',
          ],
        },
        'TestUtilities/k6-utilities',
      ],
    },
    {
      type: 'category',
      label: 'Test Guidelines',
      items: [
        'Tests/UnitTests/unittests',
        'Tests/backendTestsGuide',
        'Tests/performanceTestsGuide',
        'Tests/end2endTestsGuide',
      ],
    },
    {
      type: 'category',
      label: 'FAQ',
      items: [
        'Troubleshooting/troubleshooting-guide',
        'workflow',
      ],
    },
  ],
};

export default sidebars;
